#!/system/bin/sh
case $1 in
    ppp1)
	echo 0 > /proc/sys/net/ipv4/ip_forward;
	;;
esac

# Use interface name if linkname is not available
NAME=${LINKNAME:-"$1"}

/system/bin/setprop "net.$NAME.dns1" "$DNS1"
/system/bin/setprop "net.$NAME.dns2" "$DNS2" 
/system/bin/setprop "net.$NAME.local-ip" "$IPLOCAL" 
/system/bin/setprop "net.$NAME.remote-ip" "$IPREMOTE" 
/system/bin/setprop "net.$NAME.gw" "$IPREMOTE"
