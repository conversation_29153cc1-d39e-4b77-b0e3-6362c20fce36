#!/system/bin/sh

#get chip
hardware=`getprop ro.hardware "unknown" | busybox tr -d " "`
build_product=`getprop ro.build.product "unknown" | busybox tr -d " "`

#get uuid
BLK_DEV=UNKNOWN
UUID=UNKNOWN
if [[ "$hardware" = "hisilicon" || "$hardware" = "bigfish" ]]; then
    if [[ "$build_product" = "Hi3798MV300" || "$build_product" = "Hi3798MV310" ]]; then
        BLK_DEV=/dev/block/platform/soc/by-name/system
    elif [[ "$build_product" = "Hi3798MV320" ]]; then
        BLK_DEV=/dev/block/by-name/system
    fi
elif [[ "$hardware" = "goke" || "$hardware" = "kunlun" ]]; then
    BLK_DEV=/dev/block/by-name/system
elif [[ "$hardware" = "amlogic" ]]; then
    BLK_DEV=/dev/block/system
elif [[ "$hardware" = "rk30board" ]]; then
    if [[ "$build_product" = "rk3528_box_32" ]]; then
        BLK_DEV=/dev/block/by-name/system
    fi
fi

if [[ "$BLK_DEV" != "UNKNOWN" ]]; then
    UUID=$(blkid -s UUID -o value $BLK_DEV)
fi

if [[ "$UUID" = "UNKNOWN" ]]; then
    UUID=f198b6cb-845c-45ee-b8df-92a9ca28dddd
fi

#check uuid
#echo "uuid: $UUID"
encoded_uuid=N$(echo -n "$UUID" | base64)
token=`getprop ro.ch.token "unknown" | busybox tr -d " "`
if [[ "$token" = "unknown" ]]; then
    setprop ro.ch.token "${encoded_uuid}"
elif [[ "$token" != "${encoded_uuid}" ]]; then
    setprop ro.ch.dev.mask "1"
fi