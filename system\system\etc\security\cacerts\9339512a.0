-----<PERSON><PERSON><PERSON> CERTIFICATE-----
MIIGnTCCBIWgAwIBAgICBcYwDQYJKoZIhvcNAQEFBQAwRTELMAkGA1UEBhMCQk0x
GTAXBgNVBAoTEFF1b1ZhZGlzIExpbWl0ZWQxGzAZBgNVBAMTElF1b1ZhZGlzIFJv
b3QgQ0EgMzAeFw0wNjExMjQxOTExMjNaFw0zMTExMjQxOTA2NDRaMEUxCzAJBgNV
BAYTAkJNMRkwFwYDVQQKExBRdW9WYWRpcyBMaW1pdGVkMRswGQYDVQQDExJRdW9W
YWRpcyBSb290IENBIDMwggIiMA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoICAQDM
V0IWVJzmmNPTTe7+7cefQzl<PERSON><PERSON>bPoFog02w1ZkXTPkrgEQK0CSzGrvI2RaNggDhoB
4hp7Thdd4oq3P5kazethq8Jlph+3t723j/z9cI8LoGe+AaJZz3HmDyl2/7FWeUUr
H556VOijKTVopAFPD6QuN+8bv+OPEKhyq1hX51SGyMnzW9os2l2ObjyjPtr7guXd
8lyyBTNvijbO0BNO/79KDDRMpsMhvVAEVeuxu537RR5kFd5VAYwCdrXLoT9Cabwv
vWhDFlaJKjdhkf2mrk7AyxRllDdLkgbvBNDInIjbC3uBr7E9KsRlOni27tyAsdLT
mZw67mtaa7ONt9XOnMK+pUsvFrGeaDsGb659n/je7Mwpp5ijJUMv7/FfJuGITfhe
btfZFG4ZM2mnO4SJk8RTVROhUXhA+LjJou57ulJCg54U7QVSWllWp5f8nT8KKdjc
T5EOE7zelaTfi5m+rJsziO+1ga8bxiJTyPbH7pcUsMV8eFLI8M5ud2CEpukqdiDt
WAEXMJPpGovgc2PZapKUSU60rUqFxKMiMPwJ7Wgic6aIDFUhWMXhOp8q3crhkODZ
c6tsgLjoC2SToJyMGf+z0gzskSaHirOi4XCPLArlzW1oUevaPwV/izLmE1xr/l9A
4iLItLRkT9a6fUg+qGkM17uGcclzuD87nSVL2v9A6wIDAQABo4IBlTCCAZEwDwYD
VR0TAQH/BAUwAwEB/zCB4QYDVR0gBIHZMIHWMIHTBgkrBgEEAb5YAAMwgcUwgZMG
CCsGAQUFBwICMIGGGoGDQW55IHVzZSBvZiB0aGlzIENlcnRpZmljYXRlIGNvbnN0
aXR1dGVzIGFjY2VwdGFuY2Ugb2YgdGhlIFF1b1ZhZGlzIFJvb3QgQ0EgMyBDZXJ0
aWZpY2F0ZSBQb2xpY3kgLyBDZXJ0aWZpY2F0aW9uIFByYWN0aWNlIFN0YXRlbWVu
dC4wLQYIKwYBBQUHAgEWIWh0dHA6Ly93d3cucXVvdmFkaXNnbG9iYWwuY29tL2Nw
czALBgNVHQ8EBAMCAQYwHQYDVR0OBBYEFPLAE+CCQz777i9nMpY1XNu4ywLQMG4G
A1UdIwRnMGWAFPLAE+CCQz777i9nMpY1XNu4ywLQoUmkRzBFMQswCQYDVQQGEwJC
TTEZMBcGA1UEChMQUXVvVmFkaXMgTGltaXRlZDEbMBkGA1UEAxMSUXVvVmFkaXMg
Um9vdCBDQSAzggIFxjANBgkqhkiG9w0BAQUFAAOCAgEAT62gLEz6wPJv92ZVqyM0
7ucp2sNbtrCD2dDQ4iH782CnO11gUyeim/YIIirnv6By5ZwkajGxkHon24QRiSem
d1o417+shvzuXYO8BsbRd2sPbSQvS3pspweWyuOEn62Iix2rFo1bZhfZFvSLgNLd
+LJ2w/w4E6oM3kJpK27zPOuAJ9v1pkQNn1pVWQvVDVJIxa6f8i+AxeoyUDUSly7B
4f/xI4hROJ/yZlZ25w9Rl6VSDE1JUZU2Pb+iSwwQHYaZTKrzchGT5Or2m9qoXadN
t54CrnMAyNojA+j56hl0YgCUyyIgvpSnWbWCar6ZeXqp8kokUvd0/bpO5qgdAm6x
DYBEwa7TIzdfu4V8K5Iu6H6li92Z4b8nby1dqnuH/grdS/yO9SbkbnBCbjPsMZ57
k8HkyWkaPcBrTiJt7qtYTcbQQcEr6k8Sh17rRdhs9ZgC06DYVYoGmRmioHfRMJ6s
zHXug/WwYjnPbFfiTNKRCw51KBuav/0aQ/HKd/s7j2G4aSgWQgRecCocIdiP4b0j
Wy10QJLZYxkNc91pvGJHvOB0K7Lrfb5BG7XARsWhIstfTsEokt4YutUqKLsRixeT
mJlglFwjz1onl14LBQaTNx47aTbrqZ5hHY8y2o4M1nQ+ewkk2gF3R8Q7zTSMmfXK
4SVhM7JZG+Ju1zdXtg2pEto=
-----END CERTIFICATE-----
Certificate:
    Data:
        Version: 3 (0x2)
        Serial Number: 1478 (0x5c6)
    Signature Algorithm: sha1WithRSAEncryption
        Issuer: C=BM, O=QuoVadis Limited, CN=QuoVadis Root CA 3
        Validity
            Not Before: Nov 24 19:11:23 2006 GMT
            Not After : Nov 24 19:06:44 2031 GMT
        Subject: C=BM, O=QuoVadis Limited, CN=QuoVadis Root CA 3
        Subject Public Key Info:
            Public Key Algorithm: rsaEncryption
                Public-Key: (4096 bit)
                Modulus:
                    00:cc:57:42:16:54:9c:e6:98:d3:d3:4d:ee:fe:ed:
                    c7:9f:43:39:4a:65:b3:e8:16:88:34:db:0d:59:91:
                    74:cf:92:b8:04:40:ad:02:4b:31:ab:bc:8d:91:68:
                    d8:20:0e:1a:01:e2:1a:7b:4e:17:5d:e2:8a:b7:3f:
                    99:1a:cd:eb:61:ab:c2:65:a6:1f:b7:b7:bd:b7:8f:
                    fc:fd:70:8f:0b:a0:67:be:01:a2:59:cf:71:e6:0f:
                    29:76:ff:b1:56:79:45:2b:1f:9e:7a:54:e8:a3:29:
                    35:68:a4:01:4f:0f:a4:2e:37:ef:1b:bf:e3:8f:10:
                    a8:72:ab:58:57:e7:54:86:c8:c9:f3:5b:da:2c:da:
                    5d:8e:6e:3c:a3:3e:da:fb:82:e5:dd:f2:5c:b2:05:
                    33:6f:8a:36:ce:d0:13:4e:ff:bf:4a:0c:34:4c:a6:
                    c3:21:bd:50:04:55:eb:b1:bb:9d:fb:45:1e:64:15:
                    de:55:01:8c:02:76:b5:cb:a1:3f:42:69:bc:2f:bd:
                    68:43:16:56:89:2a:37:61:91:fd:a6:ae:4e:c0:cb:
                    14:65:94:37:4b:92:06:ef:04:d0:c8:9c:88:db:0b:
                    7b:81:af:b1:3d:2a:c4:65:3a:78:b6:ee:dc:80:b1:
                    d2:d3:99:9c:3a:ee:6b:5a:6b:b3:8d:b7:d5:ce:9c:
                    c2:be:a5:4b:2f:16:b1:9e:68:3b:06:6f:ae:7d:9f:
                    f8:de:ec:cc:29:a7:98:a3:25:43:2f:ef:f1:5f:26:
                    e1:88:4d:f8:5e:6e:d7:d9:14:6e:19:33:69:a7:3b:
                    84:89:93:c4:53:55:13:a1:51:78:40:f8:b8:c9:a2:
                    ee:7b:ba:52:42:83:9e:14:ed:05:52:5a:59:56:a7:
                    97:fc:9d:3f:0a:29:d8:dc:4f:91:0e:13:bc:de:95:
                    a4:df:8b:99:be:ac:9b:33:88:ef:b5:81:af:1b:c6:
                    22:53:c8:f6:c7:ee:97:14:b0:c5:7c:78:52:c8:f0:
                    ce:6e:77:60:84:a6:e9:2a:76:20:ed:58:01:17:30:
                    93:e9:1a:8b:e0:73:63:d9:6a:92:94:49:4e:b4:ad:
                    4a:85:c4:a3:22:30:fc:09:ed:68:22:73:a6:88:0c:
                    55:21:58:c5:e1:3a:9f:2a:dd:ca:e1:90:e0:d9:73:
                    ab:6c:80:b8:e8:0b:64:93:a0:9c:8c:19:ff:b3:d2:
                    0c:ec:91:26:87:8a:b3:a2:e1:70:8f:2c:0a:e5:cd:
                    6d:68:51:eb:da:3f:05:7f:8b:32:e6:13:5c:6b:fe:
                    5f:40:e2:22:c8:b4:b4:64:4f:d6:ba:7d:48:3e:a8:
                    69:0c:d7:bb:86:71:c9:73:b8:3f:3b:9d:25:4b:da:
                    ff:40:eb
                Exponent: 65537 (0x10001)
        X509v3 extensions:
            X509v3 Basic Constraints: critical
                CA:TRUE
            X509v3 Certificate Policies: 
                Policy: 1.3.6.1.4.1.8024.0.3
                  User Notice:
                    Explicit Text: Any use of this Certificate constitutes acceptance of the QuoVadis Root CA 3 Certificate Policy / Certification Practice Statement.
                  CPS: http://www.quovadisglobal.com/cps

            X509v3 Key Usage: 
                Certificate Sign, CRL Sign
            X509v3 Subject Key Identifier: 
                F2:C0:13:E0:82:43:3E:FB:EE:2F:67:32:96:35:5C:DB:B8:CB:02:D0
            X509v3 Authority Key Identifier: 
                keyid:F2:C0:13:E0:82:43:3E:FB:EE:2F:67:32:96:35:5C:DB:B8:CB:02:D0
                DirName:/C=BM/O=QuoVadis Limited/CN=QuoVadis Root CA 3
                serial:05:C6

    Signature Algorithm: sha1WithRSAEncryption
         4f:ad:a0:2c:4c:fa:c0:f2:6f:f7:66:55:ab:23:34:ee:e7:29:
         da:c3:5b:b6:b0:83:d9:d0:d0:e2:21:fb:f3:60:a7:3b:5d:60:
         53:27:a2:9b:f6:08:22:2a:e7:bf:a0:72:e5:9c:24:6a:31:b1:
         90:7a:27:db:84:11:89:27:a6:77:5a:38:d7:bf:ac:86:fc:ee:
         5d:83:bc:06:c6:d1:77:6b:0f:6d:24:2f:4b:7a:6c:a7:07:96:
         ca:e3:84:9f:ad:88:8b:1d:ab:16:8d:5b:66:17:d9:16:f4:8b:
         80:d2:dd:f8:b2:76:c3:fc:38:13:aa:0c:de:42:69:2b:6e:f3:
         3c:eb:80:27:db:f5:a6:44:0d:9f:5a:55:59:0b:d5:0d:52:48:
         c5:ae:9f:f2:2f:80:c5:ea:32:50:35:12:97:2e:c1:e1:ff:f1:
         23:88:51:38:9f:f2:66:56:76:e7:0f:51:97:a5:52:0c:4d:49:
         51:95:36:3d:bf:a2:4b:0c:10:1d:86:99:4c:aa:f3:72:11:93:
         e4:ea:f6:9b:da:a8:5d:a7:4d:b7:9e:02:ae:73:00:c8:da:23:
         03:e8:f9:ea:19:74:62:00:94:cb:22:20:be:94:a7:59:b5:82:
         6a:be:99:79:7a:a9:f2:4a:24:52:f7:74:fd:ba:4e:e6:a8:1d:
         02:6e:b1:0d:80:44:c1:ae:d3:23:37:5f:bb:85:7c:2b:92:2e:
         e8:7e:a5:8b:dd:99:e1:bf:27:6f:2d:5d:aa:7b:87:fe:0a:dd:
         4b:fc:8e:f5:26:e4:6e:70:42:6e:33:ec:31:9e:7b:93:c1:e4:
         c9:69:1a:3d:c0:6b:4e:22:6d:ee:ab:58:4d:c6:d0:41:c1:2b:
         ea:4f:12:87:5e:eb:45:d8:6c:f5:98:02:d3:a0:d8:55:8a:06:
         99:19:a2:a0:77:d1:30:9e:ac:cc:75:ee:83:f5:b0:62:39:cf:
         6c:57:e2:4c:d2:91:0b:0e:75:28:1b:9a:bf:fd:1a:43:f1:ca:
         77:fb:3b:8f:61:b8:69:28:16:42:04:5e:70:2a:1c:21:d8:8f:
         e1:bd:23:5b:2d:74:40:92:d9:63:19:0d:73:dd:69:bc:62:47:
         bc:e0:74:2b:b2:eb:7d:be:41:1b:b5:c0:46:c5:a1:22:cb:5f:
         4e:c1:28:92:de:18:ba:d5:2a:28:bb:11:8b:17:93:98:99:60:
         94:5c:23:cf:5a:27:97:5e:0b:05:06:93:37:1e:3b:69:36:eb:
         a9:9e:61:1d:8f:32:da:8e:0c:d6:74:3e:7b:09:24:da:01:77:
         47:c4:3b:cd:34:8c:99:f5:ca:e1:25:61:33:b2:59:1b:e2:6e:
         d7:37:57:b6:0d:a9:12:da
SHA1 Fingerprint=1F:49:14:F7:D8:74:95:1D:DD:AE:02:C0:BE:FD:3A:2D:82:75:51:85
