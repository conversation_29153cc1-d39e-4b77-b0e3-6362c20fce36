
# begin build properties
# autogenerated by buildinfo.sh
ro.build.id=PI
ro.build.display.id=rk3528_box_32-userdebug 9 PI 4027.3111.9002 test-keys
ro.build.version.incremental=1300.302.2408
ro.build.version.sdk=28
ro.build.version.preview_sdk=0
ro.build.version.codename=REL
ro.build.version.all_codenames=REL
ro.build.version.release=9
ro.build.version.security_patch=2020-11-05
ro.build.version.base_os=
ro.build.version.min_supported_target_sdk=17
ro.build.date=Wed Dec 11 17:39:34 CST 2024
ro.build.date.utc=1733909974
ro.build.type=userdebug
ro.build.user=jenkins
ro.build.host=ubuntu
ro.build.tags=test-keys
ro.build.flavor=rk3528_box_32-userdebug
ro.build.system_root_image=true
ro.product.model=SY910_765
ro.product.name=rk3528_box_32
ro.product.productclass=SY910_765
ro.product.device=rk3528_box_32
# ro.product.cpu.abi and ro.product.cpu.abi2 are obsolete,
# use ro.product.cpu.abilist instead.
ro.product.cpu.abi=armeabi-v7a
ro.product.cpu.abi2=armeabi
ro.product.cpu.abilist=armeabi-v7a,armeabi
ro.product.cpu.abilist32=armeabi-v7a,armeabi
ro.product.cpu.abilist64=
ro.product.manufacturer=SHUYING
ro.wifi.channels=
# ro.build.product is obsolete; use ro.product.device
ro.build.product=rk3528_box_32
# Do not try to parse description, fingerprint, or thumbprint
ro.build.description=rk3528_box_32-userdebug 9 PI 4027.3111.9002 test-keys
ro.build.fingerprint=RockChip/rk3528_box_32/rk3528_box_32:9/PI/4027.3111.9002:userdebug/test-keys
ro.build.characteristics=tv
# end build properties
#
# from device/rockchip/rk3528/rk3528_box_32/system.prop
#
#
# system.prop
#

#rild.libpath=/system/lib/libreference-ril.so
#rild.libargs=-d /dev/ttyUSB2
# Default ecclist
ro.ril.ecclist=112,911
wifi.interface=wlan0
rild.libpath=/system/lib/libril-rk29-dataonly.so
rild.libargs=-d /dev/ttyACM0
persist.tegra.nvmmlite = 1
persist.sys.boot.check=false
ro.audio.monitorOrientation=true

#NFC
debug.nfc.fw_download=false
debug.nfc.se=false

#add Rockchip properties here
ro.rk.screenshot_enable=true
ro.rk.def_brightness=200
ro.rk.homepage_base=http://www.google.com/webhp?client={CID}&amp;source=android-home
ro.rk.install_non_market_apps=false
vendor.hwc.compose_policy=6
sys.wallpaper.rgb565=0
sf.power.control=8847360
sys.rkadb.root=1
ro.sf.fakerotation=false
ro.sf.hwrotation=0
ro.rk.MassStorage=false
ro.rk.systembar.voiceicon=true
ro.rk.systembar.tabletUI=false
ro.rk.LowBatteryBrightness=true
ro.tether.denied=false
sys.resolution.changed=false
ro.default.size=100
persist.sys.timezone=
ro.product.usbfactory=rockchip_usb
ro.support.lossless.bitstream=true
wifi.supplicant_scan_interval=15
ro.factory.tool=0
ro.adb.secure =0
vendor.hwc.enable=1
#set wifi contry code
ro.boot.wificountrycode=CN
#set for video optimize
sys.video.netBuffer=20
sys.video.refFrameMode=0
ro.nrdp.modelgroup=NEXUSPLAYERFUGU
ro.build.shutdown_timeout=1
persist.sys.rotation.enable=true

# Set korgutv as default launcher
ro.sys.launcher.package=com.kozyax.korgutv
ro.sys.launcher.activity=com.kozyax.korgutv.MainActivity

#
# ADDITIONAL_BUILD_PROPERTIES
#
ro.bionic.ld.warning=1
ro.art.hiddenapi.warning=1
ro.treble.enabled=true
persist.sys.dalvik.vm.lib.2=libart.so
dalvik.vm.isa.arm.variant=cortex-a53
dalvik.vm.isa.arm.features=default
dalvik.vm.lockprof.threshold=500
net.bt.name=Android
dalvik.vm.stack-trace-dir=/data/anr
ro.expect.recovery_id=0x3d31875cd1b7f31f44691d1183ece754e3bbf522000000000000000000000000
