# Copyright (C) 2017 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

#
# Xbox Wireless Controller
#


# Mapping according to https://developer.android.com/training/game-controllers/controller-input.html

key 0x132    BUTTON_X
key 0x130    BUTTON_A
key 0x131    BUTTON_B
key 0x133    BUTTON_Y

key 0x134    BUTTON_L1
key 0x135    BUTTON_R1

# LT axis
axis 0x02   LTRIGGER
# RT axis
axis 0x05   RTRIGGER


# Left Analog Stick
axis 0x00    X
axis 0x01    Y
# Right Analog Stick
axis 0x03    Z
axis 0x04    RZ

# Left stick click
key 0x138    BUTTON_THUMBL
# Right stick click
key 0x139    BUTTON_THUMBR

# Hat
axis 0x10 HAT_X
axis 0x11 HAT_Y

# Mapping according to https://www.kernel.org/doc/Documentation/input/gamepad.txt
# Two overlapping rectangles
key 0x136    BUTTON_SELECT
# Hamburger - 3 parallel lines
key 0x137    BUTTON_START

# Xbox key
key 0x8b    HOME