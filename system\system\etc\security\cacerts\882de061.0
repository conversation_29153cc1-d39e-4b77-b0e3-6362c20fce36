-----<PERSON><PERSON><PERSON> CERTIFICATE-----
MIIDODCCAiCgAwIBAgIGIAYFFnACMA0GCSqGSIb3DQEBBQUAMDsxCzAJBgNVBAYT
AlJPMREwDwYDVQQKEwhjZXJ0U0lHTjEZMBcGA1UECxMQY2VydFNJR04gUk9PVCBD
QTAeFw0wNjA3MDQxNzIwMDRaFw0zMTA3MDQxNzIwMDRaMDsxCzAJBgNVBAYTAlJP
MREwDwYDVQQKEwhjZXJ0U0lHTjEZMBcGA1UECxMQY2VydFNJR04gUk9PVCBDQTCC
ASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBALczuX7IJUqOtdu0KBuqV5Do
0SLTZLrTk+jUrIZhQGpgV2hUhE28alQCBf/fm5oqrl0Hj0rDKH/v+yv6efHHrfAQ
UySQi2bJqIirr1qjAOm+ukbuW3N7LBeCgV5iLKECZbO9xSsAfsT8AzNXDe3i+s5d
RdY4zTW2ssHQnIFKquSyAVwdj1+ZxLGt24gh65AIgoDzMKND5pCCrlUoSe1b16kQ
OA7+j0xbm0bqQfWwCHTD0IgztnzXdN/chNFDDnU5oSVAKOp4yw4sLjmdjItuFhwv
JoIQ4uNllAoEwF73XVv4EOLQunpL+943AAAaWyjj0pxzPjKHmKHJUS/X3qwzs08C
AwEAAaNCMEAwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAcYwHQYDVR0O
BBYEFOCMm9slSbPxfIbWskKHC9BroNnkMA0GCSqGSIb3DQEBBQUAA4IBAQA+0hyJ
LjX8+HXd5n9liPRyTMks1zJO890ZeUe9jjtbkw9QSSQTaxQGcu8J06Gh40CEyecY
MnQ8SG4Pn0vU9x7Tk4ZkVJdjclDVVc/6IJMCopvDI5NOFlV2oHB5bc0hH88vLbwZ
44gx+FkagQnIl6Z0x2DEW8xXjrJ1/RsCCdtZb3KTafcxQdaIOL+Hsr0Wefmq5L6I
Jd1hJyMctTEHBDa0GpC9oHRxUIltvBTjD4au8as+x6AJzKNI0eDbZOeStc+vckNw
i/nDhDwTqn6Sm1dTk/pwwpEOMfmbZ13pljheX7NzTogVZ96edhBiIL5VaZVDADlN
9u6wWk5JRFRYX0KD
-----END CERTIFICATE-----
Certificate:
    Data:
        Version: 3 (0x2)
        Serial Number: 35210227249154 (0x200605167002)
    Signature Algorithm: sha1WithRSAEncryption
        Issuer: C=RO, O=certSIGN, OU=certSIGN ROOT CA
        Validity
            Not Before: Jul  4 17:20:04 2006 GMT
            Not After : Jul  4 17:20:04 2031 GMT
        Subject: C=RO, O=certSIGN, OU=certSIGN ROOT CA
        Subject Public Key Info:
            Public Key Algorithm: rsaEncryption
                Public-Key: (2048 bit)
                Modulus:
                    00:b7:33:b9:7e:c8:25:4a:8e:b5:db:b4:28:1b:aa:
                    57:90:e8:d1:22:d3:64:ba:d3:93:e8:d4:ac:86:61:
                    40:6a:60:57:68:54:84:4d:bc:6a:54:02:05:ff:df:
                    9b:9a:2a:ae:5d:07:8f:4a:c3:28:7f:ef:fb:2b:fa:
                    79:f1:c7:ad:f0:10:53:24:90:8b:66:c9:a8:88:ab:
                    af:5a:a3:00:e9:be:ba:46:ee:5b:73:7b:2c:17:82:
                    81:5e:62:2c:a1:02:65:b3:bd:c5:2b:00:7e:c4:fc:
                    03:33:57:0d:ed:e2:fa:ce:5d:45:d6:38:cd:35:b6:
                    b2:c1:d0:9c:81:4a:aa:e4:b2:01:5c:1d:8f:5f:99:
                    c4:b1:ad:db:88:21:eb:90:08:82:80:f3:30:a3:43:
                    e6:90:82:ae:55:28:49:ed:5b:d7:a9:10:38:0e:fe:
                    8f:4c:5b:9b:46:ea:41:f5:b0:08:74:c3:d0:88:33:
                    b6:7c:d7:74:df:dc:84:d1:43:0e:75:39:a1:25:40:
                    28:ea:78:cb:0e:2c:2e:39:9d:8c:8b:6e:16:1c:2f:
                    26:82:10:e2:e3:65:94:0a:04:c0:5e:f7:5d:5b:f8:
                    10:e2:d0:ba:7a:4b:fb:de:37:00:00:1a:5b:28:e3:
                    d2:9c:73:3e:32:87:98:a1:c9:51:2f:d7:de:ac:33:
                    b3:4f
                Exponent: 65537 (0x10001)
        X509v3 extensions:
            X509v3 Basic Constraints: critical
                CA:TRUE
            X509v3 Key Usage: critical
                Digital Signature, Non Repudiation, Certificate Sign, CRL Sign
            X509v3 Subject Key Identifier: 
                E0:8C:9B:DB:25:49:B3:F1:7C:86:D6:B2:42:87:0B:D0:6B:A0:D9:E4
    Signature Algorithm: sha1WithRSAEncryption
         3e:d2:1c:89:2e:35:fc:f8:75:dd:e6:7f:65:88:f4:72:4c:c9:
         2c:d7:32:4e:f3:dd:19:79:47:bd:8e:3b:5b:93:0f:50:49:24:
         13:6b:14:06:72:ef:09:d3:a1:a1:e3:40:84:c9:e7:18:32:74:
         3c:48:6e:0f:9f:4b:d4:f7:1e:d3:93:86:64:54:97:63:72:50:
         d5:55:cf:fa:20:93:02:a2:9b:c3:23:93:4e:16:55:76:a0:70:
         79:6d:cd:21:1f:cf:2f:2d:bc:19:e3:88:31:f8:59:1a:81:09:
         c8:97:a6:74:c7:60:c4:5b:cc:57:8e:b2:75:fd:1b:02:09:db:
         59:6f:72:93:69:f7:31:41:d6:88:38:bf:87:b2:bd:16:79:f9:
         aa:e4:be:88:25:dd:61:27:23:1c:b5:31:07:04:36:b4:1a:90:
         bd:a0:74:71:50:89:6d:bc:14:e3:0f:86:ae:f1:ab:3e:c7:a0:
         09:cc:a3:48:d1:e0:db:64:e7:92:b5:cf:af:72:43:70:8b:f9:
         c3:84:3c:13:aa:7e:92:9b:57:53:93:fa:70:c2:91:0e:31:f9:
         9b:67:5d:e9:96:38:5e:5f:b3:73:4e:88:15:67:de:9e:76:10:
         62:20:be:55:69:95:43:00:39:4d:f6:ee:b0:5a:4e:49:44:54:
         58:5f:42:83
SHA1 Fingerprint=FA:B7:EE:36:97:26:62:FB:2D:B0:2A:F6:BF:03:FD:E8:7C:4B:2F:9B
