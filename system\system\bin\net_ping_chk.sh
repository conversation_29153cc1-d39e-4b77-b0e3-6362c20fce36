#!/system/bin/sh

LOG="/system/bin/log -t net_ping_chk"
ECHO()
{
    #echo $1
    #$LOG "$1"
}

set_network_mode()
{
	if [ "$network_mode" != "$1" ] ; then
		ECHO "========set_network_mode: $network_mode to $1"
		network_mode=$1
		setprop sys.network.net_prio $network_mode
	fi
}

try_switch_to_wlan()
{
	set_network_mode wlan
}

try_switch_to_eth()
{
	set_network_mode eth
}

try_switch_prio()
{
	if [ "$network_prio" != "$1" ] ; then
		ECHO "========set_network_prio: $network_prio to $1"
		network_prio=$1
		setprop persist.sys.network.ip_prio $network_prio
	fi
}

ping_dns()
{
	if [ "$3" == "0" ] ; then
		return 1
	fi

	iface=$1
	if [ "$2" == "4" ] ; then
		pings=ping
        if [ "$1" == "ppp0" ] ; then
            dns1=`getprop net.$iface.dns1`
        else
            dns1=`getprop dhcp.$iface.dns1`
        fi
	else
		pings=ping6
		dns1=`getprop dhclient.$iface.dns1`
		if [[ $dns1 == fe80::* ]] ; then
			dns1=$dns1"%$iface"
		fi
	fi

	$pings -c 3 -W 3 -I $iface $dns1 > /dev/null 2>&1
	if [ "$?" == "0" ] ; then
		ECHO "$pings $iface dns1 $dns1 success"
		return 0;
	else
		ECHO "$pings $iface dns1 $dns1 fail"
		return 1;
	fi
}

enable=`getprop persist.sys.network.ip_prio_en`
if [ "$enable" != "1" ] ; then
exit
fi

eth_v4_if=eth1
eth_v6_if=eth1
wlan_if=wlan0
eth_conn=0
eth_v4=0
eth_v6=0
wlan_v4=0
wlan_v6=0

network_mode=eth
setprop sys.network.net_prio $network_mode
network_prio=6
setprop persist.sys.network.ip_prio $network_prio

while [ true ] ; do
	sleep 1

	# eth  check
	ip_addr=`ifconfig eth1 | grep "inet addr"`
	if [ "$?" == "0" ] ; then
		eth_v4=1
		eth_v4_if=eth1
		ECHO "eth1 V4 addr $ip_addr"
	else
		ip_addr=`ifconfig ppp0 | grep "inet addr"`
		if [ "$?" == "0" ] ; then
			eth_v4=1
			eth_v4_if=ppp0
			ECHO "ppp0 V4 addr $ip_addr"
		else
			eth_v4=0
		fi
	fi
	ip_addr=`ifconfig ppp0 | grep "inet6 addr"`
	if [ "$?" == "0" ] ; then
		eth_v6=1
		eth_v6_if=ppp0
		ECHO "ppp0 V6 addr $ip_addr"
	else
		ip_addr=`ifconfig eth1 | grep "inet6 addr"`
		if [ "$?" == "0" ] ; then
			eth_v6=1
			eth_v6_if=eth1
			ECHO "eth1 V6 addr $ip_addr"
		else
			eth_v6=0
		fi
	fi
	ECHO "eth_v4=$eth_v4 eth_v6=$eth_v6"

	# wlan check

	wlan0_mac=`cat /sys/class/net/wlan0/address`
	if [ "$?" == "0" ] ; then
    	echo "READ Wlan0 Mac: $wlan0_mac"
	   	wlan0_mac_str=$(echo "$wlan0_mac" | tr -d "[: ]")
	   	# echo "first Wlan0 Mac: $wlan0_mac_str"
	   	wlan0_mac_final=$(echo "$wlan0_mac_str" | tr [:lower:] [:upper:])
	   	# echo "sencond Wlan0 Mac: $wlan0_mac_final"
	   	setprop wlanMac $wlan0_mac_final
	   	setprop moduleMacAddress $wlan0_mac_final
	   	setprop modulewlanMac $wlan0_mac_final
    else
        ECHO "Wlan0 is not ready,wait for init"
    fi

	ip_addr=`ifconfig $wlan_if | grep "inet addr"`
	if [ "$?" == "0" ] ; then
		wlan_v4=1
		ECHO "wlan V4 addr $ip_addr"
	else
		wlan_v4=0
	fi
	ip_addr=`ifconfig $wlan_if | grep "inet6 addr"`
	if [ "$?" == "0" ] ; then
		wlan_v6=1
		ECHO "wlan V6 addr $ip_addr"
	else
		wlan_v6=0
	fi
	ECHO "wlan_v4=$wlan_v4 wlan_v6=$wlan_v6"

	pri=`getprop persist.sys.database.ip_prio`
	if [ "$pri" == "4" ] ; then # eth V4 > eth V6 > wlan V4 > wlan V6
		ECHO "ipv4 prefer"
		ping_dns $eth_v4_if 4 $eth_v4
		if [ "$?" == "0" ] ; then
			try_switch_prio 4
			try_switch_to_eth
			continue
		else
			ping_dns $eth_v6_if 6 $eth_v6
			if [ "$?" == "0" ] ; then
				try_switch_prio 6
				try_switch_to_eth
				continue
			else
				ping_dns $wlan_if 4 $wlan_v4
				if [ "$?" == "0" ] ; then
					try_switch_prio 4
					try_switch_to_wlan
					continue
				else
					ping_dns $wlan_if 6 $wlan_v6
					if [ "$?" == "0" ] ; then
						try_switch_prio 6
						try_switch_to_wlan
						continue
					else
						try_switch_to_eth
						continue
					fi
				fi
			fi
		fi
	else # eth V6 > eth V4 > wlan V6 > wlan V4
		ECHO "ipv6 prefer"
		ping_dns $eth_v6_if 6 $eth_v6
		if [ "$?" == "0" ] ; then
			try_switch_prio 6
			try_switch_to_eth
			continue
		else
			ping_dns $eth_v4_if 4 $eth_v4
			if [ "$?" == "0" ] ; then
				try_switch_prio 4
				try_switch_to_eth
				continue
			else
				ping_dns $wlan_if 6 $wlan_v6
				if [ "$?" == "0" ] ; then
					try_switch_prio 6
					try_switch_to_wlan
					continue
				else
					ping_dns $wlan_if 4 $wlan_v4
					if [ "$?" == "0" ] ; then
						try_switch_prio 4
						try_switch_to_wlan
						continue
					else
						try_switch_to_eth
						continue
					fi
				fi
			fi
		fi
	fi
done
