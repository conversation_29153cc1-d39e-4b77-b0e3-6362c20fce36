-----<PERSON><PERSON><PERSON> CERTIFICATE-----
MIIFcDCCA1igAwIBAgIEAJiWjTANBgkqhkiG9w0BAQsFADBYMQswCQYDVQQGEwJO
TDEeMBwGA1UECgwVU3RhYXQgZGVyIE5lZGVybGFuZGVuMSkwJwYDVQQDDCBTdGFh
dCBkZXIgTmVkZXJsYW5kZW4gRVYgUm9vdCBDQTAeFw0xMDEyMDgxMTE5MjlaFw0y
MjEyMDgxMTEwMjhaMFgxCzAJBgNVBAYTAk5MMR4wHAYDVQQKDBVTdGFhdCBkZXIg
TmVkZXJsYW5kZW4xKTAnBgNVBAMMIFN0YWF0IGRlciBOZWRlcmxhbmRlbiBFViBS
b290IENBMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA48d+ifkkSzrS
M4M1LGns3Amk41GoJSt5uAg94JG6hIXGhaTK5skuU6TJJB79VWZxXSzFYGgEt9nC
UiY4iKTWO0Cmws0/zZiTs1QUWJZV1VD+hq2kY39ch/aO5ieSZxeSAgMs3NZmdO3d
Z//BYY1jTw+bbRcwJu+r0h8QoPnFfxZpgQNH7R5ojXKhTbImxrpsX23Wr9GxE46p
rfNeaXUmGD5BKyF/7otdBwadQ8QpCiv8Kj6GyzyDOvnJDdrFmeK8eEEzduG/L13l
pJhQDBXd4Pqcfzho0LKmeqfRMb1+ilgnQ7O6M5HTp5gVXJrm0w912fxBmJc+qiXb
j5IusHsMX/FjqTf5m3VpTCgmJdrV8hJwRVXj33NeN/UhbJCONVrJ0yPr08C+eKxC
KFhmpUZtcALXEPlLVPxdhkqHz3/KRawRWrUgUY0viEeXOcDPusBCAUCZSCELa6fS
/ZbV0b5GnUngC6agIk440ME8MLxwjyx1zNDFjFE7PZQIZCZhfbnDZY8UnCHQqv0X
cgOPvZuM5l5Tnrmd74K74bzickFbIZTTRTeU0d8JOV3nI6qaHcptqAqGhYqCvkIH
1vI4gnPah1vlPNOePqc7nvQDs/nxfRN0Av+7oeX6AHkcpmZBiFxgV6YuCcS6/ZrP
px9Aw7vMWgpVSzs4dlG4Y4uElBbmVvMCAwEAAaNCMEAwDwYDVR0TAQH/BAUwAwEB
/zAOBgNVHQ8BAf8EBAMCAQYwHQYDVR0OBBYEFP6rAJCYniT8qcwaivsnuL8wbqg7
MA0GCSqGSIb3DQEBCwUAA4ICAQDPdyxuVr5Os7aEAJSrR8kN0nbHhp8dB9O2tLsI
eK9p0gtJ3jPFrK3CiAJ9Brc1AsFgyb/E6JTe1NOpEyVa/m6irn0F3H3zbPB+po3u
2dfOWBfoqSmuc0iH55vKbimhZF8ZE/euBhD/UcabTVUlT5OZEAFTdfETzsemQUHS
v4ilf0X8rLiltTMMgsT7B/Zq5SWEXwbKwYY5EdtYzXc7LMJMD16a4/CrPmEbUCTC
wPTxGfARKbalGAKb12NMcIxHowNDXLldRqANb/9Zjr7dn3LDWyvfjFvO5QxGbJKy
CqNMVEIYFRIYvdr8unRu/8G2oGTYqV9Vrp9canaW2HNnh/tNf1zuacpzEPuKqf2e
vTY4SUmH9A4U8OmHuD+nT3pajnnUk+S7aFKErGzp85hwVXIy+TSrK0m1zSBi5Dp6
Z2Orltxtrpfs/J92VoguZs9btsmksNcFuuEnL5O7Jiqik7Ab846+HUCjuTaPPoIa
Gl6I6lD4WeKDRikL40Rc4ZW2aZCaFG+XroHPaO+Zmr615+F/+PoTRxZMzG0IQOeL
eG9QgkRQP2YGiqtDhFZKDyAthg710tvSeopLzaXoTvFeJiUBWSOgftL2fiFX1ye8
FVdMpEbB4IMeDExNH08GGeL5qPQ6gqGyeUN51q1veieQA6TqJIc/2b3Z6fJfUEkc
7uzXLg==
-----END CERTIFICATE-----
Certificate:
    Data:
        Version: 3 (0x2)
        Serial Number: 10000013 (0x98968d)
    Signature Algorithm: sha256WithRSAEncryption
        Issuer: C=NL, O=Staat der Nederlanden, CN=Staat der Nederlanden EV Root CA
        Validity
            Not Before: Dec  8 11:19:29 2010 GMT
            Not After : Dec  8 11:10:28 2022 GMT
        Subject: C=NL, O=Staat der Nederlanden, CN=Staat der Nederlanden EV Root CA
        Subject Public Key Info:
            Public Key Algorithm: rsaEncryption
                Public-Key: (4096 bit)
                Modulus:
                    00:e3:c7:7e:89:f9:24:4b:3a:d2:33:83:35:2c:69:
                    ec:dc:09:a4:e3:51:a8:25:2b:79:b8:08:3d:e0:91:
                    ba:84:85:c6:85:a4:ca:e6:c9:2e:53:a4:c9:24:1e:
                    fd:55:66:71:5d:2c:c5:60:68:04:b7:d9:c2:52:26:
                    38:88:a4:d6:3b:40:a6:c2:cd:3f:cd:98:93:b3:54:
                    14:58:96:55:d5:50:fe:86:ad:a4:63:7f:5c:87:f6:
                    8e:e6:27:92:67:17:92:02:03:2c:dc:d6:66:74:ed:
                    dd:67:ff:c1:61:8d:63:4f:0f:9b:6d:17:30:26:ef:
                    ab:d2:1f:10:a0:f9:c5:7f:16:69:81:03:47:ed:1e:
                    68:8d:72:a1:4d:b2:26:c6:ba:6c:5f:6d:d6:af:d1:
                    b1:13:8e:a9:ad:f3:5e:69:75:26:18:3e:41:2b:21:
                    7f:ee:8b:5d:07:06:9d:43:c4:29:0a:2b:fc:2a:3e:
                    86:cb:3c:83:3a:f9:c9:0d:da:c5:99:e2:bc:78:41:
                    33:76:e1:bf:2f:5d:e5:a4:98:50:0c:15:dd:e0:fa:
                    9c:7f:38:68:d0:b2:a6:7a:a7:d1:31:bd:7e:8a:58:
                    27:43:b3:ba:33:91:d3:a7:98:15:5c:9a:e6:d3:0f:
                    75:d9:fc:41:98:97:3e:aa:25:db:8f:92:2e:b0:7b:
                    0c:5f:f1:63:a9:37:f9:9b:75:69:4c:28:26:25:da:
                    d5:f2:12:70:45:55:e3:df:73:5e:37:f5:21:6c:90:
                    8e:35:5a:c9:d3:23:eb:d3:c0:be:78:ac:42:28:58:
                    66:a5:46:6d:70:02:d7:10:f9:4b:54:fc:5d:86:4a:
                    87:cf:7f:ca:45:ac:11:5a:b5:20:51:8d:2f:88:47:
                    97:39:c0:cf:ba:c0:42:01:40:99:48:21:0b:6b:a7:
                    d2:fd:96:d5:d1:be:46:9d:49:e0:0b:a6:a0:22:4e:
                    38:d0:c1:3c:30:bc:70:8f:2c:75:cc:d0:c5:8c:51:
                    3b:3d:94:08:64:26:61:7d:b9:c3:65:8f:14:9c:21:
                    d0:aa:fd:17:72:03:8f:bd:9b:8c:e6:5e:53:9e:b9:
                    9d:ef:82:bb:e1:bc:e2:72:41:5b:21:94:d3:45:37:
                    94:d1:df:09:39:5d:e7:23:aa:9a:1d:ca:6d:a8:0a:
                    86:85:8a:82:be:42:07:d6:f2:38:82:73:da:87:5b:
                    e5:3c:d3:9e:3e:a7:3b:9e:f4:03:b3:f9:f1:7d:13:
                    74:02:ff:bb:a1:e5:fa:00:79:1c:a6:66:41:88:5c:
                    60:57:a6:2e:09:c4:ba:fd:9a:cf:a7:1f:40:c3:bb:
                    cc:5a:0a:55:4b:3b:38:76:51:b8:63:8b:84:94:16:
                    e6:56:f3
                Exponent: 65537 (0x10001)
        X509v3 extensions:
            X509v3 Basic Constraints: critical
                CA:TRUE
            X509v3 Key Usage: critical
                Certificate Sign, CRL Sign
            X509v3 Subject Key Identifier: 
                FE:AB:00:90:98:9E:24:FC:A9:CC:1A:8A:FB:27:B8:BF:30:6E:A8:3B
    Signature Algorithm: sha256WithRSAEncryption
         cf:77:2c:6e:56:be:4e:b3:b6:84:00:94:ab:47:c9:0d:d2:76:
         c7:86:9f:1d:07:d3:b6:b4:bb:08:78:af:69:d2:0b:49:de:33:
         c5:ac:ad:c2:88:02:7d:06:b7:35:02:c1:60:c9:bf:c4:e8:94:
         de:d4:d3:a9:13:25:5a:fe:6e:a2:ae:7d:05:dc:7d:f3:6c:f0:
         7e:a6:8d:ee:d9:d7:ce:58:17:e8:a9:29:ae:73:48:87:e7:9b:
         ca:6e:29:a1:64:5f:19:13:f7:ae:06:10:ff:51:c6:9b:4d:55:
         25:4f:93:99:10:01:53:75:f1:13:ce:c7:a6:41:41:d2:bf:88:
         a5:7f:45:fc:ac:b8:a5:b5:33:0c:82:c4:fb:07:f6:6a:e5:25:
         84:5f:06:ca:c1:86:39:11:db:58:cd:77:3b:2c:c2:4c:0f:5e:
         9a:e3:f0:ab:3e:61:1b:50:24:c2:c0:f4:f1:19:f0:11:29:b6:
         a5:18:02:9b:d7:63:4c:70:8c:47:a3:03:43:5c:b9:5d:46:a0:
         0d:6f:ff:59:8e:be:dd:9f:72:c3:5b:2b:df:8c:5b:ce:e5:0c:
         46:6c:92:b2:0a:a3:4c:54:42:18:15:12:18:bd:da:fc:ba:74:
         6e:ff:c1:b6:a0:64:d8:a9:5f:55:ae:9f:5c:6a:76:96:d8:73:
         67:87:fb:4d:7f:5c:ee:69:ca:73:10:fb:8a:a9:fd:9e:bd:36:
         38:49:49:87:f4:0e:14:f0:e9:87:b8:3f:a7:4f:7a:5a:8e:79:
         d4:93:e4:bb:68:52:84:ac:6c:e9:f3:98:70:55:72:32:f9:34:
         ab:2b:49:b5:cd:20:62:e4:3a:7a:67:63:ab:96:dc:6d:ae:97:
         ec:fc:9f:76:56:88:2e:66:cf:5b:b6:c9:a4:b0:d7:05:ba:e1:
         27:2f:93:bb:26:2a:a2:93:b0:1b:f3:8e:be:1d:40:a3:b9:36:
         8f:3e:82:1a:1a:5e:88:ea:50:f8:59:e2:83:46:29:0b:e3:44:
         5c:e1:95:b6:69:90:9a:14:6f:97:ae:81:cf:68:ef:99:9a:be:
         b5:e7:e1:7f:f8:fa:13:47:16:4c:cc:6d:08:40:e7:8b:78:6f:
         50:82:44:50:3f:66:06:8a:ab:43:84:56:4a:0f:20:2d:86:0e:
         f5:d2:db:d2:7a:8a:4b:cd:a5:e8:4e:f1:5e:26:25:01:59:23:
         a0:7e:d2:f6:7e:21:57:d7:27:bc:15:57:4c:a4:46:c1:e0:83:
         1e:0c:4c:4d:1f:4f:06:19:e2:f9:a8:f4:3a:82:a1:b2:79:43:
         79:d6:ad:6f:7a:27:90:03:a4:ea:24:87:3f:d9:bd:d9:e9:f2:
         5f:50:49:1c:ee:ec:d7:2e
SHA1 Fingerprint=76:E2:7E:C1:4F:DB:82:C1:C0:A6:75:B5:05:BE:3D:29:B4:ED:DB:BB
