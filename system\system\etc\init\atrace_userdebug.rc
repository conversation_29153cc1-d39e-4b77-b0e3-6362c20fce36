## Permissions to allow additional system-wide tracing to the kernel trace buffer.
## The default list of permissions is set in frameworks/native/cmds/atrace/atrace.rc

# Grant unix world read/write permissions to enable kernel tracepoints.
# Access control to these files is now entirely in selinux policy.

on post-fs
    chmod 0666 /sys/kernel/tracing/events/workqueue/enable
    chmod 0666 /sys/kernel/debug/tracing/events/workqueue/enable
    chmod 0666 /sys/kernel/tracing/events/regulator/enable
    chmod 0666 /sys/kernel/debug/tracing/events/regulator/enable
    chmod 0666 /sys/kernel/tracing/events/pagecache/enable
    chmod 0666 /sys/kernel/debug/tracing/events/pagecache/enable

    # irq
    chmod 0666 /sys/kernel/tracing/events/irq/enable
    chmod 0666 /sys/kernel/debug/tracing/events/irq/enable
    chmod 0666 /sys/kernel/tracing/events/ipi/enable
    chmod 0666 /sys/kernel/debug/tracing/events/ipi/enable
