# List of effect libraries to load. Each library element must contain a "path" element
# giving the full path of the library .so file.
#    libraries {
#        <lib name> {
#          path <lib path>
#        }
#    }
libraries {
# This is a proxy library that will be an abstraction for
# the HW and SW effects

  #proxy {
    #path /vendor/lib/soundfx/libeffectproxy.so
  #}

# This is the SW implementation library of the effect
  #libSW {
    #path /vendor/lib/soundfx/libswwrapper.so
  #}

# This is the HW implementation library for the effect
  #libHW {
    #path /vendor/lib/soundfx/libhwwrapper.so
  #}

  bundle {
    path /vendor/lib/soundfx/libbundlewrapper.so
  }
  reverb {
    path /vendor/lib/soundfx/libreverbwrapper.so
  }
  visualizer {
    path /vendor/lib/soundfx/libvisualizer.so
  }
  downmix {
    path /vendor/lib/soundfx/libdownmix.so
  }
  loudness_enhancer {
    path /vendor/lib/soundfx/libldnhncr.so
  }
  dynamics_processing {
    path /vendor/lib/soundfx/libdynproc.so
  }
}

# Default pre-processing library. Add to audio_effect.conf "libraries" section if
# audio HAL implements support for default software audio pre-processing effects
#
#  pre_processing {
#    path /vendor/lib/soundfx/libaudiopreprocessing.so
#  }

# list of effects to load. Each effect element must contain a "library" and a "uuid" element.
# The value of the "library" element must correspond to the name of one library element in the
# "libraries" element.
# The name of the effect element is indicative, only the value of the "uuid" element
# designates the effect.
# The uuid is the implementation specific UUID as specified by the effect vendor. This is not the
# generic effect type UUID.
#    effects {
#        <fx name> {
#            library <lib name>
#            uuid <effect uuid>
#        }
#        ...
#    }

effects {

# additions for the proxy implementation
# Proxy implementation
  #effectname {
    #library proxy
    #uuid  xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx

    # SW implemetation of the effect. Added as a node under the proxy to
    # indicate this as a sub effect.
      #libsw {
         #library libSW
         #uuid  yyyyyyyy-yyyy-yyyy-yyyy-yyyyyyyyyyyy
      #} End of SW effect

    # HW implementation of the effect. Added as a node under the proxy to
    # indicate this as a sub effect.
      #libhw {
         #library libHW
         #uuid  zzzzzzzz-zzzz-zzzz-zzzz-zzzzzzzzzzzz
      #}End of HW effect
  #} End of effect proxy

  bassboost {
    library bundle
    uuid 8631f300-72e2-11df-b57e-0002a5d5c51b
  }
  virtualizer {
    library bundle
    uuid 1d4033c0-8557-11df-9f2d-0002a5d5c51b
  }
  equalizer {
    library bundle
    uuid ce772f20-847d-11df-bb17-0002a5d5c51b
  }
  volume {
    library bundle
    uuid 119341a0-8469-11df-81f9-0002a5d5c51b
  }
  reverb_env_aux {
    library reverb
    uuid 4a387fc0-8ab3-11df-8bad-0002a5d5c51b
  }
  reverb_env_ins {
    library reverb
    uuid c7a511a0-a3bb-11df-860e-0002a5d5c51b
  }
  reverb_pre_aux {
    library reverb
    uuid f29a1400-a3bb-11df-8ddc-0002a5d5c51b
  }
  reverb_pre_ins {
    library reverb
    uuid 172cdf00-a3bc-11df-a72f-0002a5d5c51b
  }
  visualizer {
    library visualizer
    uuid d069d9e0-8329-11df-9168-0002a5d5c51b
  }
  downmix {
    library downmix
    uuid 93f04452-e4fe-41cc-91f9-e475b6d1d69f
  }
  loudness_enhancer {
    library loudness_enhancer
    uuid fa415329-2034-4bea-b5dc-5b381c8d1e2c
  }
  dynamics_processing {
    library dynamics_processing
    uuid e0e6539b-1781-7261-676f-6d7573696340
  }
}

# Default pre-processing effects. Add to audio_effect.conf "effects" section if
# audio HAL implements support for them.
#
#  agc {
#    library pre_processing
#    uuid aa8130e0-66fc-11e0-bad0-0002a5d5c51b
#  }
#  aec {
#    library pre_processing
#    uuid bb392ec0-8d4d-11e0-a896-0002a5d5c51b
#  }
#  ns {
#    library pre_processing
#    uuid c06c8400-8e06-11e0-9cb6-0002a5d5c51b
#  }

# Audio preprocessor configurations.
# The pre processor configuration consists in a list of elements each describing
# pre processor settings for a given input source. Valid input source names are:
# "mic", "camcorder", "voice_recognition", "voice_communication"
# Each input source element contains a list of effects elements. The name of the effect
# element must be the name of one of the effects in the "effects" list of the file.
# Each effect element may optionally contain a list of parameters and their
# default value to apply when the pre processor effect is created.
# A parameter is defined by a "param" element and a "value" element. Each of these elements
# consists in one or more elements specifying a type followed by a value.
# The types defined are: "int", "short", "float", "bool" and "string"
# When both "param" and "value" are a single int, a simple form is allowed where just
# the param and value pair is present in the parameter description
#    pre_processing {
#        <input source name> {
#            <fx name> {
#                <param 1 name> {
#                    param {
#                        int|short|float|bool|string <value>
#                        [ int|short|float|bool|string <value> ]
#                        ...
#                    }
#                    value {
#                        int|short|float|bool|string <value>
#                        [ int|short|float|bool|string <value> ]
#                        ...
#                    }
#                }
#                <param 2 name > {<param> <value>}
#                ...
#            }
#            ...
#        }
#        ...
#    }

#
# TODO: add default audio pre processor configurations after debug and tuning phase
#
