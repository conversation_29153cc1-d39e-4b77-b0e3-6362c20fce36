-----<PERSON><PERSON><PERSON> CERTIFICATE-----
MIIFZjCCA06gAwIBAgIQCgFCgAAAAUUjz0Z8AAAAAjANBgkqhkiG9w0BAQsFADBN
MQswCQYDVQQGEwJVUzESMBAGA1UEChMJSWRlblRydXN0MSowKAYDVQQDEyFJZGVu
VHJ1c3QgUHVibGljIFNlY3RvciBSb290IENBIDEwHhcNMTQwMTE2MTc1MzMyWhcN
MzQwMTE2MTc1MzMyWjBNMQswCQYDVQQGEwJVUzESMBAGA1UEChMJSWRlblRydXN0
MSowKAYDVQQDEyFJZGVuVHJ1c3QgUHVibGljIFNlY3RvciBSb290IENBIDEwggIi
MA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoICAQC2IpT8pEiv6EdrCvsnduTyP4o7
ekosMSqMjbCpwzFrqHd2hCa2rIFCDQjrVVi7evi8ZX3yoG2LqEfpYnYeEe4IFNGy
RBb06tD6Hi9e28tzQa68ALBKK0CyrOE7S8ItneShm+waOh7wCLPQ5CQ1B5+ctMlS
bdsHyo+1W/CD80/HLaXIrcuVIKQxKFdYWuSNG5qrng0M8gozOSI5Cpcu81N3uURF
/YTLNiCBWS2ab21ISGHKTN9T0a9SvESfqy9rg3LvdYDaBjMbXcjaY8ZNzaxmMc3R
3j6HEDbhuaR672BQssvKplbgN6+rNBM5Jeg5ZuSYeqoSmJxZZoY+rfGwyj4GD3vw
EUs3oERte8uojHH01bWRNszwFcYr3lEXsZdMUD2xlVl8BX0tIdUAvwFnol57plzy
9yLxkA2T26pEUWbMfXYD62qoKjgZl3YNa4ph+bz27nb9cCvdKTz4Ch5bQhyLVi9V
GxyhLrXHFub4qjySjmm2AcG1hp2JDws4lFTo6tyePSW8Uybt1as5qsVATFSrsrTZ
2fjXctscvG29ZV/viDUqZi/u9rNl8DONfJhBaUYPQxxp+pu10GFqzcpL2UyQRqsV
WaFHVCkugyhfHMKiq3IXAAaOReyL4jM9f9oZRORicsPfIsbyVtTdX5Vy7W1f90gD
W/3FKqD2cyOEEBsB5wIDAQABo0IwQDAOBgNVHQ8BAf8EBAMCAQYwDwYDVR0TAQH/
BAUwAwEB/zAdBgNVHQ4EFgQU43HgntinQtnbcZFrlJPrw6PRFKMwDQYJKoZIhvcN
AQELBQADggIBAEf63QqwEZE4rU1d9+UOl1QZgkiHVIyqZJnYWv6IAcVYpZmxI1Qj
t2odIFflAWJBF9MJ23XLblSQdf4an4EKwt3X9wnQW3IV5B4Jaj0z8yGa5hV+rVHV
DRDtfULAj+7AmgjVQdZcDiFpboBhDhXAuM/FSRJSzL46zNQuOAXeNf0fb7iAaJg9
TaDKQGXSc3z1i9kKlT/YPyNtGtEqJBnZhbMX73huqVjRI9PHE+1yJX9dsXNw0H8G
lwmEKYBhHfpe/3OsoOOJuBxxFcbeMX8S3OFtm6/n6J91eEyrRjuazr8FGF1NFTwW
mhlQBJqymm9li1JfPFgEKCXAZmExfrngdbkaqIHWchezxQMxNRF4eKLg6TCMf4Df
WN88uieW4oA0beOY02QnrEh+KHdcxiVhJfiFDGX6xDIvpZgF5PgLZxYWxoK4Mhn5
+bl53B/N66+rDt0b20XkeucC4pVd/GnwU2lhlXV5C15V5jgclKlZM57IcXR5f1GJ
tshquDDIajjDbp7hNxbqBWJMWxJH7ae0s1hWx0nzfxJoCTFx8G34Tkf71oXuxVhA
GaQdp/lLQzfcaFpPz+vCZHTetBXZ9FRUGi8c15dxVJCO2SCdUyt/q4/i6jC8UDfv
8Ue1fXwsBOxonbRJRBD0ckscZOf85muQ3Wl9af0AVqW3rLatt8o+Ae+c
-----END CERTIFICATE-----
Certificate:
    Data:
        Version: 3 (0x2)
        Serial Number:
            0a:01:42:80:00:00:01:45:23:cf:46:7c:00:00:00:02
    Signature Algorithm: sha256WithRSAEncryption
        Issuer: C=US, O=IdenTrust, CN=IdenTrust Public Sector Root CA 1
        Validity
            Not Before: Jan 16 17:53:32 2014 GMT
            Not After : Jan 16 17:53:32 2034 GMT
        Subject: C=US, O=IdenTrust, CN=IdenTrust Public Sector Root CA 1
        Subject Public Key Info:
            Public Key Algorithm: rsaEncryption
                Public-Key: (4096 bit)
                Modulus:
                    00:b6:22:94:fc:a4:48:af:e8:47:6b:0a:fb:27:76:
                    e4:f2:3f:8a:3b:7a:4a:2c:31:2a:8c:8d:b0:a9:c3:
                    31:6b:a8:77:76:84:26:b6:ac:81:42:0d:08:eb:55:
                    58:bb:7a:f8:bc:65:7d:f2:a0:6d:8b:a8:47:e9:62:
                    76:1e:11:ee:08:14:d1:b2:44:16:f4:ea:d0:fa:1e:
                    2f:5e:db:cb:73:41:ae:bc:00:b0:4a:2b:40:b2:ac:
                    e1:3b:4b:c2:2d:9d:e4:a1:9b:ec:1a:3a:1e:f0:08:
                    b3:d0:e4:24:35:07:9f:9c:b4:c9:52:6d:db:07:ca:
                    8f:b5:5b:f0:83:f3:4f:c7:2d:a5:c8:ad:cb:95:20:
                    a4:31:28:57:58:5a:e4:8d:1b:9a:ab:9e:0d:0c:f2:
                    0a:33:39:22:39:0a:97:2e:f3:53:77:b9:44:45:fd:
                    84:cb:36:20:81:59:2d:9a:6f:6d:48:48:61:ca:4c:
                    df:53:d1:af:52:bc:44:9f:ab:2f:6b:83:72:ef:75:
                    80:da:06:33:1b:5d:c8:da:63:c6:4d:cd:ac:66:31:
                    cd:d1:de:3e:87:10:36:e1:b9:a4:7a:ef:60:50:b2:
                    cb:ca:a6:56:e0:37:af:ab:34:13:39:25:e8:39:66:
                    e4:98:7a:aa:12:98:9c:59:66:86:3e:ad:f1:b0:ca:
                    3e:06:0f:7b:f0:11:4b:37:a0:44:6d:7b:cb:a8:8c:
                    71:f4:d5:b5:91:36:cc:f0:15:c6:2b:de:51:17:b1:
                    97:4c:50:3d:b1:95:59:7c:05:7d:2d:21:d5:00:bf:
                    01:67:a2:5e:7b:a6:5c:f2:f7:22:f1:90:0d:93:db:
                    aa:44:51:66:cc:7d:76:03:eb:6a:a8:2a:38:19:97:
                    76:0d:6b:8a:61:f9:bc:f6:ee:76:fd:70:2b:dd:29:
                    3c:f8:0a:1e:5b:42:1c:8b:56:2f:55:1b:1c:a1:2e:
                    b5:c7:16:e6:f8:aa:3c:92:8e:69:b6:01:c1:b5:86:
                    9d:89:0f:0b:38:94:54:e8:ea:dc:9e:3d:25:bc:53:
                    26:ed:d5:ab:39:aa:c5:40:4c:54:ab:b2:b4:d9:d9:
                    f8:d7:72:db:1c:bc:6d:bd:65:5f:ef:88:35:2a:66:
                    2f:ee:f6:b3:65:f0:33:8d:7c:98:41:69:46:0f:43:
                    1c:69:fa:9b:b5:d0:61:6a:cd:ca:4b:d9:4c:90:46:
                    ab:15:59:a1:47:54:29:2e:83:28:5f:1c:c2:a2:ab:
                    72:17:00:06:8e:45:ec:8b:e2:33:3d:7f:da:19:44:
                    e4:62:72:c3:df:22:c6:f2:56:d4:dd:5f:95:72:ed:
                    6d:5f:f7:48:03:5b:fd:c5:2a:a0:f6:73:23:84:10:
                    1b:01:e7
                Exponent: 65537 (0x10001)
        X509v3 extensions:
            X509v3 Key Usage: critical
                Certificate Sign, CRL Sign
            X509v3 Basic Constraints: critical
                CA:TRUE
            X509v3 Subject Key Identifier: 
                E3:71:E0:9E:D8:A7:42:D9:DB:71:91:6B:94:93:EB:C3:A3:D1:14:A3
    Signature Algorithm: sha256WithRSAEncryption
         47:fa:dd:0a:b0:11:91:38:ad:4d:5d:f7:e5:0e:97:54:19:82:
         48:87:54:8c:aa:64:99:d8:5a:fe:88:01:c5:58:a5:99:b1:23:
         54:23:b7:6a:1d:20:57:e5:01:62:41:17:d3:09:db:75:cb:6e:
         54:90:75:fe:1a:9f:81:0a:c2:dd:d7:f7:09:d0:5b:72:15:e4:
         1e:09:6a:3d:33:f3:21:9a:e6:15:7e:ad:51:d5:0d:10:ed:7d:
         42:c0:8f:ee:c0:9a:08:d5:41:d6:5c:0e:21:69:6e:80:61:0e:
         15:c0:b8:cf:c5:49:12:52:cc:be:3a:cc:d4:2e:38:05:de:35:
         fd:1f:6f:b8:80:68:98:3d:4d:a0:ca:40:65:d2:73:7c:f5:8b:
         d9:0a:95:3f:d8:3f:23:6d:1a:d1:2a:24:19:d9:85:b3:17:ef:
         78:6e:a9:58:d1:23:d3:c7:13:ed:72:25:7f:5d:b1:73:70:d0:
         7f:06:97:09:84:29:80:61:1d:fa:5e:ff:73:ac:a0:e3:89:b8:
         1c:71:15:c6:de:31:7f:12:dc:e1:6d:9b:af:e7:e8:9f:75:78:
         4c:ab:46:3b:9a:ce:bf:05:18:5d:4d:15:3c:16:9a:19:50:04:
         9a:b2:9a:6f:65:8b:52:5f:3c:58:04:28:25:c0:66:61:31:7e:
         b9:e0:75:b9:1a:a8:81:d6:72:17:b3:c5:03:31:35:11:78:78:
         a2:e0:e9:30:8c:7f:80:df:58:df:3c:ba:27:96:e2:80:34:6d:
         e3:98:d3:64:27:ac:48:7e:28:77:5c:c6:25:61:25:f8:85:0c:
         65:fa:c4:32:2f:a5:98:05:e4:f8:0b:67:16:16:c6:82:b8:32:
         19:f9:f9:b9:79:dc:1f:cd:eb:af:ab:0e:dd:1b:db:45:e4:7a:
         e7:02:e2:95:5d:fc:69:f0:53:69:61:95:75:79:0b:5e:55:e6:
         38:1c:94:a9:59:33:9e:c8:71:74:79:7f:51:89:b6:c8:6a:b8:
         30:c8:6a:38:c3:6e:9e:e1:37:16:ea:05:62:4c:5b:12:47:ed:
         a7:b4:b3:58:56:c7:49:f3:7f:12:68:09:31:71:f0:6d:f8:4e:
         47:fb:d6:85:ee:c5:58:40:19:a4:1d:a7:f9:4b:43:37:dc:68:
         5a:4f:cf:eb:c2:64:74:de:b4:15:d9:f4:54:54:1a:2f:1c:d7:
         97:71:54:90:8e:d9:20:9d:53:2b:7f:ab:8f:e2:ea:30:bc:50:
         37:ef:f1:47:b5:7d:7c:2c:04:ec:68:9d:b4:49:44:10:f4:72:
         4b:1c:64:e7:fc:e6:6b:90:dd:69:7d:69:fd:00:56:a5:b7:ac:
         b6:ad:b7:ca:3e:01:ef:9c
SHA1 Fingerprint=BA:29:41:60:77:98:3F:F4:F3:EF:F2:31:05:3B:2E:EA:6D:4D:45:FD
