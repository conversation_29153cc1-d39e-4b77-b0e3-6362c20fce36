#
# Copyright (C) 2017 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
#
#
# Dirty-image-objects file for boot image.
#
# Objects in this file are known dirty at runtime. Current this includes:
#   - classes with known dirty static fields.
#
# The image writer will bin these objects together in the image.
#
# This file can be generated using imgdiag with a command such as:
#   adb shell imgdiag --image-diff-pid=<app pid> --zygote-diff-pid=<zygote pid> \
#     --boot-image=/system/framework/boot.art --dump-dirty-objects
# Then, grep for lines containing "Private dirty object" from the output.
# This particular file was generated by dumping systemserver and systemui.
#
java.lang.System
java.net.Inet4Address
java.lang.Thread
java.lang.Throwable
java.util.Collections
javax.net.ssl.SSLContext
java.nio.charset.Charset
java.security.Provider
javax.net.ssl.HttpsURLConnection
javax.net.ssl.SSLSocketFactory
java.util.TimeZone
java.util.Locale
java.util.function.ToIntFunction
sun.misc.FormattedFloatingDecimal
java.util.stream.IntStream
android.icu.util.TimeZone
libcore.io.DropBox
org.apache.harmony.luni.internal.util.TimezoneGetter
dalvik.system.SocketTagger
dalvik.system.CloseGuard
java.lang.ref.FinalizerReference
com.android.org.conscrypt.ct.CTLogStoreImpl
com.android.org.conscrypt.SSLParametersImpl
com.android.org.conscrypt.OpenSSLContextImpl
com.android.org.conscrypt.SSLParametersImpl$AliasChooser
com.android.org.conscrypt.SSLParametersImpl$PSKCallbacks
com.android.org.conscrypt.NativeCrypto$SSLHandshakeCallbacks
com.android.okhttp.OkHttpClient
com.android.okhttp.okio.SegmentPool
com.android.okhttp.okio.AsyncTimeout
com.android.okhttp.HttpUrl
android.os.StrictMode
com.android.internal.os.BinderInternal
android.os.storage.StorageManager
android.os.Trace
android.app.ActivityManager
android.media.MediaRouter
android.os.Environment
android.view.ThreadedRenderer
android.media.AudioManager
android.app.AlarmManager
android.telephony.TelephonyManager
android.bluetooth.BluetoothAdapter
com.android.internal.os.SomeArgs
android.os.LocaleList
android.view.WindowManagerGlobal
android.media.AudioSystem
android.ddm.DdmHandleAppName
android.provider.Settings
android.view.ViewRootImpl
android.net.ConnectivityManager
android.app.ActivityThread
android.os.BaseBundle
android.util.ArraySet
android.view.View
android.os.ServiceManager
android.view.ViewTreeObserver
android.hardware.input.InputManager
android.os.UEventObserver
android.app.NotificationManager
android.hardware.display.DisplayManagerGlobal
android.os.Binder
android.app.AppOpsManager
android.content.ContentResolver
android.app.backup.BackupManager
android.util.ArrayMap
android.os.Looper
android.graphics.Bitmap
android.view.textservice.TextServicesManager
com.android.internal.inputmethod.InputMethodUtils
android.app.QueuedWork
android.graphics.TemporaryBuffer
android.widget.ImageView
android.database.sqlite.SQLiteGlobal
android.view.autofill.Helper
android.text.method.SingleLineTransformationMethod
com.android.internal.os.RuntimeInit
android.view.inputmethod.InputMethodManager
android.hardware.SystemSensorManager
android.database.CursorWindow
android.text.TextUtils
android.media.PlayerBase
android.app.ResourcesManager
android.os.Message
android.view.accessibility.AccessibilityManager
android.app.Notification
android.provider.ContactsContract$ContactNameColumns
android.provider.CalendarContract$EventsColumns
android.provider.CalendarContract$CalendarColumns
android.provider.CalendarContract$SyncColumns
android.provider.ContactsContract$ContactsColumns
android.content.pm.PackageManager$OnPermissionsChangedListener
android.net.IpConfiguration$ProxySettings
android.provider.ContactsContract$ContactOptionsColumns
android.net.wifi.SupplicantState
android.provider.ContactsContract$ContactStatusColumns
android.view.accessibility.AccessibilityManager$TouchExplorationStateChangeListener
android.provider.CalendarContract$CalendarSyncColumns
android.bluetooth.BluetoothProfile$ServiceListener
android.provider.ContactsContract$ContactCounts
android.net.IpConfiguration$IpAssignment
android.text.TextWatcher
android.graphics.Bitmap$CompressFormat
android.location.LocationListener
sun.security.jca.Providers
java.lang.CharSequence
android.icu.util.ULocale
dalvik.system.BaseDexClassLoader
android.icu.text.BreakIterator
libcore.io.EventLogger
libcore.net.NetworkSecurityPolicy
android.icu.text.UnicodeSet
com.android.org.conscrypt.TrustedCertificateStore$PreloadHolder
android.app.SearchManager
android.os.Build
android.app.ContextImpl
android.app.WallpaperManager
android.security.net.config.ApplicationConfig
android.animation.LayoutTransition
android.widget.TextView
com.android.internal.logging.MetricsLogger
android.renderscript.RenderScriptCacheDir
android.os.Process
android.os.Handler
android.content.Context
android.graphics.drawable.AdaptiveIconDrawable
android.provider.FontsContract
android.text.style.SuggestionSpan
android.graphics.drawable.VectorDrawable$VGroup
android.view.ViewStub
android.text.style.MetricAffectingSpan
android.content.SharedPreferences$OnSharedPreferenceChangeListener
android.app.PendingIntent
android.text.SpanWatcher
android.widget.FrameLayout
android.net.NetworkRequest$Type
android.net.NetworkInfo$State
android.graphics.drawable.GradientDrawable
android.text.style.AlignmentSpan
android.widget.LinearLayout
android.text.style.CharacterStyle
android.view.View$OnApplyWindowInsetsListener
android.view.MenuItem
android.text.style.ReplacementSpan
android.graphics.drawable.Icon
android.widget.Button
