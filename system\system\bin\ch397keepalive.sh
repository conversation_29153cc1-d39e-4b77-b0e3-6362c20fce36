#! /bin/sh

echo "ch397keepalive begin!"

usbpath=""
usbnode=""
usbdevpath='/sys/bus/usb/devices/'
usbdriverpath='/sys/bus/usb/drivers/'

new_value=""
last_value=""

while [ true ]
do
    new_value=$(ifconfig -a eth1 | grep "RX bytes" | sed 's/:/ /g'  | awk -Fbytes '{print $2}')
    echo  "new: $new_value\n"
    echo  "old: $last_value\n"

	if [ "$new_value" = "$last_value" ]
    then
        echo "ch397keepalive detects network rx bytes error!"
        for file in /sys/bus/usb/drivers/usb_ch397/*
        do
            if [ -d "$file" ]
            then 
                usbpath=${file##*/}
                usbpath=${usbpath%:*}
                idVendor=$usbdevpath$usbpath'/idVendor'

                if [ ! -f "$idVendor" ]
                then
                    continue
                fi

                echo "ch397keepalive re-load driver now!"
                usbnode=${file##*/}
                echo $usbnode > /sys/bus/usb/drivers/usb_ch397/unbind
                echo $usbnode > /sys/bus/usb/drivers/usb_ch397/bind

                last_value=0

            fi
        done
    fi

    last_value=$new_value

	sleep 10
done
