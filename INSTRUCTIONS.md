# 创建 system.img 的完整指南

## 🔧 方法一：使用 make_ext4fs.exe（推荐）

### 1. 下载工具
从以下任一来源下载 `make_ext4fs.exe`：

**选项A：从Android Kitchen**
- 下载 Android Kitchen 或 SuperR's Kitchen
- 从工具包中提取 `make_ext4fs.exe`

**选项B：从XDA论坛**
- 搜索 "make_ext4fs windows binary"
- 下载编译好的版本

**选项C：从GitHub项目**
- https://github.com/anpaza/make_ext4fs
- https://github.com/jamflux/URTool (包含工具)

### 2. 使用方法
将 `make_ext4fs.exe` 放在项目根目录，然后运行：
```cmd
build_system_correct.bat
```

## 🔧 方法二：使用 WSL（Windows子系统）

### 1. 安装WSL
```cmd
wsl --install
```

### 2. 安装工具
在WSL中运行：
```bash
sudo apt update
sudo apt install e2fsprogs
```

### 3. 创建镜像
```bash
# 参数来自 system_info.txt
BLOCK_COUNT=393216
BLOCK_SIZE=4096
INODE_COUNT=98304
SIZE_BYTES=$((BLOCK_COUNT * BLOCK_SIZE))

# 创建空镜像
dd if=/dev/zero of=system.img bs=$BLOCK_SIZE count=$BLOCK_COUNT

# 格式化为ext4
mke2fs -t ext4 -F -b $BLOCK_SIZE -N $INODE_COUNT system.img

# 挂载并复制文件
mkdir -p /tmp/system_mount
sudo mount -o loop system.img /tmp/system_mount
sudo cp -a system/* /tmp/system_mount/
sudo umount /tmp/system_mount
rmdir /tmp/system_mount
```

## 🔧 方法三：使用在线工具

### Android Image Kitchen
- 下载完整的 Android Image Kitchen
- 包含所有必要的工具
- 支持Windows/Linux/Mac

## 📋 当前系统配置

你的系统已经正确配置了：

✅ **启动器应用**: `system/system/app/korgu/korgutv.apk`
✅ **原启动器已禁用**: `dangbei.apk.bak`
✅ **系统属性已更新**: `build.prop` 中的启动器配置
✅ **文件权限配置**: `system_fs_config.txt`
✅ **SELinux配置**: `system_file_contexts.txt`

## 🎯 预期结果

创建的 `system.img` 将包含：
- 完整的Android 9系统
- com.kozyax.korgutv 作为默认启动器
- 所有原有的系统应用和服务
- 正确的文件权限和SELinux上下文

## ⚠️ 重要提示

1. **备份原始镜像** - 在刷机前确保有原始system.img的备份
2. **测试环境** - 建议先在测试设备上验证
3. **兼容性** - 确保新启动器与系统版本兼容
4. **权限检查** - 确认所有文件权限正确设置

## 🚀 快速开始

如果你想快速开始，推荐顺序：

1. **尝试WSL方法**（如果已安装WSL）
2. **下载make_ext4fs.exe**（最可靠）
3. **使用Android Kitchen**（包含完整工具链）

选择其中一种方法，按照步骤操作即可创建可用的system.img文件。
