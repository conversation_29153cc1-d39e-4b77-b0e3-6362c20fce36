-----<PERSON><PERSON><PERSON> CERTIFICATE-----
MIIFODCCAyCgAwIBAgIRAJW+FqD3LkbxezmCcvqLzZYwDQYJKoZIhvcNAQEFBQAw
NzEUMBIGA1UECgwLVGVsaWFTb25lcmExHzAdBgNVBAMMFlRlbGlhU29uZXJhIFJv
b3QgQ0EgdjEwHhcNMDcxMDE4MTIwMDUwWhcNMzIxMDE4MTIwMDUwWjA3MRQwEgYD
VQQKDAtUZWxpYVNvbmVyYTEfMB0GA1UEAwwWVGVsaWFTb25lcmEgUm9vdCBDQSB2
MTCCAiIwDQYJKoZIhvcNAQEBBQADggIPADCCAgoCggIBAMK+6yfwIaPzaSZVfp3F
VRaRXP3vIb9TgHot0pGMYzHw7CTww6XScnwQbfQ3t+XmfHnqjLWCi65ItqwA3GV1
7CpNX8GH9SBlK4GoRz6JI5UwFpB/6FcHSOcZrr9FZ7E3GwYq/t75rH2D+1665I+X
Z75Ljo1kB1c4VWk0Nj0TSO9P4tNmHqTPGrdeNjPUtAa9GAH9d4RQAEX1jF3oI7x+
/jXh7VB7qTCNGdMJjmhnXb88lxhTuylixcpecsHHltTbLaC0H2kD7OriUPEMPPCs
81Mt8Bz17Ww5OXOAFshSsCPN4D7c3TxHoLs1iuKYaIu+5b9y7tL6pe0S7fyYGKkm
dtwoSxAgHNN/Fnct7W+A90m7UwW7XWjH1Mh1Fj+JWov3F0fUTPHSiXk+TT2YqGHe
Oh7S+F4D4MHJHIzTjU3TlTazN19jY5szFPAtJmtTfImMMsJu7D0hADnJoWjiUIMu
sDor8zagrC/kb2HCUQk5PotTubtn2txTuXZZNp1D5SDgPTJghSJRt8czu90VL6R4
pgd7gUY2BIbdeTXHlSw7sKMXNeVzH7RcWe/a6hBle3rQf5+ztCo3O3CLm1u5K7fs
slESl1MpWtTwEhDcTwK7EpIvYtQ/aUN8Ddb8WHUBiJ1YFkveupD/RwGJBmr2X7KQ
arMCpgKIv7NHfirZ1fpoeDVNAgMBAAGjPzA9MA8GA1UdEwEB/wQFMAMBAf8wCwYD
VR0PBAQDAgEGMB0GA1UdDgQWBBTwj1k4ALP1j5qWDNXr+nuqF+gTEjANBgkqhkiG
9w0BAQUFAAOCAgEAvuRcYk4k9AwI//DTDGjkk0kiP0Qnb7tt3oNmzqjMDfz1mgbl
dxSR651Be5kqhOX//CHBXfDkH1e3damhXwIm/9fH907eT/j3HEbAek9ALCI18Bmx
0GtnLLCo4MBANzX2hFxc469CeP6nyQ1Q6g2EdvZR74NTxnr/DlZJLo961gzmJ1Tj
TQpgcmLNkQfWpb/ImWvtxBnmq0wROMVvMeJuScg/doAmAyYp4Db29iBT4xdwNBed
Y2gea+zDTYa4EzAvXUYNR0PVG6pZDrlcjQZIrXSHX8f8MVRBE+LHIQ6e4B4N4cB7
Q4WQxYpYxmUKeFfyxiMPAdkgS94P+5KFdSpcc41teyWRyu5FrgZLAMzTsVlQ2jqI
OylDRl6XK1TOU2+NSueW+r9xDkKLfP0ooNBIytrEgUy7onOTJsjrDNYmiLbAJM+7
vVvrdX3pCI6GMyx5dwlppYn8s3CQh3aP0yK7Qs69cwsgJirQmz1wHiRszYd2qReW
t88NkvuOGKmYSdGe/mBEciG5Ge3C9THxOUiIkCR1VBatzvT4aRRkOfujuLpwQMcn
HL/EVlP6Y2XQ8xwOFvVrhlhNGNTkDY6lnVuR3HYkUD/GKvvZt5y11ubQ2egZixVx
SK236thZiNSQvxaz2emsWWFUyBy6ysHK4bkgTI86k4mloMy/0/Z1pHWWbVY=
-----END CERTIFICATE-----
Certificate:
    Data:
        Version: 3 (0x2)
        Serial Number:
            95:be:16:a0:f7:2e:46:f1:7b:39:82:72:fa:8b:cd:96
    Signature Algorithm: sha1WithRSAEncryption
        Issuer: O=TeliaSonera, CN=TeliaSonera Root CA v1
        Validity
            Not Before: Oct 18 12:00:50 2007 GMT
            Not After : Oct 18 12:00:50 2032 GMT
        Subject: O=TeliaSonera, CN=TeliaSonera Root CA v1
        Subject Public Key Info:
            Public Key Algorithm: rsaEncryption
                Public-Key: (4096 bit)
                Modulus:
                    00:c2:be:eb:27:f0:21:a3:f3:69:26:55:7e:9d:c5:
                    55:16:91:5c:fd:ef:21:bf:53:80:7a:2d:d2:91:8c:
                    63:31:f0:ec:24:f0:c3:a5:d2:72:7c:10:6d:f4:37:
                    b7:e5:e6:7c:79:ea:8c:b5:82:8b:ae:48:b6:ac:00:
                    dc:65:75:ec:2a:4d:5f:c1:87:f5:20:65:2b:81:a8:
                    47:3e:89:23:95:30:16:90:7f:e8:57:07:48:e7:19:
                    ae:bf:45:67:b1:37:1b:06:2a:fe:de:f9:ac:7d:83:
                    fb:5e:ba:e4:8f:97:67:be:4b:8e:8d:64:07:57:38:
                    55:69:34:36:3d:13:48:ef:4f:e2:d3:66:1e:a4:cf:
                    1a:b7:5e:36:33:d4:b4:06:bd:18:01:fd:77:84:50:
                    00:45:f5:8c:5d:e8:23:bc:7e:fe:35:e1:ed:50:7b:
                    a9:30:8d:19:d3:09:8e:68:67:5d:bf:3c:97:18:53:
                    bb:29:62:c5:ca:5e:72:c1:c7:96:d4:db:2d:a0:b4:
                    1f:69:03:ec:ea:e2:50:f1:0c:3c:f0:ac:f3:53:2d:
                    f0:1c:f5:ed:6c:39:39:73:80:16:c8:52:b0:23:cd:
                    e0:3e:dc:dd:3c:47:a0:bb:35:8a:e2:98:68:8b:be:
                    e5:bf:72:ee:d2:fa:a5:ed:12:ed:fc:98:18:a9:26:
                    76:dc:28:4b:10:20:1c:d3:7f:16:77:2d:ed:6f:80:
                    f7:49:bb:53:05:bb:5d:68:c7:d4:c8:75:16:3f:89:
                    5a:8b:f7:17:47:d4:4c:f1:d2:89:79:3e:4d:3d:98:
                    a8:61:de:3a:1e:d2:f8:5e:03:e0:c1:c9:1c:8c:d3:
                    8d:4d:d3:95:36:b3:37:5f:63:63:9b:33:14:f0:2d:
                    26:6b:53:7c:89:8c:32:c2:6e:ec:3d:21:00:39:c9:
                    a1:68:e2:50:83:2e:b0:3a:2b:f3:36:a0:ac:2f:e4:
                    6f:61:c2:51:09:39:3e:8b:53:b9:bb:67:da:dc:53:
                    b9:76:59:36:9d:43:e5:20:e0:3d:32:60:85:22:51:
                    b7:c7:33:bb:dd:15:2f:a4:78:a6:07:7b:81:46:36:
                    04:86:dd:79:35:c7:95:2c:3b:b0:a3:17:35:e5:73:
                    1f:b4:5c:59:ef:da:ea:10:65:7b:7a:d0:7f:9f:b3:
                    b4:2a:37:3b:70:8b:9b:5b:b9:2b:b7:ec:b2:51:12:
                    97:53:29:5a:d4:f0:12:10:dc:4f:02:bb:12:92:2f:
                    62:d4:3f:69:43:7c:0d:d6:fc:58:75:01:88:9d:58:
                    16:4b:de:ba:90:ff:47:01:89:06:6a:f6:5f:b2:90:
                    6a:b3:02:a6:02:88:bf:b3:47:7e:2a:d9:d5:fa:68:
                    78:35:4d
                Exponent: 65537 (0x10001)
        X509v3 extensions:
            X509v3 Basic Constraints: critical
                CA:TRUE
            X509v3 Key Usage: 
                Certificate Sign, CRL Sign
            X509v3 Subject Key Identifier: 
                F0:8F:59:38:00:B3:F5:8F:9A:96:0C:D5:EB:FA:7B:AA:17:E8:13:12
    Signature Algorithm: sha1WithRSAEncryption
         be:e4:5c:62:4e:24:f4:0c:08:ff:f0:d3:0c:68:e4:93:49:22:
         3f:44:27:6f:bb:6d:de:83:66:ce:a8:cc:0d:fc:f5:9a:06:e5:
         77:14:91:eb:9d:41:7b:99:2a:84:e5:ff:fc:21:c1:5d:f0:e4:
         1f:57:b7:75:a9:a1:5f:02:26:ff:d7:c7:f7:4e:de:4f:f8:f7:
         1c:46:c0:7a:4f:40:2c:22:35:f0:19:b1:d0:6b:67:2c:b0:a8:
         e0:c0:40:37:35:f6:84:5c:5c:e3:af:42:78:fe:a7:c9:0d:50:
         ea:0d:84:76:f6:51:ef:83:53:c6:7a:ff:0e:56:49:2e:8f:7a:
         d6:0c:e6:27:54:e3:4d:0a:60:72:62:cd:91:07:d6:a5:bf:c8:
         99:6b:ed:c4:19:e6:ab:4c:11:38:c5:6f:31:e2:6e:49:c8:3f:
         76:80:26:03:26:29:e0:36:f6:f6:20:53:e3:17:70:34:17:9d:
         63:68:1e:6b:ec:c3:4d:86:b8:13:30:2f:5d:46:0d:47:43:d5:
         1b:aa:59:0e:b9:5c:8d:06:48:ad:74:87:5f:c7:fc:31:54:41:
         13:e2:c7:21:0e:9e:e0:1e:0d:e1:c0:7b:43:85:90:c5:8a:58:
         c6:65:0a:78:57:f2:c6:23:0f:01:d9:20:4b:de:0f:fb:92:85:
         75:2a:5c:73:8d:6d:7b:25:91:ca:ee:45:ae:06:4b:00:cc:d3:
         b1:59:50:da:3a:88:3b:29:43:46:5e:97:2b:54:ce:53:6f:8d:
         4a:e7:96:fa:bf:71:0e:42:8b:7c:fd:28:a0:d0:48:ca:da:c4:
         81:4c:bb:a2:73:93:26:c8:eb:0c:d6:26:88:b6:c0:24:cf:bb:
         bd:5b:eb:75:7d:e9:08:8e:86:33:2c:79:77:09:69:a5:89:fc:
         b3:70:90:87:76:8f:d3:22:bb:42:ce:bd:73:0b:20:26:2a:d0:
         9b:3d:70:1e:24:6c:cd:87:76:a9:17:96:b7:cf:0d:92:fb:8e:
         18:a9:98:49:d1:9e:fe:60:44:72:21:b9:19:ed:c2:f5:31:f1:
         39:48:88:90:24:75:54:16:ad:ce:f4:f8:69:14:64:39:fb:a3:
         b8:ba:70:40:c7:27:1c:bf:c4:56:53:fa:63:65:d0:f3:1c:0e:
         16:f5:6b:86:58:4d:18:d4:e4:0d:8e:a5:9d:5b:91:dc:76:24:
         50:3f:c6:2a:fb:d9:b7:9c:b5:d6:e6:d0:d9:e8:19:8b:15:71:
         48:ad:b7:ea:d8:59:88:d4:90:bf:16:b3:d9:e9:ac:59:61:54:
         c8:1c:ba:ca:c1:ca:e1:b9:20:4c:8f:3a:93:89:a5:a0:cc:bf:
         d3:f6:75:a4:75:96:6d:56
SHA1 Fingerprint=43:13:BB:96:F1:D5:86:9B:C1:4E:6A:92:F6:CF:F6:34:69:87:82:37
