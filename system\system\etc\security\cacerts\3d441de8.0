-----<PERSON><PERSON><PERSON> CERTIFICATE-----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==
-----END CERTIFICATE-----
Certificate:
    Data:
        Version: 3 (0x2)
        Serial Number: 10000012 (0x98968c)
    Signature Algorithm: sha256WithRSAEncryption
        Issuer: C=NL, O=Staat der Nederlanden, CN=Staat der Nederlanden Root CA - G2
        Validity
            Not Before: Mar 26 11:18:17 2008 GMT
            Not After : Mar 25 11:03:10 2020 GMT
        Subject: C=NL, O=Staat der Nederlanden, CN=Staat der Nederlanden Root CA - G2
        Subject Public Key Info:
            Public Key Algorithm: rsaEncryption
                Public-Key: (4096 bit)
                Modulus:
                    00:c5:59:e7:6f:75:aa:3e:4b:9c:b5:b8:ac:9e:0b:
                    e4:f9:d9:ca:ab:5d:8f:b5:39:10:82:d7:af:51:e0:
                    3b:e1:00:48:6a:cf:da:e1:06:43:11:99:aa:14:25:
                    12:ad:22:e8:00:6d:43:c4:a9:b8:e5:1f:89:4b:67:
                    bd:61:48:ef:fd:d2:e0:60:88:e5:b9:18:60:28:c3:
                    77:2b:ad:b0:37:aa:37:de:64:59:2a:46:57:e4:4b:
                    b9:f8:37:7c:d5:36:e7:80:c1:b6:f3:d4:67:9b:96:
                    e8:ce:d7:c6:0a:53:d0:6b:49:96:f3:a3:0b:05:77:
                    48:f7:25:e5:70:ac:30:14:20:25:e3:7f:75:5a:e5:
                    48:f8:4e:7b:03:07:04:fa:82:61:87:6e:f0:3b:c4:
                    a4:c7:d0:f5:74:3e:a5:5d:1a:08:f2:9b:25:d2:f6:
                    ac:04:26:3e:55:3a:62:28:a5:7b:b2:30:af:f8:37:
                    c2:d1:ba:d6:38:fd:f4:ef:49:30:37:99:26:21:48:
                    85:01:a9:e5:16:e7:dc:90:55:df:0f:e8:38:cd:99:
                    37:21:4f:5d:f5:22:6f:6a:c5:12:16:60:17:55:f2:
                    65:66:a6:a7:30:91:38:c1:38:1d:86:04:84:ba:1a:
                    25:78:5e:9d:af:cc:50:60:d6:13:87:52:ed:63:1f:
                    6d:65:7d:c2:15:18:74:ca:e1:7e:64:29:8c:72:d8:
                    16:13:7d:0b:49:4a:f1:28:1b:20:74:6b:c5:3d:dd:
                    b0:aa:48:09:3d:2e:82:94:cd:1a:65:d9:2b:88:9a:
                    99:bc:18:7e:9f:ee:7d:66:7c:3e:bd:94:b8:81:ce:
                    cd:98:30:78:c1:6f:67:d0:be:5f:e0:68:ed:de:e2:
                    b1:c9:2c:59:78:92:aa:df:2b:60:63:f2:e5:5e:b9:
                    e3:ca:fa:7f:50:86:3e:a2:34:18:0c:09:68:28:11:
                    1c:e4:e1:b9:5c:3e:47:ba:32:3f:18:cc:5b:84:f5:
                    f3:6b:74:c4:72:74:e1:e3:8b:a0:4a:bd:8d:66:2f:
                    ea:ad:35:da:20:d3:88:82:61:f0:12:22:b6:bc:d0:
                    d5:a4:ec:af:54:88:25:24:3c:a7:6d:b1:72:29:3f:
                    3e:57:a6:7f:55:af:6e:26:c6:fe:e7:cc:40:5c:51:
                    44:81:0a:78:de:4a:ce:55:bf:1d:d5:d9:b7:56:ef:
                    f0:76:ff:0b:79:b5:af:bd:fb:a9:69:91:46:97:68:
                    80:14:36:1d:b3:7f:bb:29:98:36:a5:20:fa:82:60:
                    62:33:a4:ec:d6:ba:07:a7:6e:c5:cf:14:a6:e7:d6:
                    92:34:d8:81:f5:fc:1d:5d:aa:5c:1e:f6:a3:4d:3b:
                    b8:f7:39
                Exponent: 65537 (0x10001)
        X509v3 extensions:
            X509v3 Basic Constraints: critical
                CA:TRUE
            X509v3 Certificate Policies: 
                Policy: X509v3 Any Policy
                  CPS: http://www.pkioverheid.nl/policies/root-policy-G2

            X509v3 Key Usage: critical
                Certificate Sign, CRL Sign
            X509v3 Subject Key Identifier: 
                91:68:32:87:15:1D:89:E2:B5:F1:AC:36:28:34:8D:0B:7C:62:88:EB
    Signature Algorithm: sha256WithRSAEncryption
         a8:41:4a:67:2a:92:81:82:50:6e:e1:d7:d8:b3:39:3b:f3:02:
         15:09:50:51:ef:2d:bd:24:7b:88:86:3b:f9:b4:bc:92:09:96:
         b9:f6:c0:ab:23:60:06:79:8c:11:4e:51:d2:79:80:33:fb:9d:
         48:be:ec:41:43:81:1f:7e:47:40:1c:e5:7a:08:ca:aa:8b:75:
         ad:14:c4:c2:e8:66:3c:82:07:a7:e6:27:82:5b:18:e6:0f:6e:
         d9:50:3e:8a:42:18:29:c6:b4:56:fc:56:10:a0:05:17:bd:0c:
         23:7f:f4:93:ed:9c:1a:51:be:dd:45:41:bf:91:24:b4:1f:8c:
         e9:5f:cf:7b:21:99:9f:95:9f:39:3a:46:1c:6c:f9:cd:7b:9c:
         90:cd:28:a9:c7:a9:55:bb:ac:62:34:62:35:13:4b:14:3a:55:
         83:b9:86:8d:92:a6:c6:f4:07:25:54:cc:16:57:12:4a:82:78:
         c8:14:d9:17:82:26:2d:5d:20:1f:79:ae:fe:d4:70:16:16:95:
         83:d8:35:39:ff:52:5d:75:1c:16:c5:13:55:cf:47:cc:75:65:
         52:4a:de:f0:b0:a7:e4:0a:96:0b:fb:ad:c2:e2:25:84:b2:dd:
         e4:bd:7e:59:6c:9b:f0:f0:d8:e7:ca:f2:e9:97:38:7e:89:be:
         cc:fb:39:17:61:3f:72:db:3a:91:d8:65:01:19:1d:ad:50:a4:
         57:0a:7c:4b:bc:9c:71:73:2a:45:51:19:85:cc:8e:fd:47:a7:
         74:95:1d:a8:d1:af:4e:17:b1:69:26:c2:aa:78:57:5b:c5:4d:
         a7:e5:9e:05:17:94:ca:b2:5f:a0:49:18:8d:34:e9:26:6c:48:
         1e:aa:68:92:05:e1:82:73:5a:9b:dc:07:5b:08:6d:7d:9d:d7:
         8d:21:d9:fc:14:20:aa:c2:45:df:3f:e7:00:b2:51:e4:c2:f8:
         05:b9:79:1a:8c:34:f3:9e:5b:e4:37:5b:6b:4a:df:2c:57:8a:
         40:5a:36:ba:dd:75:44:08:37:42:70:0c:fe:dc:5e:21:a0:a3:
         8a:c0:90:9c:68:da:50:e6:45:10:47:78:b6:4e:d2:65:c9:c3:
         37:df:e1:42:63:b0:57:37:45:2d:7b:8a:9c:bf:05:ea:65:55:
         33:f7:39:10:c5:28:2a:21:7a:1b:8a:c4:24:f9:3f:15:c8:9a:
         15:20:f5:55:62:96:ed:6d:93:50:bc:e4:aa:78:ad:d9:cb:0a:
         65:87:a6:66:c1:c4:81:a3:77:3a:58:1e:0b:ee:83:8b:9d:1e:
         d2:52:a4:cc:1d:6f:b0:98:6d:94:31:b5:f8:71:0a:dc:b9:fc:
         7d:32:60:e6:eb:af:8a:01
SHA1 Fingerprint=59:AF:82:79:91:86:C7:B4:75:07:CB:CF:03:57:46:EB:04:DD:B7:16
