% title: Hyphenation patterns for modern and medieval Latin
% copyright: Copyright (c) 1999-2016 <PERSON>
%                                    e-mail claudio dot beccari at gmail dot com
% notice: This file is part of the hyph-utf8 package.
%     See http://www.hyphenation.org for more information.
% language:
%     name: Latin
%     tag: la
% version: 3.201 2016-08-28
% licence:
%     - This file is available under the following licence:
%         name: MIT
%         url: https://opensource.org/licenses/MIT
%         text: >
%             Permission is hereby granted, free of charge, to any person
%             obtaining a copy of this software and associated documentation
%             files (the “Software”), to deal in the Software without
%             restriction, including without limitation the rights to use,
%             copy, modify, merge, publish, distribute, sublicense, and/or sell
%             copies of the Software, and to permit persons to whom the
%             Software is furnished to do so, subject to the following
%             conditions:
%
%             The above copyright notice and this permission notice shall be
%             included in all copies or substantial portions of the Software.
%
%             THE SOFTWARE IS PROVIDED “AS IS”, WITHOUT WARRANTY OF ANY KIND,
%             EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
%             OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
%             NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
%             HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
%             WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
%             FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
%             OTHER DEALINGS IN THE SOFTWARE.
% changes:
%     -
%         date: 1999
%         version: 1.0
%         author: Claudio Beccari
%         description: First public release
%     -
%         date: 2007-04-16
%         version: 3.1
%         author: Claudio Beccari
%     -
%         date: 2010-05-31
%         author: Claudio Beccari
%         description: Removal of OT1 support
%     -
%         date: 2010-06-01
%         version: 3.2
%         author: Claudio Beccari
%         description: Removal of pattern 2'2
%     -
%         date: 2016-08-28
%         version: 3.201
%         author: Claudio Beccari
%         description: updated header with MIT licence notice;
%                   added few missing patterns
%
% ==========================================
% Patterns for the latin language mainly in modern spelling
% (u when u is needed and v when v is needed); medieval spelling
% with the ligatures \ae and \oe  and the (uncial) lowercase `v'
% written as a `u' is also supported; apparently there is no conflict
% between the patterns of modern  Latin and those of medieval Latin.
%
% For more information please read the babel-latin documentation.
%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%
%  For documentation see:
%  C. Beccari, "Computer aided hyphenation for Italian and Modern
%        Latin", TUG vol. 13, n. 1, pp. 23-33 (1992)
%
%  see also
%
%  C. Beccari, "Typesetting of ancient languages",
%              TUG vol.15, n.1, pp. 9-16 (1994)
%
%  In the former paper  the  code  was  described  as  being contained in file
%  ITALAT.TEX; this is substantially the same code,  but  the  file  has  been
%  renamed and included in hyph-utf8.
%
%  A corresponding file (ITHYPH.TEX) has been extracted in order to  eliminate
%  the  (few)  patterns specific to Latin and leave those specific to Italian;
%  ITHYPH.TEX has been further  extended  with  many  new patterns in order to
%  cope with the many neologisms and technical terms with foreign roots.
%
%  Should you find any word that gets hyphenated in a wrong way, please, AFTER
%  CHECKING  ON A RELIABLE MODERN DICTIONARY, report to the author, preferably
%  by e-mail.  Please  do  not  report  about  wrong  break  points concerning
%  prefixes and/or suffixes; see at the bottom of this file.
%
%  Compared with the previous versions, this file has been extended so  as  to
%  cope also with the medieval Latin spelling, where the letter `V' played the
%  roles of both `U' and `V', as in the Roman times, save that the Romans used
%  only capitals. In the middle ages the availability of soft writing supports
%  and the necessity of copying books with a reasonable speed, several scripts
%  evolved  in  (practically)  all  of  which  there was a lower case alphabet
%  different from the upper case  one,  and  where  the lower case `v' had the
%  rounded shape of our modern lower case `u', and where the Latin  diphthongs
%  `AE'  and  `OE',  both in upper and lower case, where written as ligatures,
%  not to mention the habit of  substituting  them with their sound, that is a
%  simple `E'.
%
%  According  to  Leon  Battista  Alberti,  who  in  1466  wrote  a  book   on
%  cryptography  where  he  thoroughly  analyzed  the hyphenation of the Latin
%  language of his (still  medieval)  times,  the  differences from the Tuscan
%  language (the Italian language, as it was named  at  his  time)  were  very
%  limited,  in particular for what concerns the handling of the ascending and
%  descending diphthongs; in  Central  and  Northern  Europe,  and later on in
%  North America, the Scholars perceived the above diphthongs as made  of  two
%  distinct  vowels;  the  hyphenation of medieval Latin, therefore, was quite
%  different in the northern countries compared to the southern ones, at least
%  for what concerns these  diphthongs.  If  you need hyphenation patterns for
%  medieval Latin that suite you better according to the  habits  of  Northern
%  Europe  you  should  resort  to the hyphenation patterns prepared by Yannis
%  Haralambous (TUGboat, vol.13 n.4 (1992)).
%
%
%
%                            PREFIXES AND SUFFIXES
%
% For what concerns prefixes and suffixes, the latter are generally  separated
% according  to  "natural"  syllabification,  while  the  former are generally
% divided etimologically. In order to  avoid  an excessive number of patterns,
% care has been paid to some prefixes,  especially  "ex",  "trans",  "circum",
% "prae",  but  this set of patterns is NOT capable of separating the prefixes
% in all circumstances.
%
%                         BABEL SHORTCUTS AND FACILITIES
%
% Read  the  documentation  coming  with the discription of the Latin language
% interface of  Babel  in  order  to  see  the  shortcuts  and  the facilities
% introduced in order to facilitate the insertion  of  "compound  word  marks"
% which are very useful for inserting etymological break points.
%
% Happy Latin and multilingual typesetting!
%
