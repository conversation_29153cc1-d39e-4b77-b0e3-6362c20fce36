-----B<PERSON><PERSON> CERTIFICATE-----
MIIFuzCCA6OgAwIBAgIIVwoRl0LE48wwDQYJKoZIhvcNAQELBQAwazELMAkGA1UE
BhMCSVQxDjAMBgNVBAcMBU1pbGFuMSMwIQYDVQQKDBpBY3RhbGlzIFMucC5BLi8w
MzM1ODUyMDk2NzEnMCUGA1UEAwweQWN0YWxpcyBBdXRoZW50aWNhdGlvbiBSb290
IENBMB4XDTExMDkyMjExMjIwMloXDTMwMDkyMjExMjIwMlowazELMAkGA1UEBhMC
SVQxDjAMBgNVBAcMBU1pbGFuMSMwIQYDVQQKDBpBY3RhbGlzIFMucC5BLi8wMzM1
ODUyMDk2NzEnMCUGA1UEAwweQWN0YWxpcyBBdXRoZW50aWNhdGlvbiBSb290IENB
MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAp8bEpSmkLO/lGMWwUKNv
UTufClrJwkg4CsIcoBh/kbWHuUA/3R1oHwiD1S0eiKD4j1aPbZkCkpAW1V8IbInX
4ay8IMKx4INRimlNAJZaby/ARH6jDuSRzVju3PvHHkVH3Se5CAGfpiEd9UEtL0z9
KK3giq0itFZljoZUj5NDKd45RnijMCO6zfB9E1fAXdKDa0hMxKufgFpbOr3JpyI/
gCczWw63igxdBzcIy2zSekciRDXFzMwujt0q7bd9Zg1fYVEiVRvjRuPjPdA1Yprb
rxTIW6HMiRvhMCb8oJsfgadHHwTrozmSBp+Z07/T6k9QnBn+locePGX2oxgkg4YQ
51Q+qDp2JE+BIcXjDwL4k5RHILv+1A7TaLndxHqEguNTVHnd25zS8gebLra8Pu2F
be8lEfKXGkJh90qX6IuxEAf6ZYGyojnP9zz/GPvG8VqLWeICrHuS0E4UT1lF9gxe
KF+w6D9Fz8+vm2/7hNN3WpVvrJSEnu68wEqPSpP4RCHiMUVhUE4Q2OM1fEwZtN4F
v6MGn8i1zeQf1xcGDXqVdFUNaBr8EBtiZJ1t4JWgw5QHVw0U5r0F+7if5t+L4sbn
fpb2U8WANFAoWPASUHEXMLrmeGO89LKtmyuy/uE5jF66CyCU3nuDuP/jVo23Eek7
jPKxwV2dpAtMK9myGPW1n0sCAwEAAaNjMGEwHQYDVR0OBBYEFFLYiDrIn3hm7Ynz
ezhwlMkCAjbQMA8GA1UdEwEB/wQFMAMBAf8wHwYDVR0jBBgwFoAUUtiIOsifeGbt
ifN7OHCUyQICNtAwDgYDVR0PAQH/BAQDAgEGMA0GCSqGSIb3DQEBCwUAA4ICAQAL
e3KHwGCmSUyIWOYdiPcUZEim2FgKDk8TNd81HdTtBjHIgT5q1d07GjLukD0R0i70
jsNjLiNmsGe+b7bAEzlgqqI0JZN1Ut6nna0Oh4lScWoWPBkdg/iaKWW+9D+a2fDz
WochcYBNy+A4mz+7+uAwTc+G02UQGRjRlwKxK3JCaKygvU5a2hi/a5iB0P2avl4V
SM0RFbnAKVy06Ij3Pjaut2L9HmLecHgQHEhb2rykOLpn7VU+Xlff1ANATIGk0k9j
pwlCCRT8AKnCgHNPLsBA2RF7SOp6AsDT6ygBJlh0wcBzIm2Tlf05fbsq4/aC4yyX
X04fkZT6/iyj2HYauE2yOE+b+h1IYHkm4vP9qdCa6HCPSXrW5b0KDtst842/6+Ok
fcvHlXHo2qN8xcL4dJIEG4aspCJTQLas/kx2z/uUMsA1n3Y/buWQbqCmJqK4LL7R
K4X9p2jIugErsWx0Hbhzlefut8cl8ABMALJ+tguLHPPAUJ4lueAI3jZm/zel0btU
ZCzJJ7VLkn5l/9Mt4blOvH+kQSGQQXemOR/qnuOf0GZvBeyqdn6/axag67XH/JJU
LysRJyU3eExRarDzzFhdFPFqSBX/wge2sY0PjlxQRrM9vwGYT7JZVEc+NHt4bVaT
LnPqZih4zR0Uv6CPLy64Lo7yFIrM6bV8+2ydDKXhlg==
-----END CERTIFICATE-----
Certificate:
    Data:
        Version: 3 (0x2)
        Serial Number: 6271844772424770508 (0x570a119742c4e3cc)
    Signature Algorithm: sha256WithRSAEncryption
        Issuer: C=IT, L=Milan, O=Actalis S.p.A./03358520967, CN=Actalis Authentication Root CA
        Validity
            Not Before: Sep 22 11:22:02 2011 GMT
            Not After : Sep 22 11:22:02 2030 GMT
        Subject: C=IT, L=Milan, O=Actalis S.p.A./03358520967, CN=Actalis Authentication Root CA
        Subject Public Key Info:
            Public Key Algorithm: rsaEncryption
                Public-Key: (4096 bit)
                Modulus:
                    00:a7:c6:c4:a5:29:a4:2c:ef:e5:18:c5:b0:50:a3:
                    6f:51:3b:9f:0a:5a:c9:c2:48:38:0a:c2:1c:a0:18:
                    7f:91:b5:87:b9:40:3f:dd:1d:68:1f:08:83:d5:2d:
                    1e:88:a0:f8:8f:56:8f:6d:99:02:92:90:16:d5:5f:
                    08:6c:89:d7:e1:ac:bc:20:c2:b1:e0:83:51:8a:69:
                    4d:00:96:5a:6f:2f:c0:44:7e:a3:0e:e4:91:cd:58:
                    ee:dc:fb:c7:1e:45:47:dd:27:b9:08:01:9f:a6:21:
                    1d:f5:41:2d:2f:4c:fd:28:ad:e0:8a:ad:22:b4:56:
                    65:8e:86:54:8f:93:43:29:de:39:46:78:a3:30:23:
                    ba:cd:f0:7d:13:57:c0:5d:d2:83:6b:48:4c:c4:ab:
                    9f:80:5a:5b:3a:bd:c9:a7:22:3f:80:27:33:5b:0e:
                    b7:8a:0c:5d:07:37:08:cb:6c:d2:7a:47:22:44:35:
                    c5:cc:cc:2e:8e:dd:2a:ed:b7:7d:66:0d:5f:61:51:
                    22:55:1b:e3:46:e3:e3:3d:d0:35:62:9a:db:af:14:
                    c8:5b:a1:cc:89:1b:e1:30:26:fc:a0:9b:1f:81:a7:
                    47:1f:04:eb:a3:39:92:06:9f:99:d3:bf:d3:ea:4f:
                    50:9c:19:fe:96:87:1e:3c:65:f6:a3:18:24:83:86:
                    10:e7:54:3e:a8:3a:76:24:4f:81:21:c5:e3:0f:02:
                    f8:93:94:47:20:bb:fe:d4:0e:d3:68:b9:dd:c4:7a:
                    84:82:e3:53:54:79:dd:db:9c:d2:f2:07:9b:2e:b6:
                    bc:3e:ed:85:6d:ef:25:11:f2:97:1a:42:61:f7:4a:
                    97:e8:8b:b1:10:07:fa:65:81:b2:a2:39:cf:f7:3c:
                    ff:18:fb:c6:f1:5a:8b:59:e2:02:ac:7b:92:d0:4e:
                    14:4f:59:45:f6:0c:5e:28:5f:b0:e8:3f:45:cf:cf:
                    af:9b:6f:fb:84:d3:77:5a:95:6f:ac:94:84:9e:ee:
                    bc:c0:4a:8f:4a:93:f8:44:21:e2:31:45:61:50:4e:
                    10:d8:e3:35:7c:4c:19:b4:de:05:bf:a3:06:9f:c8:
                    b5:cd:e4:1f:d7:17:06:0d:7a:95:74:55:0d:68:1a:
                    fc:10:1b:62:64:9d:6d:e0:95:a0:c3:94:07:57:0d:
                    14:e6:bd:05:fb:b8:9f:e6:df:8b:e2:c6:e7:7e:96:
                    f6:53:c5:80:34:50:28:58:f0:12:50:71:17:30:ba:
                    e6:78:63:bc:f4:b2:ad:9b:2b:b2:fe:e1:39:8c:5e:
                    ba:0b:20:94:de:7b:83:b8:ff:e3:56:8d:b7:11:e9:
                    3b:8c:f2:b1:c1:5d:9d:a4:0b:4c:2b:d9:b2:18:f5:
                    b5:9f:4b
                Exponent: 65537 (0x10001)
        X509v3 extensions:
            X509v3 Subject Key Identifier: 
                52:D8:88:3A:C8:9F:78:66:ED:89:F3:7B:38:70:94:C9:02:02:36:D0
            X509v3 Basic Constraints: critical
                CA:TRUE
            X509v3 Authority Key Identifier: 
                keyid:52:D8:88:3A:C8:9F:78:66:ED:89:F3:7B:38:70:94:C9:02:02:36:D0

            X509v3 Key Usage: critical
                Certificate Sign, CRL Sign
    Signature Algorithm: sha256WithRSAEncryption
         0b:7b:72:87:c0:60:a6:49:4c:88:58:e6:1d:88:f7:14:64:48:
         a6:d8:58:0a:0e:4f:13:35:df:35:1d:d4:ed:06:31:c8:81:3e:
         6a:d5:dd:3b:1a:32:ee:90:3d:11:d2:2e:f4:8e:c3:63:2e:23:
         66:b0:67:be:6f:b6:c0:13:39:60:aa:a2:34:25:93:75:52:de:
         a7:9d:ad:0e:87:89:52:71:6a:16:3c:19:1d:83:f8:9a:29:65:
         be:f4:3f:9a:d9:f0:f3:5a:87:21:71:80:4d:cb:e0:38:9b:3f:
         bb:fa:e0:30:4d:cf:86:d3:65:10:19:18:d1:97:02:b1:2b:72:
         42:68:ac:a0:bd:4e:5a:da:18:bf:6b:98:81:d0:fd:9a:be:5e:
         15:48:cd:11:15:b9:c0:29:5c:b4:e8:88:f7:3e:36:ae:b7:62:
         fd:1e:62:de:70:78:10:1c:48:5b:da:bc:a4:38:ba:67:ed:55:
         3e:5e:57:df:d4:03:40:4c:81:a4:d2:4f:63:a7:09:42:09:14:
         fc:00:a9:c2:80:73:4f:2e:c0:40:d9:11:7b:48:ea:7a:02:c0:
         d3:eb:28:01:26:58:74:c1:c0:73:22:6d:93:95:fd:39:7d:bb:
         2a:e3:f6:82:e3:2c:97:5f:4e:1f:91:94:fa:fe:2c:a3:d8:76:
         1a:b8:4d:b2:38:4f:9b:fa:1d:48:60:79:26:e2:f3:fd:a9:d0:
         9a:e8:70:8f:49:7a:d6:e5:bd:0a:0e:db:2d:f3:8d:bf:eb:e3:
         a4:7d:cb:c7:95:71:e8:da:a3:7c:c5:c2:f8:74:92:04:1b:86:
         ac:a4:22:53:40:b6:ac:fe:4c:76:cf:fb:94:32:c0:35:9f:76:
         3f:6e:e5:90:6e:a0:a6:26:a2:b8:2c:be:d1:2b:85:fd:a7:68:
         c8:ba:01:2b:b1:6c:74:1d:b8:73:95:e7:ee:b7:c7:25:f0:00:
         4c:00:b2:7e:b6:0b:8b:1c:f3:c0:50:9e:25:b9:e0:08:de:36:
         66:ff:37:a5:d1:bb:54:64:2c:c9:27:b5:4b:92:7e:65:ff:d3:
         2d:e1:b9:4e:bc:7f:a4:41:21:90:41:77:a6:39:1f:ea:9e:e3:
         9f:d0:66:6f:05:ec:aa:76:7e:bf:6b:16:a0:eb:b5:c7:fc:92:
         54:2f:2b:11:27:25:37:78:4c:51:6a:b0:f3:cc:58:5d:14:f1:
         6a:48:15:ff:c2:07:b6:b1:8d:0f:8e:5c:50:46:b3:3d:bf:01:
         98:4f:b2:59:54:47:3e:34:7b:78:6d:56:93:2e:73:ea:66:28:
         78:cd:1d:14:bf:a0:8f:2f:2e:b8:2e:8e:f2:14:8a:cc:e9:b5:
         7c:fb:6c:9d:0c:a5:e1:96
SHA1 Fingerprint=F3:73:B3:87:06:5A:28:84:8A:F2:F3:4A:CE:19:2B:DD:C7:8E:9C:AC
