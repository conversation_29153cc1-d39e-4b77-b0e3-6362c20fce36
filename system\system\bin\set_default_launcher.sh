#!/system/bin/sh

LOG="/system/bin/log -t set_default_launcher"
ECHO()
{
    echo $*
    $LOG "$*"
}

# Wait for system to be ready
sleep 5

# Set default launcher properties
setprop ro.config.default_launcher com.kozyax.korgutv
setprop persist.sys.default_launcher com.kozyax.korgutv

# Clear any existing launcher preferences
pm clear-default-launcher

# Set com.kozyax.korgutv as default launcher
pm set-home-activity com.kozyax.korgutv/.MainActivity

ECHO "Default launcher set to com.kozyax.korgutv"
