-----<PERSON><PERSON><PERSON> CERTIFICATE-----
MIIDdzCCAl+gAwIBAgIBADANBgkqhkiG9w0BAQsFADBdMQswCQYDVQQGEwJKUDEl
MCMGA1UEChMcU0VDT00gVHJ1c3QgU3lzdGVtcyBDTy4sTFRELjEnMCUGA1UECxMe
U2VjdXJpdHkgQ29tbXVuaWNhdGlvbiBSb290Q0EyMB4XDTA5MDUyOTA1MDAzOVoX
DTI5MDUyOTA1MDAzOVowXTELMAkGA1UEBhMCSlAxJTAjBgNVBAoTHFNFQ09NIFRy
dXN0IFN5c3RlbXMgQ08uLExURC4xJzAlBgNVBAsTHlNlY3VyaXR5IENvbW11bmlj
YXRpb24gUm9vdENBMjCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBANAV
OVKxUrO6xVmCxF1SrjpDZYBLx/KWvNs2l9amZIyoXvDjChz335c9S672XewhtUGr
zbl+dp+++T42NKA7wfYxEUV0kz1XgMX5iZnK5atq1LXaQZAQwdbWQonCv/Q4EpVM
VAX3NuRFg3sUZdbcDE3R3n4MqzvEFb46VqZab3ZpUql6ucjrappdUtAtCms1FgkQ
hNBqyjoGADdH5H5XTz+L62e4iKrFvlNVspHEfbmwhRkGeC7bYRr6hfVKkaHnFtWO
ojnflLhwHyg/i/xAXmODPIMqGplrz95Zajv8bxbXH/1KEOtOghY6rCcMU/Gt1SSw
awNQwS08Ft1ENCcadfsCAwEAAaNCMEAwHQYDVR0OBBYEFAqFqXdlBZh8QIH4D5cs
OPEK7DzPMA4GA1UdDwEB/wQEAwIBBjAPBgNVHRMBAf8EBTADAQH/MA0GCSqGSIb3
DQEBCwUAA4IBAQBMOqNErLlFsceTfsgLCkLfZOoc7llsCLqJX2rKSpWeeo8HxdpF
coJxDjrSzG+ntKEju/Ykn8sX/oymzsLS28yN/HH8AynBbF0zX2S2ZTuJbxh2ePXc
okgfGT+Ok+vx+hfuzU7jBBJV1uXk3fs+BXziHV7Gp7yXT2g69ekuCkO2r1dcYmh8
t/2jioSgrGK+KwmHNPBqAbubKVY8/gA3zyNs8U6qtnRGEmyR7jTV7JqR50S+kDFy
1UkC9gLl9B/rfNmWVan/7Ir5mUf/NVoCqgTLiluHcSmRvaS0eg29mvVXIwAHIRc/
SjnRBUkLp7Y3gaVdjKozXoEofKd9J+sAro03
-----END CERTIFICATE-----
Certificate:
    Data:
        Version: 3 (0x2)
        Serial Number: 0 (0x0)
    Signature Algorithm: sha256WithRSAEncryption
        Issuer: C=JP, O=SECOM Trust Systems CO.,LTD., OU=Security Communication RootCA2
        Validity
            Not Before: May 29 05:00:39 2009 GMT
            Not After : May 29 05:00:39 2029 GMT
        Subject: C=JP, O=SECOM Trust Systems CO.,LTD., OU=Security Communication RootCA2
        Subject Public Key Info:
            Public Key Algorithm: rsaEncryption
                Public-Key: (2048 bit)
                Modulus:
                    00:d0:15:39:52:b1:52:b3:ba:c5:59:82:c4:5d:52:
                    ae:3a:43:65:80:4b:c7:f2:96:bc:db:36:97:d6:a6:
                    64:8c:a8:5e:f0:e3:0a:1c:f7:df:97:3d:4b:ae:f6:
                    5d:ec:21:b5:41:ab:cd:b9:7e:76:9f:be:f9:3e:36:
                    34:a0:3b:c1:f6:31:11:45:74:93:3d:57:80:c5:f9:
                    89:99:ca:e5:ab:6a:d4:b5:da:41:90:10:c1:d6:d6:
                    42:89:c2:bf:f4:38:12:95:4c:54:05:f7:36:e4:45:
                    83:7b:14:65:d6:dc:0c:4d:d1:de:7e:0c:ab:3b:c4:
                    15:be:3a:56:a6:5a:6f:76:69:52:a9:7a:b9:c8:eb:
                    6a:9a:5d:52:d0:2d:0a:6b:35:16:09:10:84:d0:6a:
                    ca:3a:06:00:37:47:e4:7e:57:4f:3f:8b:eb:67:b8:
                    88:aa:c5:be:53:55:b2:91:c4:7d:b9:b0:85:19:06:
                    78:2e:db:61:1a:fa:85:f5:4a:91:a1:e7:16:d5:8e:
                    a2:39:df:94:b8:70:1f:28:3f:8b:fc:40:5e:63:83:
                    3c:83:2a:1a:99:6b:cf:de:59:6a:3b:fc:6f:16:d7:
                    1f:fd:4a:10:eb:4e:82:16:3a:ac:27:0c:53:f1:ad:
                    d5:24:b0:6b:03:50:c1:2d:3c:16:dd:44:34:27:1a:
                    75:fb
                Exponent: 65537 (0x10001)
        X509v3 extensions:
            X509v3 Subject Key Identifier: 
                0A:85:A9:77:65:05:98:7C:40:81:F8:0F:97:2C:38:F1:0A:EC:3C:CF
            X509v3 Key Usage: critical
                Certificate Sign, CRL Sign
            X509v3 Basic Constraints: critical
                CA:TRUE
    Signature Algorithm: sha256WithRSAEncryption
         4c:3a:a3:44:ac:b9:45:b1:c7:93:7e:c8:0b:0a:42:df:64:ea:
         1c:ee:59:6c:08:ba:89:5f:6a:ca:4a:95:9e:7a:8f:07:c5:da:
         45:72:82:71:0e:3a:d2:cc:6f:a7:b4:a1:23:bb:f6:24:9f:cb:
         17:fe:8c:a6:ce:c2:d2:db:cc:8d:fc:71:fc:03:29:c1:6c:5d:
         33:5f:64:b6:65:3b:89:6f:18:76:78:f5:dc:a2:48:1f:19:3f:
         8e:93:eb:f1:fa:17:ee:cd:4e:e3:04:12:55:d6:e5:e4:dd:fb:
         3e:05:7c:e2:1d:5e:c6:a7:bc:97:4f:68:3a:f5:e9:2e:0a:43:
         b6:af:57:5c:62:68:7c:b7:fd:a3:8a:84:a0:ac:62:be:2b:09:
         87:34:f0:6a:01:bb:9b:29:56:3c:fe:00:37:cf:23:6c:f1:4e:
         aa:b6:74:46:12:6c:91:ee:34:d5:ec:9a:91:e7:44:be:90:31:
         72:d5:49:02:f6:02:e5:f4:1f:eb:7c:d9:96:55:a9:ff:ec:8a:
         f9:99:47:ff:35:5a:02:aa:04:cb:8a:5b:87:71:29:91:bd:a4:
         b4:7a:0d:bd:9a:f5:57:23:00:07:21:17:3f:4a:39:d1:05:49:
         0b:a7:b6:37:81:a5:5d:8c:aa:33:5e:81:28:7c:a7:7d:27:eb:
         00:ae:8d:37
SHA1 Fingerprint=5F:3B:8C:F2:F8:10:B3:7D:78:B4:CE:EC:19:19:C3:73:34:B9:C7:74
