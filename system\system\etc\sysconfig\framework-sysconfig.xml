<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2017 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<!-- These are configurations that must exist on all Android devices. -->
<config>

    <!-- Broadcast actions that are currently exempted from O+ background
         delivery restrictions. -->
    <allow-implicit-broadcast action="android.intent.action.SIM_STATE_CHANGED" />
    <allow-implicit-broadcast action="android.intent.action.PACKAGE_CHANGED" />
    <allow-implicit-broadcast action="android.intent.action.MEDIA_SCANNER_SCAN_FILE" />
    <allow-implicit-broadcast action="android.media.action.OPEN_AUDIO_EFFECT_CONTROL_SESSION" />
    <allow-implicit-broadcast action="android.media.action.CLOSE_AUDIO_EFFECT_CONTROL_SESSION" />

    <!-- Whitelist of what components are permitted as backup data transports.  The
         'service' attribute here is a flattened ComponentName string. -->
    <backup-transport-whitelisted-service
        service="android/com.android.internal.backup.LocalTransportService" />

    <!-- Whitelist of bundled applications which all handle URLs to their websites by default -->
    <app-link package="com.android.carrierdefaultapp" />
</config>
