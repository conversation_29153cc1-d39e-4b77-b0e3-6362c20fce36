# Copyright (C) 2011 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

#
# XBox 360 USB Controller
#

key 304   BUTTON_A
key 305   BUTTON_B
key 307   BUTTON_X
key 308   BUTTON_Y
key 310   BUTTON_L1
key 311   BUTTON_R1
key 314   BACK
key 315   BUTTON_START
key 316   HOME
key 317   BUTTON_THUMBL
key 318   BUTTON_THUMBR

# Left and right stick.
# The reported value for flat is 128 out of a range from -32767 to 32768, which is absurd.
# This confuses applications that rely on the flat value because the joystick actually
# settles in a flat range of +/- 4096 or so.
axis 0x00 X flat 4096
axis 0x01 Y flat 4096
axis 0x03 Z flat 4096
axis 0x04 RZ flat 4096

# Triggers.
axis 0x02 LTRIGGER
axis 0x05 RTRIGGER

# Hat.
axis 0x10 HAT_X
axis 0x11 HAT_Y
