<!--
    Input:
        compatibility_matrix.legacy.xml
-->
<compatibility-matrix version="1.0" type="framework" level="legacy">
    <hal format="hidl" optional="false">
        <name>android.hardware.audio</name>
        <version>2.0</version>
        <interface>
            <name>IDevicesFactory</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="false">
        <name>android.hardware.audio.effect</name>
        <version>2.0</version>
        <interface>
            <name>IEffectsFactory</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>android.hardware.automotive.evs</name>
        <version>1.0</version>
        <interface>
            <name>IEvsEnumerator</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>android.hardware.automotive.vehicle</name>
        <version>2.0</version>
        <interface>
            <name>IVehicle</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>android.hardware.biometrics.fingerprint</name>
        <version>2.1</version>
        <interface>
            <name>IBiometricsFingerprint</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>android.hardware.bluetooth</name>
        <version>1.0</version>
        <interface>
            <name>IBluetoothHci</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>android.hardware.boot</name>
        <version>1.0</version>
        <interface>
            <name>IBootControl</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>android.hardware.broadcastradio</name>
        <version>1.0</version>
        <interface>
            <name>IBroadcastRadioFactory</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>android.hardware.camera.provider</name>
        <version>2.4</version>
        <interface>
            <name>ICameraProvider</name>
            <instance>legacy/0</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="false">
        <name>android.hardware.configstore</name>
        <version>1.0</version>
        <interface>
            <name>ISurfaceFlingerConfigs</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>android.hardware.contexthub</name>
        <version>1.0</version>
        <interface>
            <name>IContexthub</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="false">
        <name>android.hardware.drm</name>
        <version>1.0</version>
        <interface>
            <name>ICryptoFactory</name>
            <instance>default</instance>
        </interface>
        <interface>
            <name>IDrmFactory</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>android.hardware.dumpstate</name>
        <version>1.0</version>
        <interface>
            <name>IDumpstateDevice</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>android.hardware.gatekeeper</name>
        <version>1.0</version>
        <interface>
            <name>IGatekeeper</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>android.hardware.gnss</name>
        <version>1.0</version>
        <interface>
            <name>IGnss</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="false">
        <name>android.hardware.graphics.allocator</name>
        <version>2.0</version>
        <interface>
            <name>IAllocator</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="false">
        <name>android.hardware.graphics.composer</name>
        <version>2.1</version>
        <interface>
            <name>IComposer</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="false">
        <name>android.hardware.graphics.mapper</name>
        <version>2.0</version>
        <interface>
            <name>IMapper</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>android.hardware.health</name>
        <version>1.0</version>
        <interface>
            <name>IHealth</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>android.hardware.ir</name>
        <version>1.0</version>
    </hal>
    <hal format="hidl" optional="false">
        <name>android.hardware.keymaster</name>
        <version>3.0</version>
        <interface>
            <name>IKeymasterDevice</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>android.hardware.light</name>
        <version>2.0</version>
        <interface>
            <name>ILight</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="false">
        <name>android.hardware.media.omx</name>
        <version>1.0</version>
        <interface>
            <name>IOmx</name>
            <instance>default</instance>
        </interface>
        <interface>
            <name>IOmxStore</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>android.hardware.memtrack</name>
        <version>1.0</version>
        <interface>
            <name>IMemtrack</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>android.hardware.nfc</name>
        <version>1.0</version>
        <interface>
            <name>INfc</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>android.hardware.power</name>
        <version>1.0</version>
        <interface>
            <name>IPower</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>android.hardware.radio</name>
        <version>1.0</version>
        <interface>
            <name>IRadio</name>
            <instance>slot1</instance>
        </interface>
        <interface>
            <name>ISap</name>
            <instance>slot1</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>android.hardware.radio.deprecated</name>
        <version>1.0</version>
        <interface>
            <name>IOemHook</name>
            <instance>slot1</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>android.hardware.renderscript</name>
        <version>1.0</version>
        <interface>
            <name>IDevice</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>android.hardware.sensors</name>
        <version>1.0</version>
        <interface>
            <name>ISensors</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>android.hardware.soundtrigger</name>
        <version>2.0</version>
        <interface>
            <name>ISoundTriggerHw</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>android.hardware.thermal</name>
        <version>1.0</version>
        <interface>
            <name>IThermal</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>android.hardware.tv.cec</name>
        <version>1.0</version>
        <interface>
            <name>IHdmiCec</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>android.hardware.tv.input</name>
        <version>1.0</version>
        <interface>
            <name>ITvInput</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>android.hardware.usb</name>
        <version>1.0</version>
        <interface>
            <name>IUsb</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>android.hardware.vibrator</name>
        <version>1.0</version>
        <interface>
            <name>IVibrator</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>android.hardware.vr</name>
        <version>1.0</version>
        <interface>
            <name>IVr</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>android.hardware.wifi</name>
        <version>1.0</version>
        <interface>
            <name>IWifi</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>android.hardware.wifi.supplicant</name>
        <version>1.0</version>
        <interface>
            <name>ISupplicant</name>
            <instance>default</instance>
        </interface>
    </hal>
    <kernel version="3.18.0">
        <config>
            <key>CONFIG_ANDROID</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_ANDROID_BINDER_DEVICES</key>
            <value type="string">binder,hwbinder,vndbinder</value>
        </config>
        <config>
            <key>CONFIG_ANDROID_BINDER_IPC</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_ANDROID_LOW_MEMORY_KILLER</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_ASHMEM</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_AUDIT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_BLK_DEV_INITRD</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_CGROUPS</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_CGROUP_CPUACCT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_CGROUP_FREEZER</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_CGROUP_SCHED</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_DEFAULT_SECURITY_SELINUX</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_DEVKMEM</key>
            <value type="tristate">n</value>
        </config>
        <config>
            <key>CONFIG_DEVMEM</key>
            <value type="tristate">n</value>
        </config>
        <config>
            <key>CONFIG_EMBEDDED</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_FHANDLE</key>
            <value type="tristate">n</value>
        </config>
        <config>
            <key>CONFIG_HARDENED_USERCOPY</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_HIGH_RES_TIMERS</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IKCONFIG</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IKCONFIG_PROC</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_INET</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_INET6_AH</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_INET6_ESP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_INET6_IPCOMP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_INET_DIAG_DESTROY</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_INET_ESP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_INET_LRO</key>
            <value type="tristate">n</value>
        </config>
        <config>
            <key>CONFIG_INET_XFRM_MODE_TUNNEL</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP6_NF_FILTER</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP6_NF_IPTABLES</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP6_NF_MANGLE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP6_NF_RAW</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP6_NF_TARGET_REJECT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IPV6</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IPV6_MIP6</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IPV6_MULTIPLE_TABLES</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IPV6_OPTIMISTIC_DAD</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IPV6_ROUTER_PREF</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IPV6_ROUTE_INFO</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_ADVANCED_ROUTER</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_MULTICAST</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_MULTIPLE_TABLES</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_ARPFILTER</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_ARPTABLES</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_ARP_MANGLE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_FILTER</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_IPTABLES</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_MANGLE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_MATCH_AH</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_MATCH_ECN</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_MATCH_TTL</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_NAT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_RAW</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_SECURITY</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_TARGET_MASQUERADE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_TARGET_NETMAP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_TARGET_REDIRECT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_TARGET_REJECT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_MODULES</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_MODULE_UNLOAD</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_MODVERSIONS</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NET</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETDEVICES</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_COMMENT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_CONNLIMIT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_CONNMARK</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_CONNTRACK</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_HASHLIMIT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_HELPER</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_IPRANGE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_LENGTH</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_LIMIT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_MAC</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_MARK</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_PKTTYPE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_POLICY</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_QTAGUID</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_QUOTA</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_QUOTA2</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_SOCKET</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_STATE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_STATISTIC</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_STRING</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_TIME</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_U32</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_TARGET_CLASSIFY</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_TARGET_CONNMARK</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_TARGET_CONNSECMARK</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_TARGET_IDLETIMER</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_TARGET_MARK</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_TARGET_NFLOG</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_TARGET_NFQUEUE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_TARGET_SECMARK</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_TARGET_TCPMSS</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_TARGET_TPROXY</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_TARGET_TRACE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NET_CLS_ACT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NET_CLS_U32</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NET_EMATCH</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NET_EMATCH_U32</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NET_KEY</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NET_SCHED</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NET_SCH_HTB</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK_AMANDA</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK_EVENTS</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK_FTP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK_H323</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK_IPV4</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK_IPV6</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK_IRC</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK_NETBIOS_NS</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK_PPTP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK_SANE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK_SECMARK</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK_TFTP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CT_NETLINK</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CT_PROTO_DCCP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CT_PROTO_SCTP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CT_PROTO_UDPLITE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_NAT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NO_HZ</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_OABI_COMPAT</key>
            <value type="tristate">n</value>
        </config>
        <config>
            <key>CONFIG_PACKET</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_PM_AUTOSLEEP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_PM_WAKELOCKS</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_PPP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_PPPOLAC</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_PPPOPNS</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_PPP_BSDCOMP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_PPP_DEFLATE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_PPP_MPPE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_PREEMPT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_RESOURCE_COUNTERS</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_RTC_CLASS</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_RT_GROUP_SCHED</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_SECCOMP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_SECURITY</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_SECURITY_NETWORK</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_SECURITY_PERF_EVENTS_RESTRICT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_SECURITY_SELINUX</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_STAGING</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_SWITCH</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_SYNC</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_SYSVIPC</key>
            <value type="tristate">n</value>
        </config>
        <config>
            <key>CONFIG_TUN</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_UID_SYS_STATS</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_UNIX</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_USB_GADGET</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_USELIB</key>
            <value type="tristate">n</value>
        </config>
        <config>
            <key>CONFIG_XFRM_USER</key>
            <value type="tristate">y</value>
        </config>
    </kernel>
    <kernel version="3.18.0">
        <conditions>
            <config>
                <key>CONFIG_ARM64</key>
                <value type="tristate">y</value>
            </config>
        </conditions>
        <config>
            <key>CONFIG_ARMV8_DEPRECATED</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_CP15_BARRIER_EMULATION</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_SETEND_EMULATION</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_SWP_EMULATION</key>
            <value type="tristate">y</value>
        </config>
    </kernel>
    <kernel version="4.4.0">
        <config>
            <key>CONFIG_ANDROID</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_ANDROID_BINDER_DEVICES</key>
            <value type="string">binder,hwbinder,vndbinder</value>
        </config>
        <config>
            <key>CONFIG_ANDROID_BINDER_IPC</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_ANDROID_LOW_MEMORY_KILLER</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_ASHMEM</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_AUDIT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_BLK_DEV_INITRD</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_CGROUPS</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_CGROUP_CPUACCT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_CGROUP_FREEZER</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_CGROUP_SCHED</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_DEFAULT_SECURITY_SELINUX</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_DEVKMEM</key>
            <value type="tristate">n</value>
        </config>
        <config>
            <key>CONFIG_DEVMEM</key>
            <value type="tristate">n</value>
        </config>
        <config>
            <key>CONFIG_EMBEDDED</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_FHANDLE</key>
            <value type="tristate">n</value>
        </config>
        <config>
            <key>CONFIG_HARDENED_USERCOPY</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_HIGH_RES_TIMERS</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IKCONFIG</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IKCONFIG_PROC</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_INET</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_INET6_AH</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_INET6_ESP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_INET6_IPCOMP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_INET_DIAG_DESTROY</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_INET_ESP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_INET_LRO</key>
            <value type="tristate">n</value>
        </config>
        <config>
            <key>CONFIG_INET_XFRM_MODE_TUNNEL</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP6_NF_FILTER</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP6_NF_IPTABLES</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP6_NF_MANGLE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP6_NF_RAW</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP6_NF_TARGET_REJECT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IPV6</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IPV6_MIP6</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IPV6_MULTIPLE_TABLES</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IPV6_OPTIMISTIC_DAD</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IPV6_ROUTER_PREF</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IPV6_ROUTE_INFO</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_ADVANCED_ROUTER</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_MULTICAST</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_MULTIPLE_TABLES</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_ARPFILTER</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_ARPTABLES</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_ARP_MANGLE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_FILTER</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_IPTABLES</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_MANGLE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_MATCH_AH</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_MATCH_ECN</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_MATCH_TTL</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_NAT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_RAW</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_SECURITY</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_TARGET_MASQUERADE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_TARGET_NETMAP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_TARGET_REDIRECT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_TARGET_REJECT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_MODULES</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_MODULE_UNLOAD</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_MODVERSIONS</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NET</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETDEVICES</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_COMMENT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_CONNLIMIT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_CONNMARK</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_CONNTRACK</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_HASHLIMIT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_HELPER</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_IPRANGE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_LENGTH</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_LIMIT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_MAC</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_MARK</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_PKTTYPE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_POLICY</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_QTAGUID</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_QUOTA</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_QUOTA2</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_SOCKET</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_STATE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_STATISTIC</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_STRING</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_TIME</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_U32</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_TARGET_CLASSIFY</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_TARGET_CONNMARK</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_TARGET_CONNSECMARK</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_TARGET_IDLETIMER</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_TARGET_MARK</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_TARGET_NFLOG</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_TARGET_NFQUEUE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_TARGET_SECMARK</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_TARGET_TCPMSS</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_TARGET_TPROXY</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_TARGET_TRACE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NET_CLS_ACT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NET_CLS_U32</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NET_EMATCH</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NET_EMATCH_U32</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NET_KEY</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NET_SCHED</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NET_SCH_HTB</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK_AMANDA</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK_EVENTS</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK_FTP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK_H323</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK_IPV4</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK_IPV6</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK_IRC</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK_NETBIOS_NS</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK_PPTP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK_SANE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK_SECMARK</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK_TFTP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CT_NETLINK</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CT_PROTO_DCCP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CT_PROTO_SCTP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CT_PROTO_UDPLITE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_NAT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NO_HZ</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_OABI_COMPAT</key>
            <value type="tristate">n</value>
        </config>
        <config>
            <key>CONFIG_PACKET</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_PM_AUTOSLEEP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_PM_WAKELOCKS</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_PPP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_PPPOLAC</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_PPPOPNS</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_PPP_BSDCOMP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_PPP_DEFLATE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_PPP_MPPE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_PREEMPT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_PROFILING</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_RTC_CLASS</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_RT_GROUP_SCHED</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_SECCOMP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_SECURITY</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_SECURITY_NETWORK</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_SECURITY_PERF_EVENTS_RESTRICT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_SECURITY_SELINUX</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_STAGING</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_SYNC</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_SYSVIPC</key>
            <value type="tristate">n</value>
        </config>
        <config>
            <key>CONFIG_TUN</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_UID_SYS_STATS</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_UNIX</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_USB_CONFIGFS</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_USB_CONFIGFS_F_ACC</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_USB_CONFIGFS_F_AUDIO_SRC</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_USB_CONFIGFS_F_FS</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_USB_CONFIGFS_F_MIDI</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_USB_CONFIGFS_F_MTP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_USB_CONFIGFS_F_PTP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_USB_CONFIGFS_UEVENT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_USB_GADGET</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_USELIB</key>
            <value type="tristate">n</value>
        </config>
        <config>
            <key>CONFIG_XFRM_USER</key>
            <value type="tristate">y</value>
        </config>
    </kernel>
    <kernel version="4.4.0">
        <conditions>
            <config>
                <key>CONFIG_ARM64</key>
                <value type="tristate">y</value>
            </config>
        </conditions>
        <config>
            <key>CONFIG_ARMV8_DEPRECATED</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_CP15_BARRIER_EMULATION</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_SETEND_EMULATION</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_SWP_EMULATION</key>
            <value type="tristate">y</value>
        </config>
    </kernel>
    <kernel version="4.9.0">
        <config>
            <key>CONFIG_ANDROID</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_ANDROID_BINDER_DEVICES</key>
            <value type="string">binder,hwbinder,vndbinder</value>
        </config>
        <config>
            <key>CONFIG_ANDROID_BINDER_IPC</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_ANDROID_LOW_MEMORY_KILLER</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_ASHMEM</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_AUDIT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_BLK_DEV_INITRD</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_CGROUPS</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_CGROUP_CPUACCT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_CGROUP_FREEZER</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_CGROUP_SCHED</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_DEFAULT_SECURITY_SELINUX</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_DEVKMEM</key>
            <value type="tristate">n</value>
        </config>
        <config>
            <key>CONFIG_DEVMEM</key>
            <value type="tristate">n</value>
        </config>
        <config>
            <key>CONFIG_EMBEDDED</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_FHANDLE</key>
            <value type="tristate">n</value>
        </config>
        <config>
            <key>CONFIG_HARDENED_USERCOPY</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_HIGH_RES_TIMERS</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IKCONFIG</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IKCONFIG_PROC</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_INET</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_INET6_AH</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_INET6_ESP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_INET6_IPCOMP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_INET_DIAG_DESTROY</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_INET_ESP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_INET_LRO</key>
            <value type="tristate">n</value>
        </config>
        <config>
            <key>CONFIG_INET_XFRM_MODE_TUNNEL</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP6_NF_FILTER</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP6_NF_IPTABLES</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP6_NF_MANGLE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP6_NF_RAW</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP6_NF_TARGET_REJECT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IPV6</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IPV6_MIP6</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IPV6_MULTIPLE_TABLES</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IPV6_OPTIMISTIC_DAD</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IPV6_ROUTER_PREF</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IPV6_ROUTE_INFO</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_ADVANCED_ROUTER</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_MULTICAST</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_MULTIPLE_TABLES</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_ARPFILTER</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_ARPTABLES</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_ARP_MANGLE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_FILTER</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_IPTABLES</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_MANGLE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_MATCH_AH</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_MATCH_ECN</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_MATCH_TTL</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_NAT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_RAW</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_SECURITY</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_TARGET_MASQUERADE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_TARGET_NETMAP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_TARGET_REDIRECT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_IP_NF_TARGET_REJECT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_MODULES</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_MODULE_UNLOAD</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_MODVERSIONS</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NET</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETDEVICES</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_COMMENT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_CONNLIMIT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_CONNMARK</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_CONNTRACK</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_HASHLIMIT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_HELPER</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_IPRANGE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_LENGTH</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_LIMIT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_MAC</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_MARK</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_PKTTYPE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_POLICY</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_QTAGUID</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_QUOTA</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_QUOTA2</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_SOCKET</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_STATE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_STATISTIC</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_STRING</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_TIME</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_MATCH_U32</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_TARGET_CLASSIFY</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_TARGET_CONNMARK</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_TARGET_CONNSECMARK</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_TARGET_IDLETIMER</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_TARGET_MARK</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_TARGET_NFLOG</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_TARGET_NFQUEUE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_TARGET_SECMARK</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_TARGET_TCPMSS</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_TARGET_TPROXY</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NETFILTER_XT_TARGET_TRACE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NET_CLS_ACT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NET_CLS_U32</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NET_EMATCH</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NET_EMATCH_U32</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NET_KEY</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NET_SCHED</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NET_SCH_HTB</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK_AMANDA</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK_EVENTS</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK_FTP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK_H323</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK_IPV4</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK_IPV6</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK_IRC</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK_NETBIOS_NS</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK_PPTP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK_SANE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK_SECMARK</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CONNTRACK_TFTP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CT_NETLINK</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CT_PROTO_DCCP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CT_PROTO_SCTP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_CT_PROTO_UDPLITE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NF_NAT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_NO_HZ</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_OABI_COMPAT</key>
            <value type="tristate">n</value>
        </config>
        <config>
            <key>CONFIG_PACKET</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_PM_AUTOSLEEP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_PM_WAKELOCKS</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_PPP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_PPPOLAC</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_PPPOPNS</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_PPP_BSDCOMP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_PPP_DEFLATE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_PPP_MPPE</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_PREEMPT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_PROFILING</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_RTC_CLASS</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_RT_GROUP_SCHED</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_SECCOMP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_SECURITY</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_SECURITY_NETWORK</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_SECURITY_PERF_EVENTS_RESTRICT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_SECURITY_SELINUX</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_STAGING</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_SYSVIPC</key>
            <value type="tristate">n</value>
        </config>
        <config>
            <key>CONFIG_TUN</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_UID_SYS_STATS</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_UNIX</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_USB_CONFIGFS</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_USB_CONFIGFS_F_ACC</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_USB_CONFIGFS_F_AUDIO_SRC</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_USB_CONFIGFS_F_FS</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_USB_CONFIGFS_F_MIDI</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_USB_CONFIGFS_F_MTP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_USB_CONFIGFS_F_PTP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_USB_CONFIGFS_UEVENT</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_USB_GADGET</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_USELIB</key>
            <value type="tristate">n</value>
        </config>
        <config>
            <key>CONFIG_XFRM_USER</key>
            <value type="tristate">y</value>
        </config>
    </kernel>
    <kernel version="4.9.0">
        <conditions>
            <config>
                <key>CONFIG_ARM64</key>
                <value type="tristate">y</value>
            </config>
        </conditions>
        <config>
            <key>CONFIG_ARMV8_DEPRECATED</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_CP15_BARRIER_EMULATION</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_SETEND_EMULATION</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_SWP_EMULATION</key>
            <value type="tristate">y</value>
        </config>
    </kernel>
</compatibility-matrix>
