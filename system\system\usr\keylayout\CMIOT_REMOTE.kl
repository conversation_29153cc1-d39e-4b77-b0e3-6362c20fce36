# Copyright (C) 2010 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

#
# Generic key layout file for full alphabetic US English PC style external keyboards.
#
# This file is intentionally very generic and is intended to support a broad rang of keyboards.
# Do not edit the generic key layout to support a specific keyboard; instead, create
# a new key layout file with the required keyboard configuration.
#

key 1     BACK
key 2     1
key 3     2
key 4     3
key 5     4
key 6     5
key 7     6
key 8     7
key 9     8
key 10    9
key 11    0
key 12    MINUS
key 13    EQUALS
key 14    DEL
key 15    TAB
key 16    Q
key 17    W
key 18    E
key 19    R
key 20    T
key 21    Y
key 22    U
key 23    I
key 24    O
key 25    P
key 26    LEFT_BRACKET
key 27    RIGHT_BRACKET
key 28    DPAD_CENTER
key 29    CTRL_LEFT
key 30    A
key 31    S
key 32    D
key 33    F
key 34    G
key 35    H
key 36    J
key 37    K
key 38    L
key 39    SE<PERSON><PERSON><PERSON><PERSON>
key 40    APOSTROPHE
key 41    GRAVE
key 42    SHIFT_LEFT
key 43    BACKSLASH
key 44    Z
key 45    X
key 46    C
key 47    V
key 48    B
key 49    N
key 50    M
key 51    COMMA
key 52    PERIOD
key 53    SLASH
key 54    SHIFT_RIGHT
key 55    STAR
key 56    ALT_LEFT
key 57    SPACE
key 58    CAPS_LOCK
key 59    F1
key 60    F2
key 61    F3
key 62    F4
key 63    F12
key 64    F6
key 65    F7
key 66    F8
key 67    F9
key 68    F10
key 69    NUM_LOCK
key 70    SCROLL_LOCK
key 71    NUMPAD_7
key 72    NUMPAD_8
key 73    NUMPAD_9
key 74    NUMPAD_SUBTRACT
key 75    NUMPAD_4
key 76    NUMPAD_5
key 77    NUMPAD_6
key 78    NUMPAD_ADD
key 79    NUMPAD_1
key 80    NUMPAD_2
key 81    NUMPAD_3
key 82    NUMPAD_0
key 83    NUMPAD_DOT
# key 84 (undefined)
key 85    ZENKAKU_HANKAKU
key 86    BACKSLASH
key 87    F11
key 88    F12
key 89    RO
# key 90 "KEY_KATAKANA"
# key 91 "KEY_HIRAGANA"
key 92    HENKAN
key 93    KATAKANA_HIRAGANA
key 94    MUHENKAN
key 95    NUMPAD_COMMA
key 96    NUMPAD_ENTER
key 97    CTRL_RIGHT
key 98    NUMPAD_DIVIDE
key 99    SYSRQ
key 100   ALT_RIGHT
# key 101 "KEY_LINEFEED"
key 102   HOME
key 103   DPAD_UP
key 104   CHANNEL_UP
key 105   DPAD_LEFT
key 106   DPAD_RIGHT
key 107   MOVE_END
key 108   DPAD_DOWN
key 109   CHANNEL_DOWN
key 110   INSERT
key 111   FORWARD_DEL
# key 112 "KEY_MACRO"
key 113   VOLUME_MUTE
key 114   VOLUME_DOWN
key 115   VOLUME_UP
key 116   STB_POWER
key 117   NUMPAD_EQUALS
# key 118 "KEY_KPPLUSMINUS"
key 119   MEDIA_PLAY_PAUSE
# key 120 (undefined)
key 121   NUMPAD_COMMA
key 122   KANA
key 123   EISU
key 124   YEN
key 125   META_LEFT
key 126   META_RIGHT
key 127   MENU
key 128   MEDIA_STOP
# key 129 "KEY_AGAIN"
# key 130 "KEY_PROPS"
# key 131 "KEY_UNDO"
# key 132 "KEY_FRONT"
# key 133 "KEY_COPY"
# key 134 "KEY_OPEN"
# key 135 "KEY_PASTE"
# key 136 "KEY_FIND"
# key 137 "KEY_CUT"
key 138    PROG_BLUE
key 139   MENU
key 140   CALCULATOR
# key 141 "KEY_SETUP"
key 142   POWER
key 143   POWER
# key 144 "KEY_FILE"
# key 145 "KEY_SENDFILE"
# key 146 "KEY_DELETEFILE"
# key 147 "KEY_XFER"
# key 148 "KEY_PROG1"
# key 149 "KEY_PROG2"
key 150   EXPLORER
# key 151 "KEY_MSDOS"
key 152   POWER
# key 153 "KEY_DIRECTION"
# key 154 "KEY_CYCLEWINDOWS"
key 155   ENVELOPE
key 156   BOOKMARK
# key 157 "KEY_COMPUTER"
key 158   BACK
key 159   FORWARD
key 160   MEDIA_CLOSE
key 161   MEDIA_EJECT
key 162   MEDIA_EJECT
key 163   MEDIA_NEXT
key 164   MEDIA_PLAY_PAUSE
key 165   MEDIA_PREVIOUS
key 166   MEDIA_STOP
key 167   MEDIA_RECORD
key 168   MEDIA_REWIND
key 169   CALL
# key 170 "KEY_ISO"
key 171   MUSIC
key 172   HOME
# key 173 "KEY_REFRESH"
# key 174 "KEY_EXIT"
# key 175 "KEY_MOVE"
key 176   SETTINGS
key 177   PAGE_UP
key 178   PAGE_DOWN
key 179   NUMPAD_LEFT_PAREN
key 180   NUMPAD_RIGHT_PAREN
# key 181 "KEY_NEW"
# key 182 "KEY_REDO"
key 183   F12
# key 184   F14
# key 185   F15
# key 186   F16
# key 187   F17
# key 188   F18
# key 189   F19
# key 190   F20
# key 191   F21
# key 192   F22
# key 193   F23
# key 194   F24
# key 195 (undefined)
# key 196 (undefined)
# key 197 (undefined)
# key 198 (undefined)
# key 199 (undefined)
key 200   MEDIA_PLAY
key 201   MEDIA_PAUSE
# key 202 "KEY_PROG3"
# key 203 "KEY_PROG4"
key 204   POUND
# key 205 "KEY_SUSPEND"
# key 206 "KEY_CLOSE"
key 207   MEDIA_PLAY
key 208   MEDIA_FAST_FORWARD
# key 209 "KEY_BASSBOOST"
# key 210 "KEY_PRINT"
# key 211 "KEY_HP"
key 212   CAMERA
key 213   MUSIC
# key 214 "KEY_QUESTION"
key 215   ENVELOPE
# key 216 "KEY_CHAT"
key 217   SEARCH
# key 218 "KEY_CONNECT"
# key 219 "KEY_FINANCE"
# key 220 "KEY_SPORT"
# key 221 "KEY_SHOP"
# key 222 "KEY_ALTERASE"
# key 223 "KEY_CANCEL"
key 224   BRIGHTNESS_DOWN
key 225   BRIGHTNESS_UP
key 226   HEADSETHOOK
key 240   POUND

key 256   BUTTON_1
key 257   BUTTON_2
key 258   BUTTON_3
key 259   BUTTON_4
key 260   BUTTON_5
key 261   BUTTON_6
key 262   BUTTON_7
key 263   BUTTON_8
key 264   BUTTON_9
key 265   BUTTON_10
key 266   BUTTON_11
key 267   BUTTON_12
key 268   BUTTON_13
key 269   BUTTON_14
key 270   BUTTON_15
key 271   BUTTON_16

key 288   BUTTON_1
key 289   BUTTON_2
key 290   BUTTON_3
key 291   BUTTON_4
key 292   BUTTON_5
key 293   BUTTON_6
key 294   BUTTON_7
key 295   BUTTON_8
key 296   BUTTON_9
key 297   BUTTON_10
key 298   BUTTON_11
key 299   BUTTON_12
key 300   BUTTON_13
key 301   BUTTON_14
key 302   BUTTON_15
key 303   BUTTON_16


key 304   BUTTON_A
key 305   BUTTON_B
key 306   BUTTON_C
key 307   BUTTON_X
key 308   BUTTON_Y
key 309   BUTTON_Z
key 310   BUTTON_L1
key 311   BUTTON_R1
key 312   BUTTON_L2
key 313   BUTTON_R2
key 314   BUTTON_SELECT
key 315   BUTTON_START
key 316   BUTTON_MODE
key 317   BUTTON_THUMBL
key 318   BUTTON_THUMBR
key 500   MOBILE_M


# key 352 "KEY_OK"
key 353   DPAD_CENTER
# key 354 "KEY_GOTO"
# key 355 "KEY_CLEAR"
# key 356 "KEY_POWER2"
# key 357 "KEY_OPTION"
# key 358 "KEY_INFO"
# key 359 "KEY_TIME"
# key 360 "KEY_VENDOR"
# key 361 "KEY_ARCHIVE"
key 362   GUIDE
# key 363 "KEY_CHANNEL"
# key 364 "KEY_FAVORITES"
# key 365 "KEY_EPG"
key 366   DVR
# key 367 "KEY_MHP"
# key 368 "KEY_LANGUAGE"
# key 369 "KEY_TITLE"
# key 370 "KEY_SUBTITLE"
# key 371 "KEY_ANGLE"
# key 372 "KEY_ZOOM"
# key 373 "KEY_MODE"
# key 374 "KEY_KEYBOARD"
# key 375 "KEY_SCREEN"
# key 376 "KEY_PC"
key 377   TV
# key 378 "KEY_TV2"
# key 379 "KEY_VCR"
# key 380 "KEY_VCR2"
# key 381 "KEY_SAT"
# key 382 "KEY_SAT2"
# key 383 "KEY_CD"
# key 384 "KEY_TAPE"
# key 385 "KEY_RADIO"
# key 386 "KEY_TUNER"
# key 387 "KEY_PLAYER"
# key 388 "KEY_TEXT"
# key 389 "KEY_DVD"
# key 390 "KEY_AUX"
# key 391 "KEY_MP3"
# key 392 "KEY_AUDIO"
# key 393 "KEY_VIDEO"
# key 394 "KEY_DIRECTORY"
# key 395 "KEY_LIST"
# key 396 "KEY_MEMO"
key 397   CALENDAR
# key 398 "KEY_RED"
# key 399 "KEY_GREEN"
# key 400 "KEY_YELLOW"
# key 401 "KEY_BLUE"
key 402   CHANNEL_UP
key 403   CHANNEL_DOWN
# key 404 "KEY_FIRST"
# key 405 "KEY_LAST"
# key 406 "KEY_AB"
# key 407 "KEY_NEXT"
# key 408 "KEY_RESTART"
# key 409 "KEY_SLOW"
# key 410 "KEY_SHUFFLE"
# key 411 "KEY_BREAK"
# key 412 "KEY_PREVIOUS"
# key 413 "KEY_DIGITS"
# key 414 "KEY_TEEN"
# key 415 "KEY_TWEN"

key 429   CONTACTS

# key 448 "KEY_DEL_EOL"
# key 449 "KEY_DEL_EOS"
# key 450 "KEY_INS_LINE"
# key 451 "KEY_DEL_LINE"


key 464   FUNCTION
key 465   ESCAPE            FUNCTION
key 466   F1                FUNCTION
key 467   F2                FUNCTION
key 468   F3                FUNCTION
key 469   F4                FUNCTION
key 470   F5                FUNCTION
key 471   F6                FUNCTION
key 472   F7                FUNCTION
key 473   F8                FUNCTION
key 474   F9                FUNCTION
key 475   F10               FUNCTION
key 476   F11               FUNCTION
key 477   F12               FUNCTION
key 478   1                 FUNCTION
key 479   2                 FUNCTION
key 480   D                 FUNCTION
key 481   E                 FUNCTION
key 482   F                 FUNCTION
key 483   S                 FUNCTION
key 484   B                 FUNCTION


# key 497 KEY_BRL_DOT1
# key 498 KEY_BRL_DOT2
# key 499 KEY_BRL_DOT3
# key 500 KEY_BRL_DOT4
# key 501 KEY_BRL_DOT5
# key 502 KEY_BRL_DOT6
# key 503 KEY_BRL_DOT7
# key 504 KEY_BRL_DOT8

# Keys defined by HID usages
key usage 0x0c006F BRIGHTNESS_UP
key usage 0x0c0070 BRIGHTNESS_DOWN

# Joystick and game controller axes.
# Axes that are not mapped will be assigned generic axis numbers by the input subsystem.
axis 0x00 X
axis 0x01 Y
axis 0x02 Z
axis 0x03 RX
axis 0x04 RY
axis 0x05 RZ
axis 0x06 THROTTLE
axis 0x07 RUDDER
axis 0x08 WHEEL
axis 0x09 GAS
axis 0x0a BRAKE
axis 0x10 HAT_X
axis 0x11 HAT_Y
