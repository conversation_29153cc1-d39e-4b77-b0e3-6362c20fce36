-----<PERSON><PERSON><PERSON> CERTIFICATE-----
MIIDezCCAmOgAwIBAgIBATANBgkqhkiG9w0BAQUFADBfMQswCQYDVQQGEwJUVzES
MBAGA1UECgwJVEFJV0FOLUNBMRAwDgYDVQQLDAdSb290IENBMSowKAYDVQQDDCFU
V0NBIFJvb3QgQ2VydGlmaWNhdGlvbiBBdXRob3JpdHkwHhcNMDgwODI4MDcyNDMz
WhcNMzAxMjMxMTU1OTU5WjBfMQswCQYDVQQGEwJUVzESMBAGA1UECgwJVEFJV0FO
LUNBMRAwDgYDVQQLDAdSb290IENBMSowKAYDVQQDDCFUV0NBIFJvb3QgQ2VydGlm
aWNhdGlvbiBBdXRob3JpdHkwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIB
AQCwfnK4pAOU5qfeCTiRShFAh6d8WWQUe7UREN3+v9XAu1bihSX0NXIP+FPQQeFE
AcK0HMMxQhZHhTMidrIKbw/lJVBPhYa+v5guEGcevhEFhgWQxFnQfHgQsIBct+HH
K3XLfJ+utdGdIzdjp9xCoi2SBBtQwXu4PhvJVgSLL1KbralW6cH/ralYhzC2gfeX
RfwZVzsrb+RH9JlF/h3x+JejiB03HFyP4HYlmlD4oFT/RJB2I9IyxsOrBr/8+7/z
rX2SYgJbKdM1o5OaQ2RgXbL6Mv87BK9NQGr5x+PvI/1ry+UPizgN7gr8/g+YnzAx
3WxSZfmLgb4i4RxYA7qRG4kHAgMBAAGjQjBAMA4GA1UdDwEB/wQEAwIBBjAPBgNV
HRMBAf8EBTADAQH/MB0GA1UdDgQWBBRqOFsmjd6LWvJPelSDGRjjCDWmujANBgkq
hkiG9w0BAQUFAAOCAQEAPNV3PdrfibqHDAhUaiBQkr6wQT25JmSDCi/oQMCXKCeC
MErJk/9q56YAf4lCmtYR5VPOL8zy2gXE/uJQxDqGfczafhAJO5I1KlOy/usrBdls
XebQ79NqZp4VKIV66IIArB6nCWlWQtNoURi+VJq/REG6Sb4gumlc7rh3zc5sH62D
lhh9DrUUOYTxKOkto557HnpyWoOzeW/vtPzQCqVYT0bf+215WfKEIlKuD8z7fDvn
aspHYcN6+NOSBB+4IIThNlQWx0DeO4pz3N/GCUzf7Nr/1FNCocnyYh0igzyXxfkZ
YiesZSLX0zzG5Y6yU8xJzrww/nsOM5D77dIUkR8Hrw==
-----END CERTIFICATE-----
Certificate:
    Data:
        Version: 3 (0x2)
        Serial Number: 1 (0x1)
    Signature Algorithm: sha1WithRSAEncryption
        Issuer: C=TW, O=TAIWAN-CA, OU=Root CA, CN=TWCA Root Certification Authority
        Validity
            Not Before: Aug 28 07:24:33 2008 GMT
            Not After : Dec 31 15:59:59 2030 GMT
        Subject: C=TW, O=TAIWAN-CA, OU=Root CA, CN=TWCA Root Certification Authority
        Subject Public Key Info:
            Public Key Algorithm: rsaEncryption
                Public-Key: (2048 bit)
                Modulus:
                    00:b0:7e:72:b8:a4:03:94:e6:a7:de:09:38:91:4a:
                    11:40:87:a7:7c:59:64:14:7b:b5:11:10:dd:fe:bf:
                    d5:c0:bb:56:e2:85:25:f4:35:72:0f:f8:53:d0:41:
                    e1:44:01:c2:b4:1c:c3:31:42:16:47:85:33:22:76:
                    b2:0a:6f:0f:e5:25:50:4f:85:86:be:bf:98:2e:10:
                    67:1e:be:11:05:86:05:90:c4:59:d0:7c:78:10:b0:
                    80:5c:b7:e1:c7:2b:75:cb:7c:9f:ae:b5:d1:9d:23:
                    37:63:a7:dc:42:a2:2d:92:04:1b:50:c1:7b:b8:3e:
                    1b:c9:56:04:8b:2f:52:9b:ad:a9:56:e9:c1:ff:ad:
                    a9:58:87:30:b6:81:f7:97:45:fc:19:57:3b:2b:6f:
                    e4:47:f4:99:45:fe:1d:f1:f8:97:a3:88:1d:37:1c:
                    5c:8f:e0:76:25:9a:50:f8:a0:54:ff:44:90:76:23:
                    d2:32:c6:c3:ab:06:bf:fc:fb:bf:f3:ad:7d:92:62:
                    02:5b:29:d3:35:a3:93:9a:43:64:60:5d:b2:fa:32:
                    ff:3b:04:af:4d:40:6a:f9:c7:e3:ef:23:fd:6b:cb:
                    e5:0f:8b:38:0d:ee:0a:fc:fe:0f:98:9f:30:31:dd:
                    6c:52:65:f9:8b:81:be:22:e1:1c:58:03:ba:91:1b:
                    89:07
                Exponent: 65537 (0x10001)
        X509v3 extensions:
            X509v3 Key Usage: critical
                Certificate Sign, CRL Sign
            X509v3 Basic Constraints: critical
                CA:TRUE
            X509v3 Subject Key Identifier: 
                6A:38:5B:26:8D:DE:8B:5A:F2:4F:7A:54:83:19:18:E3:08:35:A6:BA
    Signature Algorithm: sha1WithRSAEncryption
         3c:d5:77:3d:da:df:89:ba:87:0c:08:54:6a:20:50:92:be:b0:
         41:3d:b9:26:64:83:0a:2f:e8:40:c0:97:28:27:82:30:4a:c9:
         93:ff:6a:e7:a6:00:7f:89:42:9a:d6:11:e5:53:ce:2f:cc:f2:
         da:05:c4:fe:e2:50:c4:3a:86:7d:cc:da:7e:10:09:3b:92:35:
         2a:53:b2:fe:eb:2b:05:d9:6c:5d:e6:d0:ef:d3:6a:66:9e:15:
         28:85:7a:e8:82:00:ac:1e:a7:09:69:56:42:d3:68:51:18:be:
         54:9a:bf:44:41:ba:49:be:20:ba:69:5c:ee:b8:77:cd:ce:6c:
         1f:ad:83:96:18:7d:0e:b5:14:39:84:f1:28:e9:2d:a3:9e:7b:
         1e:7a:72:5a:83:b3:79:6f:ef:b4:fc:d0:0a:a5:58:4f:46:df:
         fb:6d:79:59:f2:84:22:52:ae:0f:cc:fb:7c:3b:e7:6a:ca:47:
         61:c3:7a:f8:d3:92:04:1f:b8:20:84:e1:36:54:16:c7:40:de:
         3b:8a:73:dc:df:c6:09:4c:df:ec:da:ff:d4:53:42:a1:c9:f2:
         62:1d:22:83:3c:97:c5:f9:19:62:27:ac:65:22:d7:d3:3c:c6:
         e5:8e:b2:53:cc:49:ce:bc:30:fe:7b:0e:33:90:fb:ed:d2:14:
         91:1f:07:af
SHA1 Fingerprint=CF:9E:87:6D:D3:EB:FC:42:26:97:A3:B5:A3:7A:A0:76:A9:06:23:48
