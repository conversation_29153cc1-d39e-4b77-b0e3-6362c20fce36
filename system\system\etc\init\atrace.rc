## Permissions to allow system-wide tracing to the kernel trace buffer.
##
on late-init

# Allow writing to the kernel trace log.
    chmod 0222 /sys/kernel/debug/tracing/trace_marker
    chmod 0222 /sys/kernel/tracing/trace_marker

# Scheduler tracepoints require schedstats=enable
    write /proc/sys/kernel/sched_schedstats 1

# Grant unix world read/write permissions to kernel tracepoints.
# Access control to these files is now entirely in selinux policy.
    chmod 0666 /sys/kernel/debug/tracing/trace_clock
    chmod 0666 /sys/kernel/tracing/trace_clock
    chmod 0666 /sys/kernel/debug/tracing/buffer_size_kb
    chmod 0666 /sys/kernel/tracing/buffer_size_kb
    chmod 0666 /sys/kernel/debug/tracing/options/overwrite
    chmod 0666 /sys/kernel/tracing/options/overwrite
    chmod 0666 /sys/kernel/debug/tracing/options/print-tgid
    chmod 0666 /sys/kernel/tracing/options/print-tgid
    chmod 0666 /sys/kernel/debug/tracing/saved_cmdlines_size
    chmod 0666 /sys/kernel/tracing/saved_cmdlines_size
    chmod 0666 /sys/kernel/debug/tracing/events/sched/sched_switch/enable
    chmod 0666 /sys/kernel/tracing/events/sched/sched_switch/enable
    chmod 0666 /sys/kernel/debug/tracing/events/sched/sched_wakeup/enable
    chmod 0666 /sys/kernel/tracing/events/sched/sched_wakeup/enable
    chmod 0666 /sys/kernel/debug/tracing/events/sched/sched_blocked_reason/enable
    chmod 0666 /sys/kernel/tracing/events/sched/sched_blocked_reason/enable
    chmod 0666 /sys/kernel/debug/tracing/events/sched/sched_cpu_hotplug/enable
    chmod 0666 /sys/kernel/tracing/events/sched/sched_cpu_hotplug/enable
    chmod 0666 /sys/kernel/debug/tracing/events/sched/sched_pi_setprio/enable
    chmod 0666 /sys/kernel/tracing/events/sched/sched_pi_setprio/enable
    chmod 0666 /sys/kernel/debug/tracing/events/cgroup/enable
    chmod 0666 /sys/kernel/tracing/events/cgroup/enable
    chmod 0666 /sys/kernel/debug/tracing/events/power/cpu_frequency/enable
    chmod 0666 /sys/kernel/tracing/events/power/cpu_frequency/enable
    chmod 0666 /sys/kernel/debug/tracing/events/power/cpu_idle/enable
    chmod 0666 /sys/kernel/tracing/events/power/cpu_idle/enable
    chmod 0666 /sys/kernel/debug/tracing/events/power/clock_set_rate/enable
    chmod 0666 /sys/kernel/tracing/events/power/clock_set_rate/enable
    chmod 0666 /sys/kernel/debug/tracing/events/power/cpu_frequency_limits/enable
    chmod 0666 /sys/kernel/tracing/events/power/cpu_frequency_limits/enable
    chmod 0666 /sys/kernel/debug/tracing/events/cpufreq_interactive/enable
    chmod 0666 /sys/kernel/tracing/events/cpufreq_interactive/enable
    chmod 0666 /sys/kernel/debug/tracing/events/vmscan/mm_vmscan_direct_reclaim_begin/enable
    chmod 0666 /sys/kernel/tracing/events/vmscan/mm_vmscan_direct_reclaim_begin/enable
    chmod 0666 /sys/kernel/debug/tracing/events/vmscan/mm_vmscan_direct_reclaim_end/enable
    chmod 0666 /sys/kernel/tracing/events/vmscan/mm_vmscan_direct_reclaim_end/enable
    chmod 0666 /sys/kernel/debug/tracing/events/vmscan/mm_vmscan_kswapd_wake/enable
    chmod 0666 /sys/kernel/tracing/events/vmscan/mm_vmscan_kswapd_wake/enable
    chmod 0666 /sys/kernel/debug/tracing/events/vmscan/mm_vmscan_kswapd_sleep/enable
    chmod 0666 /sys/kernel/tracing/events/vmscan/mm_vmscan_kswapd_sleep/enable
    chmod 0666 /sys/kernel/debug/tracing/tracing_on
    chmod 0666 /sys/kernel/tracing/tracing_on
    chmod 0666 /sys/kernel/debug/tracing/events/binder/binder_transaction/enable
    chmod 0666 /sys/kernel/tracing/events/binder/binder_transaction/enable
    chmod 0666 /sys/kernel/debug/tracing/events/binder/binder_transaction_received/enable
    chmod 0666 /sys/kernel/tracing/events/binder/binder_transaction_received/enable
    chmod 0666 /sys/kernel/debug/tracing/events/binder/binder_lock/enable
    chmod 0666 /sys/kernel/tracing/events/binder/binder_lock/enable
    chmod 0666 /sys/kernel/debug/tracing/events/binder/binder_locked/enable
    chmod 0666 /sys/kernel/tracing/events/binder/binder_locked/enable
    chmod 0666 /sys/kernel/debug/tracing/events/binder/binder_unlock/enable
    chmod 0666 /sys/kernel/tracing/events/binder/binder_unlock/enable
    chmod 0666 /sys/kernel/debug/tracing/events/i2c/enable
    chmod 0666 /sys/kernel/tracing/events/i2c/enable
    chmod 0666 /sys/kernel/debug/tracing/events/i2c/i2c_read/enable
    chmod 0666 /sys/kernel/tracing/events/i2c/i2c_read/enable
    chmod 0666 /sys/kernel/debug/tracing/events/i2c/i2c_write/enable
    chmod 0666 /sys/kernel/tracing/events/i2c/i2c_write/enable
    chmod 0666 /sys/kernel/debug/tracing/events/i2c/i2c_result/enable
    chmod 0666 /sys/kernel/tracing/events/i2c/i2c_result/enable
    chmod 0666 /sys/kernel/debug/tracing/events/i2c/i2c_reply/enable
    chmod 0666 /sys/kernel/tracing/events/i2c/i2c_reply/enable
    chmod 0666 /sys/kernel/debug/tracing/events/i2c/smbus_read/enable
    chmod 0666 /sys/kernel/tracing/events/i2c/smbus_read/enable
    chmod 0666 /sys/kernel/debug/tracing/events/i2c/smbus_write/enable
    chmod 0666 /sys/kernel/tracing/events/i2c/smbus_write/enable
    chmod 0666 /sys/kernel/debug/tracing/events/i2c/smbus_result/enable
    chmod 0666 /sys/kernel/tracing/events/i2c/smbus_result/enable
    chmod 0666 /sys/kernel/debug/tracing/events/i2c/smbus_reply/enable
    chmod 0666 /sys/kernel/tracing/events/i2c/smbus_reply/enable
    chmod 0666 /sys/kernel/debug/tracing/events/lowmemorykiller/enable
    chmod 0666 /sys/kernel/tracing/events/lowmemorykiller/enable
    chmod 0666 /sys/kernel/debug/tracing/events/sync/enable
    chmod 0666 /sys/kernel/tracing/events/sync/enable
    chmod 0666 /sys/kernel/debug/tracing/events/fence/enable
    chmod 0666 /sys/kernel/tracing/events/fence/enable


    # disk
    chmod 0666 /sys/kernel/tracing/events/f2fs/f2fs_sync_file_enter/enable
    chmod 0666 /sys/kernel/debug/tracing/events/f2fs/f2fs_sync_file_enter/enable
    chmod 0666 /sys/kernel/tracing/events/f2fs/f2fs_sync_file_exit/enable
    chmod 0666 /sys/kernel/debug/tracing/events/f2fs/f2fs_sync_file_exit/enable
    chmod 0666 /sys/kernel/tracing/events/f2fs/f2fs_write_begin/enable
    chmod 0666 /sys/kernel/debug/tracing/events/f2fs/f2fs_write_begin/enable
    chmod 0666 /sys/kernel/tracing/events/f2fs/f2fs_write_end/enable
    chmod 0666 /sys/kernel/debug/tracing/events/f2fs/f2fs_write_end/enable
    chmod 0666 /sys/kernel/tracing/events/ext4/ext4_da_write_begin/enable
    chmod 0666 /sys/kernel/debug/tracing/events/ext4/ext4_da_write_begin/enable
    chmod 0666 /sys/kernel/tracing/events/ext4/ext4_da_write_end/enable
    chmod 0666 /sys/kernel/debug/tracing/events/ext4/ext4_da_write_end/enable
    chmod 0666 /sys/kernel/tracing/events/ext4/ext4_sync_file_enter/enable
    chmod 0666 /sys/kernel/debug/tracing/events/ext4/ext4_sync_file_enter/enable
    chmod 0666 /sys/kernel/tracing/events/ext4/ext4_sync_file_exit/enable
    chmod 0666 /sys/kernel/debug/tracing/events/ext4/ext4_sync_file_exit/enable
    chmod 0666 /sys/kernel/tracing/events/block/block_rq_issue/enable
    chmod 0666 /sys/kernel/debug/tracing/events/block/block_rq_issue/enable
    chmod 0666 /sys/kernel/tracing/events/block/block_rq_complete/enable
    chmod 0666 /sys/kernel/debug/tracing/events/block/block_rq_complete/enable

    # graphics
    chmod 0666 /sys/kernel/tracing/events/sde/enable
    chmod 0666 /sys/kernel/debug/tracing/events/sde/enable
    chmod 0666 /sys/kernel/tracing/events/mdss/enable
    chmod 0666 /sys/kernel/debug/tracing/events/mdss/enable

# Tracing disabled by default
    write /sys/kernel/debug/tracing/tracing_on 0
    write /sys/kernel/tracing/tracing_on 0

# Read and truncate the kernel trace.
    chmod 0666 /sys/kernel/debug/tracing/trace
    chmod 0666 /sys/kernel/tracing/trace

on property:persist.debug.atrace.boottrace=1
    start boottrace

# Run atrace with the categories written in a file
service boottrace /system/bin/atrace --async_start -f /data/misc/boottrace/categories
    disabled
    oneshot
