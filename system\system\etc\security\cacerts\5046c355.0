-----<PERSON><PERSON><PERSON> CERTIFICATE-----
MIIFvTCCA6WgAwIBAgIITxvUL1S7L0swDQYJKoZIhvcNAQEFBQAwRzELMAkGA1UE
BhMCQ0gxFTATBgNVBAoTDFN3aXNzU2lnbiBBRzEhMB8GA1UEAxMYU3dpc3NTaWdu
IFNpbHZlciBDQSAtIEcyMB4XDTA2MTAyNTA4MzI0NloXDTM2MTAyNTA4MzI0Nlow
RzELMAkGA1UEBhMCQ0gxFTATBgNVBAoTDFN3aXNzU2lnbiBBRzEhMB8GA1UEAxMY
U3dpc3NTaWduIFNpbHZlciBDQSAtIEcyMIICIjANBgkqhkiG9w0BAQEFAAOCAg8A
MIICCgKCAgEAxPGHf9N4Mfc4yfjDmUO8x/e8N+dOcbpLj6VzHVxumK4DV644N0Mv
Fz0fyM5oEMF4rhkDKxD6LHmD9ui5aLlV8gREpzn5/ASLHvGiTSf5YXu6t+WiE7br
YT7QbNHm+/pe7R20nqA1W6GSy/BJkv6FCgU+5tkL4k+73JU3/JHpMjUi0R86TieF
nbAVlDLaYQ1HTWBCrpJH6INaUFjpiou5XaHc3ZlKHzZnu0jkg7Y360g6rw9njxcH
6ATK72oxh9TAtvmUcXtnZLi2kUpCe2UuMGoM9ZDulebyzYLs2aFK7PayS+VFheZt
eJMELpyCbTapxDFkH4aDCyr0NQp4yVXPQbBH6TCfmb5hqAaEuSh6XzjZG6k4sIN/
c8HDO0gqgg8hm7jMqDXDhBuDsz6+pJVpATqJAHgE2cn0mRmrVn5bi4Y5FZGkECwJ
MoBgs5PAKrYYC51+jUnyEEp/+dVGLxmSo5mnJqy7jDzmDrxHB9xzUfFwZC8I+bRH
HTBsROopN4WSaGa8gzj+ezku01DwH/teYLappvonQfGbGHLy9YR0SslnxFSuSGTf
jNFusB3hB48IHpmccelM2KX3RxIfdNFRnobzwqIjQAtz20um53MGjMGg6cFZrEb6
5i/4z3GcRm25xBWNOHkDRUjvxF3XCO6HOSKGsg0PWEP3calILv3q1h8CAwEAAaOB
rDCBqTAOBgNVHQ8BAf8EBAMCAQYwDwYDVR0TAQH/BAUwAwEB/zAdBgNVHQ4EFgQU
F6DNweRBtjpbO8tFnb0cwpj6hlgwHwYDVR0jBBgwFoAUF6DNweRBtjpbO8tFnb0c
wpj6hlgwRgYDVR0gBD8wPTA7BglghXQBWQEDAQEwLjAsBggrBgEFBQcCARYgaHR0
cDovL3JlcG9zaXRvcnkuc3dpc3NzaWduLmNvbS8wDQYJKoZIhvcNAQEFBQADggIB
AHPGgeAn0i0P4JUw4ppBf1AsX19iYamGamkYDHRJ1l2E6kFSGG9YrVBWIGrGvShp
WJHckRE1qTodvBqlYJ7YH39FkWnZfrt4csEGDyrOj4VwYaygzQu4OSlWhDJOhrs9
xCrZ1x9y7v5RoSJBsXECYxqCsGKrXlcSH9/L3XWgwF15kIwb4FDm3jH+mHtwX6WQ
2K34ArZv02DdQEsixT2tOnqfGhpHkXkzuoLcMmkDlm4fS/Bx/uNncqCxv1yL5PqZ
IseEuRuNI5c/7SXgz2W79WEE790eslpBIlqhn10s6FvJbakMDHiqYMZWjwFaDGi8
aRl5xB9+lwW/xekkUV7U1UtT7dkjWjYDZaPBA61BMPNGG4WQr2W11bHkFlt4dR2X
em1ZqSqPe97Dh4kQmUlzeMg9vVE1dCrV8X5pGyq7O70luJpaPXJhkGaH7gzWTdQR
dAtq/gsD/KNVV4n+SsuuWxcFyPKNIzFTONItaj+CuY0IavdeQXRuwxF+B6wpYJE/
OMpXEA29MC/HpeZBoNquBYeaoKRlbEwJDIm6uNO5wJOKMPqN5ZprFQFOZ6raYlY+
hAhm0sQ2fac+EPyI4NSA5QC9qvNOBqN6avlicuMJT+ubDgEj8Z+7fNzcbBGXJbLy
tGMU0gYqZ4yD9c7qB9iaah7s5Aq7KkzrCWA5zspi2C5u
-----END CERTIFICATE-----
Certificate:
    Data:
        Version: 3 (0x2)
        Serial Number: 5700383053117599563 (0x4f1bd42f54bb2f4b)
    Signature Algorithm: sha1WithRSAEncryption
        Issuer: C=CH, O=SwissSign AG, CN=SwissSign Silver CA - G2
        Validity
            Not Before: Oct 25 08:32:46 2006 GMT
            Not After : Oct 25 08:32:46 2036 GMT
        Subject: C=CH, O=SwissSign AG, CN=SwissSign Silver CA - G2
        Subject Public Key Info:
            Public Key Algorithm: rsaEncryption
                Public-Key: (4096 bit)
                Modulus:
                    00:c4:f1:87:7f:d3:78:31:f7:38:c9:f8:c3:99:43:
                    bc:c7:f7:bc:37:e7:4e:71:ba:4b:8f:a5:73:1d:5c:
                    6e:98:ae:03:57:ae:38:37:43:2f:17:3d:1f:c8:ce:
                    68:10:c1:78:ae:19:03:2b:10:fa:2c:79:83:f6:e8:
                    b9:68:b9:55:f2:04:44:a7:39:f9:fc:04:8b:1e:f1:
                    a2:4d:27:f9:61:7b:ba:b7:e5:a2:13:b6:eb:61:3e:
                    d0:6c:d1:e6:fb:fa:5e:ed:1d:b4:9e:a0:35:5b:a1:
                    92:cb:f0:49:92:fe:85:0a:05:3e:e6:d9:0b:e2:4f:
                    bb:dc:95:37:fc:91:e9:32:35:22:d1:1f:3a:4e:27:
                    85:9d:b0:15:94:32:da:61:0d:47:4d:60:42:ae:92:
                    47:e8:83:5a:50:58:e9:8a:8b:b9:5d:a1:dc:dd:99:
                    4a:1f:36:67:bb:48:e4:83:b6:37:eb:48:3a:af:0f:
                    67:8f:17:07:e8:04:ca:ef:6a:31:87:d4:c0:b6:f9:
                    94:71:7b:67:64:b8:b6:91:4a:42:7b:65:2e:30:6a:
                    0c:f5:90:ee:95:e6:f2:cd:82:ec:d9:a1:4a:ec:f6:
                    b2:4b:e5:45:85:e6:6d:78:93:04:2e:9c:82:6d:36:
                    a9:c4:31:64:1f:86:83:0b:2a:f4:35:0a:78:c9:55:
                    cf:41:b0:47:e9:30:9f:99:be:61:a8:06:84:b9:28:
                    7a:5f:38:d9:1b:a9:38:b0:83:7f:73:c1:c3:3b:48:
                    2a:82:0f:21:9b:b8:cc:a8:35:c3:84:1b:83:b3:3e:
                    be:a4:95:69:01:3a:89:00:78:04:d9:c9:f4:99:19:
                    ab:56:7e:5b:8b:86:39:15:91:a4:10:2c:09:32:80:
                    60:b3:93:c0:2a:b6:18:0b:9d:7e:8d:49:f2:10:4a:
                    7f:f9:d5:46:2f:19:92:a3:99:a7:26:ac:bb:8c:3c:
                    e6:0e:bc:47:07:dc:73:51:f1:70:64:2f:08:f9:b4:
                    47:1d:30:6c:44:ea:29:37:85:92:68:66:bc:83:38:
                    fe:7b:39:2e:d3:50:f0:1f:fb:5e:60:b6:a9:a6:fa:
                    27:41:f1:9b:18:72:f2:f5:84:74:4a:c9:67:c4:54:
                    ae:48:64:df:8c:d1:6e:b0:1d:e1:07:8f:08:1e:99:
                    9c:71:e9:4c:d8:a5:f7:47:12:1f:74:d1:51:9e:86:
                    f3:c2:a2:23:40:0b:73:db:4b:a6:e7:73:06:8c:c1:
                    a0:e9:c1:59:ac:46:fa:e6:2f:f8:cf:71:9c:46:6d:
                    b9:c4:15:8d:38:79:03:45:48:ef:c4:5d:d7:08:ee:
                    87:39:22:86:b2:0d:0f:58:43:f7:71:a9:48:2e:fd:
                    ea:d6:1f
                Exponent: 65537 (0x10001)
        X509v3 extensions:
            X509v3 Key Usage: critical
                Certificate Sign, CRL Sign
            X509v3 Basic Constraints: critical
                CA:TRUE
            X509v3 Subject Key Identifier: 
                17:A0:CD:C1:E4:41:B6:3A:5B:3B:CB:45:9D:BD:1C:C2:98:FA:86:58
            X509v3 Authority Key Identifier: 
                keyid:17:A0:CD:C1:E4:41:B6:3A:5B:3B:CB:45:9D:BD:1C:C2:98:FA:86:58

            X509v3 Certificate Policies: 
                Policy: 2.16.756.1.89.1.3.1.1
                  CPS: http://repository.swisssign.com/

    Signature Algorithm: sha1WithRSAEncryption
         73:c6:81:e0:27:d2:2d:0f:e0:95:30:e2:9a:41:7f:50:2c:5f:
         5f:62:61:a9:86:6a:69:18:0c:74:49:d6:5d:84:ea:41:52:18:
         6f:58:ad:50:56:20:6a:c6:bd:28:69:58:91:dc:91:11:35:a9:
         3a:1d:bc:1a:a5:60:9e:d8:1f:7f:45:91:69:d9:7e:bb:78:72:
         c1:06:0f:2a:ce:8f:85:70:61:ac:a0:cd:0b:b8:39:29:56:84:
         32:4e:86:bb:3d:c4:2a:d9:d7:1f:72:ee:fe:51:a1:22:41:b1:
         71:02:63:1a:82:b0:62:ab:5e:57:12:1f:df:cb:dd:75:a0:c0:
         5d:79:90:8c:1b:e0:50:e6:de:31:fe:98:7b:70:5f:a5:90:d8:
         ad:f8:02:b6:6f:d3:60:dd:40:4b:22:c5:3d:ad:3a:7a:9f:1a:
         1a:47:91:79:33:ba:82:dc:32:69:03:96:6e:1f:4b:f0:71:fe:
         e3:67:72:a0:b1:bf:5c:8b:e4:fa:99:22:c7:84:b9:1b:8d:23:
         97:3f:ed:25:e0:cf:65:bb:f5:61:04:ef:dd:1e:b2:5a:41:22:
         5a:a1:9f:5d:2c:e8:5b:c9:6d:a9:0c:0c:78:aa:60:c6:56:8f:
         01:5a:0c:68:bc:69:19:79:c4:1f:7e:97:05:bf:c5:e9:24:51:
         5e:d4:d5:4b:53:ed:d9:23:5a:36:03:65:a3:c1:03:ad:41:30:
         f3:46:1b:85:90:af:65:b5:d5:b1:e4:16:5b:78:75:1d:97:7a:
         6d:59:a9:2a:8f:7b:de:c3:87:89:10:99:49:73:78:c8:3d:bd:
         51:35:74:2a:d5:f1:7e:69:1b:2a:bb:3b:bd:25:b8:9a:5a:3d:
         72:61:90:66:87:ee:0c:d6:4d:d4:11:74:0b:6a:fe:0b:03:fc:
         a3:55:57:89:fe:4a:cb:ae:5b:17:05:c8:f2:8d:23:31:53:38:
         d2:2d:6a:3f:82:b9:8d:08:6a:f7:5e:41:74:6e:c3:11:7e:07:
         ac:29:60:91:3f:38:ca:57:10:0d:bd:30:2f:c7:a5:e6:41:a0:
         da:ae:05:87:9a:a0:a4:65:6c:4c:09:0c:89:ba:b8:d3:b9:c0:
         93:8a:30:fa:8d:e5:9a:6b:15:01:4e:67:aa:da:62:56:3e:84:
         08:66:d2:c4:36:7d:a7:3e:10:fc:88:e0:d4:80:e5:00:bd:aa:
         f3:4e:06:a3:7a:6a:f9:62:72:e3:09:4f:eb:9b:0e:01:23:f1:
         9f:bb:7c:dc:dc:6c:11:97:25:b2:f2:b4:63:14:d2:06:2a:67:
         8c:83:f5:ce:ea:07:d8:9a:6a:1e:ec:e4:0a:bb:2a:4c:eb:09:
         60:39:ce:ca:62:d8:2e:6e
SHA1 Fingerprint=9B:AA:E5:9F:56:EE:21:CB:43:5A:BE:25:93:DF:A7:F0:40:D1:1D:CB
