-----<PERSON><PERSON><PERSON> CERTIFICATE-----
MIIFWTCCA0GgAwIBAgIBAjANBgkqhkiG9w0BAQsFADBOMQswCQYDVQQGEwJOTzEd
MBsGA1UECgwUQnV5cGFzcyBBUy05ODMxNjMzMjcxIDAeBgNVBAMMF0J1eXBhc3Mg
Q2xhc3MgMyBSb290IENBMB4XDTEwMTAyNjA4Mjg1OFoXDTQwMTAyNjA4Mjg1OFow
TjELMAkGA1UEBhMCTk8xHTAbBgNVBAoMFEJ1eXBhc3MgQVMtOTgzMTYzMzI3MSAw
HgYDVQQDDBdCdXlwYXNzIENsYXNzIDMgUm9vdCBDQTCCAiIwDQYJKoZIhvcNAQEB
BQADggIPADCCAgoCggIBAKXaCpUWUOOV8l6ddjEGMnqb8RB2uACatVI2zSRHsJ8Y
ZLya9vrVediQYkwiL944PdbgqOkcLNt4EemOaFEVcsfzM4fkoF0LXOBXByow9c3E
N3coTRiR5r/VUv1xLXA+58bEiuPwKAv0dpihi4dVsjoT/Lc+JzeOIuOoTyrvYLs9
tznDDgFHmV0ST9tD+leh7fmdvhFHJlsTmKtdFoqwNxxXnUX/iJY2v7vKB3tvh2PX
0DJq1l1sDPGzbjniazEuOQAnFN44wOwZZoYS6J1yFhNkUsepNxz9gjDthBgd9K5c
/3ATAOux9TN6S9ZV+AWNS2mw9bMoNlwUxFFzTWsL8TQH2xc519woe2v1n/MuwU8X
KhDzzMro6/1rqy6any2CbgTUUgGTLT2G/H783+9CHaZr77kgxve9oKeV/afmiSTY
zIw0bOIjL9kSGiG5VZFvC5F5GQytQIgLcOJ60g7YaEi7ghM5EFjp2CoHxhLbWNvS
O1UQRwUVZ2J+GGOmRj8JDlQyXr8NYnon74Do29lLBlo3WiXQCBJ31G8JUJc9yB3D
34xFMFbG02SrZvPAXpacw8Tvw3xrizp5f7NJzz3iiZ+gMEuFuZyUJHmPfWupRWgP
K9Dx2hzLabjKSWJtyNBjYt1gD1iqj6G8BaVmos8bdrKEZLFMOVLAMLrwjEsCsLa3
AgMBAAGjQjBAMA8GA1UdEwEB/wQFMAMBAf8wHQYDVR0OBBYEFEe4zf/lb+74suwv
Tg75JbCOPGvDMA4GA1UdDwEB/wQEAwIBBjANBgkqhkiG9w0BAQsFAAOCAgEAACAj
QTUEkMJAYmDv4jVM1z+s4jSQuKFvdvoWFqRINyzpkMLyPPgKn9iB5btb2iUspKdV
cSQy9sgL8rxq+JOssgfCX5/bzMiKqr5qb+FJEMwx14C7u8jYog5kV+qi9cKpMRXS
IGrs/CIBKM+GuIAeqcwRpTzyFrNHnfzSgCHEy9BHcEGhyoMZCCxt8l13nIoUE9Q2
HJLw5QY33KbmkJs4j1xrG0aGQ0JfPgEHU1RdZX33inOhmlRaHylDFCfChQ+1iHsa
O5S3HWCntZznKWlXWpuTekMwGwPXYshApqr8ZORK15FTAaggiG6cX0S5y2CBNOxv
033aSF/rtJC8LakcC6wc1aJoIIAE1vyxjy+7SjENSoYc6+I2KSb12tjE8nVhz36u
dmNKekBlk4f4HoCMhuWG1o8O/FMsYOgWYRqiPkN7zTlgVGr18okmAWiDSKIz6MkE
kbIRNBE+6tBDGR8Dk5AM/1E9V/RBbuHLoL7ryWPNbczk+DaqaJ3tvV2XcEQNtg41
3OEMXbugUZTLfhbrES+jkkXITHHZvMmZUldGL1DPvTVp9D0VzgalLA8+9oG6lLvD
u79leNKGef9JOxqDDPDeeOzI8k1MGt6CKfjBWtrt7uYnXuhF0J0cUahoq0Tj0Itq
4/g7u9xN12TyUb7mqqta6THuBrxzvxNiCp/HuZc=
-----END CERTIFICATE-----
Certificate:
    Data:
        Version: 3 (0x2)
        Serial Number: 2 (0x2)
    Signature Algorithm: sha256WithRSAEncryption
        Issuer: C=NO, O=Buypass AS-983163327, CN=Buypass Class 3 Root CA
        Validity
            Not Before: Oct 26 08:28:58 2010 GMT
            Not After : Oct 26 08:28:58 2040 GMT
        Subject: C=NO, O=Buypass AS-983163327, CN=Buypass Class 3 Root CA
        Subject Public Key Info:
            Public Key Algorithm: rsaEncryption
                Public-Key: (4096 bit)
                Modulus:
                    00:a5:da:0a:95:16:50:e3:95:f2:5e:9d:76:31:06:
                    32:7a:9b:f1:10:76:b8:00:9a:b5:52:36:cd:24:47:
                    b0:9f:18:64:bc:9a:f6:fa:d5:79:d8:90:62:4c:22:
                    2f:de:38:3d:d6:e0:a8:e9:1c:2c:db:78:11:e9:8e:
                    68:51:15:72:c7:f3:33:87:e4:a0:5d:0b:5c:e0:57:
                    07:2a:30:f5:cd:c4:37:77:28:4d:18:91:e6:bf:d5:
                    52:fd:71:2d:70:3e:e7:c6:c4:8a:e3:f0:28:0b:f4:
                    76:98:a1:8b:87:55:b2:3a:13:fc:b7:3e:27:37:8e:
                    22:e3:a8:4f:2a:ef:60:bb:3d:b7:39:c3:0e:01:47:
                    99:5d:12:4f:db:43:fa:57:a1:ed:f9:9d:be:11:47:
                    26:5b:13:98:ab:5d:16:8a:b0:37:1c:57:9d:45:ff:
                    88:96:36:bf:bb:ca:07:7b:6f:87:63:d7:d0:32:6a:
                    d6:5d:6c:0c:f1:b3:6e:39:e2:6b:31:2e:39:00:27:
                    14:de:38:c0:ec:19:66:86:12:e8:9d:72:16:13:64:
                    52:c7:a9:37:1c:fd:82:30:ed:84:18:1d:f4:ae:5c:
                    ff:70:13:00:eb:b1:f5:33:7a:4b:d6:55:f8:05:8d:
                    4b:69:b0:f5:b3:28:36:5c:14:c4:51:73:4d:6b:0b:
                    f1:34:07:db:17:39:d7:dc:28:7b:6b:f5:9f:f3:2e:
                    c1:4f:17:2a:10:f3:cc:ca:e8:eb:fd:6b:ab:2e:9a:
                    9f:2d:82:6e:04:d4:52:01:93:2d:3d:86:fc:7e:fc:
                    df:ef:42:1d:a6:6b:ef:b9:20:c6:f7:bd:a0:a7:95:
                    fd:a7:e6:89:24:d8:cc:8c:34:6c:e2:23:2f:d9:12:
                    1a:21:b9:55:91:6f:0b:91:79:19:0c:ad:40:88:0b:
                    70:e2:7a:d2:0e:d8:68:48:bb:82:13:39:10:58:e9:
                    d8:2a:07:c6:12:db:58:db:d2:3b:55:10:47:05:15:
                    67:62:7e:18:63:a6:46:3f:09:0e:54:32:5e:bf:0d:
                    62:7a:27:ef:80:e8:db:d9:4b:06:5a:37:5a:25:d0:
                    08:12:77:d4:6f:09:50:97:3d:c8:1d:c3:df:8c:45:
                    30:56:c6:d3:64:ab:66:f3:c0:5e:96:9c:c3:c4:ef:
                    c3:7c:6b:8b:3a:79:7f:b3:49:cf:3d:e2:89:9f:a0:
                    30:4b:85:b9:9c:94:24:79:8f:7d:6b:a9:45:68:0f:
                    2b:d0:f1:da:1c:cb:69:b8:ca:49:62:6d:c8:d0:63:
                    62:dd:60:0f:58:aa:8f:a1:bc:05:a5:66:a2:cf:1b:
                    76:b2:84:64:b1:4c:39:52:c0:30:ba:f0:8c:4b:02:
                    b0:b6:b7
                Exponent: 65537 (0x10001)
        X509v3 extensions:
            X509v3 Basic Constraints: critical
                CA:TRUE
            X509v3 Subject Key Identifier: 
                47:B8:CD:FF:E5:6F:EE:F8:B2:EC:2F:4E:0E:F9:25:B0:8E:3C:6B:C3
            X509v3 Key Usage: critical
                Certificate Sign, CRL Sign
    Signature Algorithm: sha256WithRSAEncryption
         00:20:23:41:35:04:90:c2:40:62:60:ef:e2:35:4c:d7:3f:ac:
         e2:34:90:b8:a1:6f:76:fa:16:16:a4:48:37:2c:e9:90:c2:f2:
         3c:f8:0a:9f:d8:81:e5:bb:5b:da:25:2c:a4:a7:55:71:24:32:
         f6:c8:0b:f2:bc:6a:f8:93:ac:b2:07:c2:5f:9f:db:cc:c8:8a:
         aa:be:6a:6f:e1:49:10:cc:31:d7:80:bb:bb:c8:d8:a2:0e:64:
         57:ea:a2:f5:c2:a9:31:15:d2:20:6a:ec:fc:22:01:28:cf:86:
         b8:80:1e:a9:cc:11:a5:3c:f2:16:b3:47:9d:fc:d2:80:21:c4:
         cb:d0:47:70:41:a1:ca:83:19:08:2c:6d:f2:5d:77:9c:8a:14:
         13:d4:36:1c:92:f0:e5:06:37:dc:a6:e6:90:9b:38:8f:5c:6b:
         1b:46:86:43:42:5f:3e:01:07:53:54:5d:65:7d:f7:8a:73:a1:
         9a:54:5a:1f:29:43:14:27:c2:85:0f:b5:88:7b:1a:3b:94:b7:
         1d:60:a7:b5:9c:e7:29:69:57:5a:9b:93:7a:43:30:1b:03:d7:
         62:c8:40:a6:aa:fc:64:e4:4a:d7:91:53:01:a8:20:88:6e:9c:
         5f:44:b9:cb:60:81:34:ec:6f:d3:7d:da:48:5f:eb:b4:90:bc:
         2d:a9:1c:0b:ac:1c:d5:a2:68:20:80:04:d6:fc:b1:8f:2f:bb:
         4a:31:0d:4a:86:1c:eb:e2:36:29:26:f5:da:d8:c4:f2:75:61:
         cf:7e:ae:76:63:4a:7a:40:65:93:87:f8:1e:80:8c:86:e5:86:
         d6:8f:0e:fc:53:2c:60:e8:16:61:1a:a2:3e:43:7b:cd:39:60:
         54:6a:f5:f2:89:26:01:68:83:48:a2:33:e8:c9:04:91:b2:11:
         34:11:3e:ea:d0:43:19:1f:03:93:90:0c:ff:51:3d:57:f4:41:
         6e:e1:cb:a0:be:eb:c9:63:cd:6d:cc:e4:f8:36:aa:68:9d:ed:
         bd:5d:97:70:44:0d:b6:0e:35:dc:e1:0c:5d:bb:a0:51:94:cb:
         7e:16:eb:11:2f:a3:92:45:c8:4c:71:d9:bc:c9:99:52:57:46:
         2f:50:cf:bd:35:69:f4:3d:15:ce:06:a5:2c:0f:3e:f6:81:ba:
         94:bb:c3:bb:bf:65:78:d2:86:79:ff:49:3b:1a:83:0c:f0:de:
         78:ec:c8:f2:4d:4c:1a:de:82:29:f8:c1:5a:da:ed:ee:e6:27:
         5e:e8:45:d0:9d:1c:51:a8:68:ab:44:e3:d0:8b:6a:e3:f8:3b:
         bb:dc:4d:d7:64:f2:51:be:e6:aa:ab:5a:e9:31:ee:06:bc:73:
         bf:13:62:0a:9f:c7:b9:97
SHA1 Fingerprint=DA:FA:F7:FA:66:84:EC:06:8F:14:50:BD:C7:C2:81:A5:BC:A9:64:57
