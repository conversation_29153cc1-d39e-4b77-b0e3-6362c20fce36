# Organized by frequency of systemcall - in descending order for
# best performance.
ioctl: 1
futex: 1
prctl: 1
write: 1
getpriority: 1
mmap2: 1
close: 1
munmap: 1
dup: 1
mprotect: 1
getuid32: 1
setpriority: 1
sigaltstack: 1
openat: 1
clone: 1
read: 1
clock_gettime: 1
lseek: 1
writev: 1
fstatat64: 1
fstatfs64: 1
fstat64: 1
restart_syscall: 1
exit: 1
exit_group: 1
rt_sigreturn: 1
faccessat: 1
madvise: 1
brk: 1
sched_setscheduler: 1
gettid: 1
rt_sigprocmask: 1
sched_yield: 1
ugetrlimit: 1
geteuid32: 1
getgid32: 1
getegid32: 1
getgroups32: 1
nanosleep: 1
getrandom: 1

# for dynamically loading extractors
pread64: 1

# for FileSource
readlinkat: 1
_llseek: 1

@include /system/etc/seccomp_policy/crash_dump.arm.policy
