-----<PERSON><PERSON><PERSON> CERTIFICATE-----
MIIDWjCCAkKgAwIBAgIBADANBgkqhkiG9w0BAQUFADBQMQswCQYDVQQGEwJKUDEY
MBYGA1UEChMPU0VDT00gVHJ1c3QubmV0MScwJQYDVQQLEx5TZWN1cml0eSBDb21t
dW5pY2F0aW9uIFJvb3RDQTEwHhcNMDMwOTMwMDQyMDQ5WhcNMjMwOTMwMDQyMDQ5
WjBQMQswCQYDVQQGEwJKUDEYMBYGA1UEChMPU0VDT00gVHJ1c3QubmV0MScwJQYD
VQQLEx5TZWN1cml0eSBDb21tdW5pY2F0aW9uIFJvb3RDQTEwggEiMA0GCSqGSIb3
DQEBAQUAA4IBDwAwggEKAoIBAQCzs/5/022x7xZ8V6UMbXaKL0u/ZPtM7orw8yl8
9f/uKuDp6bpbZCKamm8sOiZpUQWZJtzVHGpxxpp9Hp3dfGzGjGdnSj74cbAZJ6kJ
DKaVv0uMDPpVmDvY6CKhS3E4eayXkmmziX7qIWgGmBSWh9JhNrxtJ1aeV+7AwFb9
Ms+k2Y7CI9eNqPPYJayX5HA49LY6tJ07lyZDo6G8SVlyTCMwhwFY9k6+HGhWZq/N
QV3Is00qVUarH9oe4kA92819uZKAnDfdDJZkndwi92SL32HeFZRSFaB9UslLqCHJ
xrHty8OVYNEP8Ktw+N/LTX7s1vqr2b1/VPKl6Xn62dZ2JChzAgMBAAGjPzA9MB0G
A1UdDgQWBBSgc0mZaNyFW2XjmygvV5+9M7wHSDALBgNVHQ8EBAMCAQYwDwYDVR0T
AQH/BAUwAwEB/zANBgkqhkiG9w0BAQUFAAOCAQEAaECpqLvkT115swW1F7NgE+vG
kl3g0dNq/vu+m22/xwVtWSDEHPC32oRYAmP6SBbvT6UL90qY8j+eG61Ha2POCEfr
Uj94nK9NrvjVT8+amCoQQTlSxN3Zmw7vkwGusi7KaEIkQmywszo+zenaSMQVy+n5
Bw+SUEmK3TGXX8npN6o7WWWXlDLJs58+OmJYxUmtYg5xpTKqL8aJdkNAExNnPaJU
JRDL8Try2frbSVa7pv6nQTXD4IhhyYjH3zYQIphZ6rBK+1YWc26sTfcioU+tHXot
RSflMMFe8toTyyVCUZVHA4xsIcx0Qu1T/zOLjw9XARYvz6buyXAiFL39vmwLAw==
-----END CERTIFICATE-----
Certificate:
    Data:
        Version: 3 (0x2)
        Serial Number: 0 (0x0)
    Signature Algorithm: sha1WithRSAEncryption
        Issuer: C=JP, O=SECOM Trust.net, OU=Security Communication RootCA1
        Validity
            Not Before: Sep 30 04:20:49 2003 GMT
            Not After : Sep 30 04:20:49 2023 GMT
        Subject: C=JP, O=SECOM Trust.net, OU=Security Communication RootCA1
        Subject Public Key Info:
            Public Key Algorithm: rsaEncryption
                Public-Key: (2048 bit)
                Modulus:
                    00:b3:b3:fe:7f:d3:6d:b1:ef:16:7c:57:a5:0c:6d:
                    76:8a:2f:4b:bf:64:fb:4c:ee:8a:f0:f3:29:7c:f5:
                    ff:ee:2a:e0:e9:e9:ba:5b:64:22:9a:9a:6f:2c:3a:
                    26:69:51:05:99:26:dc:d5:1c:6a:71:c6:9a:7d:1e:
                    9d:dd:7c:6c:c6:8c:67:67:4a:3e:f8:71:b0:19:27:
                    a9:09:0c:a6:95:bf:4b:8c:0c:fa:55:98:3b:d8:e8:
                    22:a1:4b:71:38:79:ac:97:92:69:b3:89:7e:ea:21:
                    68:06:98:14:96:87:d2:61:36:bc:6d:27:56:9e:57:
                    ee:c0:c0:56:fd:32:cf:a4:d9:8e:c2:23:d7:8d:a8:
                    f3:d8:25:ac:97:e4:70:38:f4:b6:3a:b4:9d:3b:97:
                    26:43:a3:a1:bc:49:59:72:4c:23:30:87:01:58:f6:
                    4e:be:1c:68:56:66:af:cd:41:5d:c8:b3:4d:2a:55:
                    46:ab:1f:da:1e:e2:40:3d:db:cd:7d:b9:92:80:9c:
                    37:dd:0c:96:64:9d:dc:22:f7:64:8b:df:61:de:15:
                    94:52:15:a0:7d:52:c9:4b:a8:21:c9:c6:b1:ed:cb:
                    c3:95:60:d1:0f:f0:ab:70:f8:df:cb:4d:7e:ec:d6:
                    fa:ab:d9:bd:7f:54:f2:a5:e9:79:fa:d9:d6:76:24:
                    28:73
                Exponent: 65537 (0x10001)
        X509v3 extensions:
            X509v3 Subject Key Identifier: 
                A0:73:49:99:68:DC:85:5B:65:E3:9B:28:2F:57:9F:BD:33:BC:07:48
            X509v3 Key Usage: 
                Certificate Sign, CRL Sign
            X509v3 Basic Constraints: critical
                CA:TRUE
    Signature Algorithm: sha1WithRSAEncryption
         68:40:a9:a8:bb:e4:4f:5d:79:b3:05:b5:17:b3:60:13:eb:c6:
         92:5d:e0:d1:d3:6a:fe:fb:be:9b:6d:bf:c7:05:6d:59:20:c4:
         1c:f0:b7:da:84:58:02:63:fa:48:16:ef:4f:a5:0b:f7:4a:98:
         f2:3f:9e:1b:ad:47:6b:63:ce:08:47:eb:52:3f:78:9c:af:4d:
         ae:f8:d5:4f:cf:9a:98:2a:10:41:39:52:c4:dd:d9:9b:0e:ef:
         93:01:ae:b2:2e:ca:68:42:24:42:6c:b0:b3:3a:3e:cd:e9:da:
         48:c4:15:cb:e9:f9:07:0f:92:50:49:8a:dd:31:97:5f:c9:e9:
         37:aa:3b:59:65:97:94:32:c9:b3:9f:3e:3a:62:58:c5:49:ad:
         62:0e:71:a5:32:aa:2f:c6:89:76:43:40:13:13:67:3d:a2:54:
         25:10:cb:f1:3a:f2:d9:fa:db:49:56:bb:a6:fe:a7:41:35:c3:
         e0:88:61:c9:88:c7:df:36:10:22:98:59:ea:b0:4a:fb:56:16:
         73:6e:ac:4d:f7:22:a1:4f:ad:1d:7a:2d:45:27:e5:30:c1:5e:
         f2:da:13:cb:25:42:51:95:47:03:8c:6c:21:cc:74:42:ed:53:
         ff:33:8b:8f:0f:57:01:16:2f:cf:a6:ee:c9:70:22:14:bd:fd:
         be:6c:0b:03
SHA1 Fingerprint=36:B1:2B:49:F9:81:9E:D7:4C:9E:BC:38:0F:C6:56:8F:5D:AC:B2:F7
