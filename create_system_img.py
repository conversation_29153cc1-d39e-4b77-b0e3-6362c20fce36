#!/usr/bin/env python3
"""
Simple System.img Creator for Android
Creates a basic ext4 filesystem image from system directory
"""

import os
import sys
import subprocess
import shutil
import tempfile
from pathlib import Path

def check_wsl():
    """Check if WSL is available"""
    try:
        result = subprocess.run(['wsl', '--version'], 
                              capture_output=True, text=True, timeout=5)
        return result.returncode == 0
    except:
        return False

def get_system_info():
    """Parse system_info.txt to get filesystem parameters"""
    info_file = Path('system_info.txt')
    if not info_file.exists():
        print("Error: system_info.txt not found!")
        return None
    
    info = {}
    with open(info_file, 'r') as f:
        for line in f:
            if 'Block count:' in line:
                info['block_count'] = int(line.split(':')[1].strip())
            elif 'Block size:' in line:
                info['block_size'] = int(line.split(':')[1].strip())
            elif 'Inode count:' in line:
                info['inode_count'] = int(line.split(':')[1].strip())
    
    return info

def create_with_wsl(info):
    """Create system.img using WSL"""
    size_bytes = info['block_count'] * info['block_size']
    
    print("Using WSL to create system.img...")
    
    # Create the image using WSL
    wsl_commands = f"""
    set -e
    echo "Creating empty image file..."
    dd if=/dev/zero of=system.img bs={info['block_size']} count={info['block_count']}
    
    echo "Formatting as ext4..."
    mke2fs -t ext4 -F -b {info['block_size']} -N {info['inode_count']} system.img
    
    echo "Mounting image..."
    mkdir -p /tmp/system_mount_$$
    sudo mount -o loop system.img /tmp/system_mount_$$
    
    echo "Copying system files..."
    sudo cp -a system/* /tmp/system_mount_$$/
    
    echo "Unmounting..."
    sudo umount /tmp/system_mount_$$
    rmdir /tmp/system_mount_$$
    
    echo "Setting permissions..."
    chmod 644 system.img
    
    echo "System image created successfully!"
    """
    
    try:
        result = subprocess.run(['wsl', 'bash', '-c', wsl_commands], 
                              capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("WSL method succeeded!")
            return True
        else:
            print(f"WSL method failed: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("WSL method timed out")
        return False
    except Exception as e:
        print(f"WSL method error: {e}")
        return False

def create_with_python(info):
    """Create a basic system.img using Python (fallback method)"""
    print("Creating basic system.img using Python fallback method...")
    print("Note: This creates a simple archive, not a proper ext4 filesystem")
    
    size_bytes = info['block_count'] * info['block_size']
    
    try:
        # Create empty file
        with open('system.img', 'wb') as f:
            # Write zeros to create the file of correct size
            chunk_size = 1024 * 1024  # 1MB chunks
            remaining = size_bytes
            
            while remaining > 0:
                write_size = min(chunk_size, remaining)
                f.write(b'\x00' * write_size)
                remaining -= write_size
                
                # Show progress
                progress = (size_bytes - remaining) / size_bytes * 100
                print(f"\rProgress: {progress:.1f}%", end='', flush=True)
        
        print(f"\nCreated {size_bytes} byte file")
        
        # This is a very basic approach - just creates an empty file
        # A real ext4 filesystem would need proper formatting
        print("Warning: This is a basic empty file, not a proper ext4 filesystem")
        print("For a proper system.img, you need make_ext4fs or WSL with e2fsprogs")
        
        return True
        
    except Exception as e:
        print(f"Python method failed: {e}")
        return False

def download_make_ext4fs():
    """Provide instructions for downloading make_ext4fs"""
    print("\n" + "="*50)
    print("  RECOMMENDED: Download make_ext4fs.exe")
    print("="*50)
    print()
    print("For the best results, download make_ext4fs.exe:")
    print()
    print("1. Visit: https://github.com/osm0sis/make_ext4fs-Windows")
    print("2. Download the latest release")
    print("3. Extract make_ext4fs.exe to this directory")
    print("4. Run build_system_correct.bat")
    print()
    print("Alternative sources:")
    print("- Android Kitchen tools")
    print("- SuperR's Kitchen")
    print("- Android SDK build tools")
    print()

def main():
    print("Android System.img Creator")
    print("=" * 40)
    
    # Check if system directory exists
    if not Path('system').exists():
        print("Error: system directory not found!")
        sys.exit(1)
    
    # Get system info
    info = get_system_info()
    if not info:
        sys.exit(1)
    
    print(f"System parameters:")
    print(f"  Block count: {info['block_count']}")
    print(f"  Block size: {info['block_size']}")
    print(f"  Inode count: {info['inode_count']}")
    print(f"  Total size: {info['block_count'] * info['block_size'] // 1024 // 1024} MB")
    print()
    
    # Check for make_ext4fs first
    if Path('make_ext4fs.exe').exists():
        print("Found make_ext4fs.exe - please run build_system_correct.bat instead")
        sys.exit(0)
    
    # Try WSL method
    if check_wsl():
        print("WSL detected, attempting to use Linux tools...")
        if create_with_wsl(info):
            print("\nSuccess! system.img created using WSL")
            if Path('system.img').exists():
                size = Path('system.img').stat().st_size
                print(f"File size: {size // 1024 // 1024} MB")
            return
        else:
            print("WSL method failed, trying fallback...")
    
    # Fallback method
    print("Using Python fallback method...")
    if create_with_python(info):
        print("\nBasic system.img file created")
        if Path('system.img').exists():
            size = Path('system.img').stat().st_size
            print(f"File size: {size // 1024 // 1024} MB")
    
    # Show download instructions
    download_make_ext4fs()

if __name__ == '__main__':
    main()
