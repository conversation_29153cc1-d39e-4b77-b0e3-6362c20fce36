@echo off
echo ========================================
echo   Android System Image Packing Tool
echo   Using APF Tool from aihaven.top
echo ========================================

REM Check if apftool exists
if not exist "apftool.exe" (
    echo Error: apftool.exe not found!
    echo Please download APF Tool from: https://apftool-rs.aihaven.top/
    echo And place apftool.exe in this directory.
    echo.
    pause
    exit /b 1
)

REM Get system info from system_info.txt
echo Reading system parameters...
for /f "tokens=3" %%a in ('findstr "Block count:" system_info.txt') do set BLOCK_COUNT=%%a
for /f "tokens=3" %%a in ('findstr "Block size:" system_info.txt') do set BLOCK_SIZE=%%a
for /f "tokens=3" %%a in ('findstr "Inode count:" system_info.txt') do set INODE_COUNT=%%a

echo Block Count: %BLOCK_COUNT%
echo Block Size: %BLOCK_SIZE%
echo Inode Count: %INODE_COUNT%

REM Calculate size in bytes
set /a SIZE_BYTES=%BLOCK_COUNT% * %BLOCK_SIZE%
set /a SIZE_MB=%SIZE_BYTES% / 1024 / 1024

echo Total Size: %SIZE_MB% MB (%SIZE_BYTES% bytes)
echo.

REM Create system.img using APF tool
echo Creating system.img using APF tool...
echo Command: apftool.exe pack system system.img

apftool.exe pack system system.img

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo   SUCCESS: system.img created!
    echo ========================================
    echo.
    if exist system.img (
        for %%A in (system.img) do (
            set SIZE=%%~zA
            set /a SIZE_MB=!SIZE! / 1024 / 1024
            echo File: system.img
            echo Size: !SIZE_MB! MB ^(!SIZE! bytes^)
        )
    )
    echo.
    echo The system.img is ready for flashing!
) else (
    echo.
    echo ========================================
    echo   ERROR: Failed to create system.img
    echo ========================================
    echo Error code: %ERRORLEVEL%
    echo.
    echo Possible solutions:
    echo 1. Make sure apftool.exe is the correct version
    echo 2. Check if system directory is complete
    echo 3. Ensure sufficient disk space
    echo 4. Try running as administrator
)

echo.
pause
