#!/system/bin/sh

LOG="/system/bin/log -t gitv_launcher"
ECHO()
{
    echo $*
    $LOG "$*"
}

#sleep 2
function promoteThreadsToRT()
{
       str1=`ps -Teo PID,TID,S,CMD | grep -i $1 | grep -v grep| busybox awk '{print $2}'`

        if [ "$str1" != "" ] ; then
                ECHO "Thread $1 exist"
                ECHO "promoteThreadsToRT: chrt for $str1 to $2"
                chrt -p $str1 -f "$2"
                return 1
        else
                ECHO "Thread $1 not exist"
                return 0
        fi
}
#sleep .2
promoteThreadsToRT m.gitv.launcher 95
if [ $? -eq 1 ] ; then
  ECHO "PROMOTE GITV Launcher main thread scuess!!"
fi

