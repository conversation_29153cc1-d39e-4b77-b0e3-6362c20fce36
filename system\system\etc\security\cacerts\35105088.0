-----<PERSON><PERSON><PERSON> CERTIFICATE-----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-----END CERTIFICATE-----
Certificate:
    Data:
        Version: 3 (0x2)
        Serial Number:
            01:fd:6d:30:fc:a3:ca:51:a8:1b:bc:64:0e:35:03:2d
    Signature Algorithm: sha384WithRSAEncryption
        Issuer: C=US, ST=New Jersey, L=Jersey City, O=The USERTRUST Network, CN=USERTrust RSA Certification Authority
        Validity
            Not Before: Feb  1 00:00:00 2010 GMT
            Not After : Jan 18 23:59:59 2038 GMT
        Subject: C=US, ST=New Jersey, L=Jersey City, O=The USERTRUST Network, CN=USERTrust RSA Certification Authority
        Subject Public Key Info:
            Public Key Algorithm: rsaEncryption
                Public-Key: (4096 bit)
                Modulus:
                    00:80:12:65:17:36:0e:c3:db:08:b3:d0:ac:57:0d:
                    76:ed:cd:27:d3:4c:ad:50:83:61:e2:aa:20:4d:09:
                    2d:64:09:dc:ce:89:9f:cc:3d:a9:ec:f6:cf:c1:dc:
                    f1:d3:b1:d6:7b:37:28:11:2b:47:da:39:c6:bc:3a:
                    19:b4:5f:a6:bd:7d:9d:a3:63:42:b6:76:f2:a9:3b:
                    2b:91:f8:e2:6f:d0:ec:16:20:90:09:3e:e2:e8:74:
                    c9:18:b4:91:d4:62:64:db:7f:a3:06:f1:88:18:6a:
                    90:22:3c:bc:fe:13:f0:87:14:7b:f6:e4:1f:8e:d4:
                    e4:51:c6:11:67:46:08:51:cb:86:14:54:3f:bc:33:
                    fe:7e:6c:9c:ff:16:9d:18:bd:51:8e:35:a6:a7:66:
                    c8:72:67:db:21:66:b1:d4:9b:78:03:c0:50:3a:e8:
                    cc:f0:dc:bc:9e:4c:fe:af:05:96:35:1f:57:5a:b7:
                    ff:ce:f9:3d:b7:2c:b6:f6:54:dd:c8:e7:12:3a:4d:
                    ae:4c:8a:b7:5c:9a:b4:b7:20:3d:ca:7f:22:34:ae:
                    7e:3b:68:66:01:44:e7:01:4e:46:53:9b:33:60:f7:
                    94:be:53:37:90:73:43:f3:32:c3:53:ef:db:aa:fe:
                    74:4e:69:c7:6b:8c:60:93:de:c4:c7:0c:df:e1:32:
                    ae:cc:93:3b:51:78:95:67:8b:ee:3d:56:fe:0c:d0:
                    69:0f:1b:0f:f3:25:26:6b:33:6d:f7:6e:47:fa:73:
                    43:e5:7e:0e:a5:66:b1:29:7c:32:84:63:55:89:c4:
                    0d:c1:93:54:30:19:13:ac:d3:7d:37:a7:eb:5d:3a:
                    6c:35:5c:db:41:d7:12:da:a9:49:0b:df:d8:80:8a:
                    09:93:62:8e:b5:66:cf:25:88:cd:84:b8:b1:3f:a4:
                    39:0f:d9:02:9e:eb:12:4c:95:7c:f3:6b:05:a9:5e:
                    16:83:cc:b8:67:e2:e8:13:9d:cc:5b:82:d3:4c:b3:
                    ed:5b:ff:de:e5:73:ac:23:3b:2d:00:bf:35:55:74:
                    09:49:d8:49:58:1a:7f:92:36:e6:51:92:0e:f3:26:
                    7d:1c:4d:17:bc:c9:ec:43:26:d0:bf:41:5f:40:a9:
                    44:44:f4:99:e7:57:87:9e:50:1f:57:54:a8:3e:fd:
                    74:63:2f:b1:50:65:09:e6:58:42:2e:43:1a:4c:b4:
                    f0:25:47:59:fa:04:1e:93:d4:26:46:4a:50:81:b2:
                    de:be:78:b7:fc:67:15:e1:c9:57:84:1e:0f:63:d6:
                    e9:62:ba:d6:5f:55:2e:ea:5c:c6:28:08:04:25:39:
                    b8:0e:2b:a9:f2:4c:97:1c:07:3f:0d:52:f5:ed:ef:
                    2f:82:0f
                Exponent: 65537 (0x10001)
        X509v3 extensions:
            X509v3 Subject Key Identifier: 
                53:79:BF:5A:AA:2B:4A:CF:54:80:E1:D8:9B:C0:9D:F2:B2:03:66:CB
            X509v3 Key Usage: critical
                Certificate Sign, CRL Sign
            X509v3 Basic Constraints: critical
                CA:TRUE
    Signature Algorithm: sha384WithRSAEncryption
         5c:d4:7c:0d:cf:f7:01:7d:41:99:65:0c:73:c5:52:9f:cb:f8:
         cf:99:06:7f:1b:da:43:15:9f:9e:02:55:57:96:14:f1:52:3c:
         27:87:94:28:ed:1f:3a:01:37:a2:76:fc:53:50:c0:84:9b:c6:
         6b:4e:ba:8c:21:4f:a2:8e:55:62:91:f3:69:15:d8:bc:88:e3:
         c4:aa:0b:fd:ef:a8:e9:4b:55:2a:06:20:6d:55:78:29:19:ee:
         5f:30:5c:4b:24:11:55:ff:24:9a:6e:5e:2a:2b:ee:0b:4d:9f:
         7f:f7:01:38:94:14:95:43:07:09:fb:60:a9:ee:1c:ab:12:8c:
         a0:9a:5e:a7:98:6a:59:6d:8b:3f:08:fb:c8:d1:45:af:18:15:
         64:90:12:0f:73:28:2e:c5:e2:24:4e:fc:58:ec:f0:f4:45:fe:
         22:b3:eb:2f:8e:d2:d9:45:61:05:c1:97:6f:a8:76:72:8f:8b:
         8c:36:af:bf:0d:05:ce:71:8d:e6:a6:6f:1f:6c:a6:71:62:c5:
         d8:d0:83:72:0c:f1:67:11:89:0c:9c:13:4c:72:34:df:bc:d5:
         71:df:aa:71:dd:e1:b9:6c:8c:3c:12:5d:65:da:bd:57:12:b6:
         43:6b:ff:e5:de:4d:66:11:51:cf:99:ae:ec:17:b6:e8:71:91:
         8c:de:49:fe:dd:35:71:a2:15:27:94:1c:cf:61:e3:26:bb:6f:
         a3:67:25:21:5d:e6:dd:1d:0b:2e:68:1b:3b:82:af:ec:83:67:
         85:d4:98:51:74:b1:b9:99:80:89:ff:7f:78:19:5c:79:4a:60:
         2e:92:40:ae:4c:37:2a:2c:c9:c7:62:c8:0e:5d:f7:36:5b:ca:
         e0:25:25:01:b4:dd:1a:07:9c:77:00:3f:d0:dc:d5:ec:3d:d4:
         fa:bb:3f:cc:85:d6:6f:7f:a9:2d:df:b9:02:f7:f5:97:9a:b5:
         35:da:c3:67:b0:87:4a:a9:28:9e:23:8e:ff:5c:27:6b:e1:b0:
         4f:f3:07:ee:00:2e:d4:59:87:cb:52:41:95:ea:f4:47:d7:ee:
         64:41:55:7c:8d:59:02:95:dd:62:9d:c2:b9:ee:5a:28:74:84:
         a5:9b:b7:90:c7:0c:07:df:f5:89:36:74:32:d6:28:c1:b0:b0:
         0b:e0:9c:4c:c3:1c:d6:fc:e3:69:b5:47:46:81:2f:a2:82:ab:
         d3:63:44:70:c4:8d:ff:2d:33:ba:ad:8f:7b:b5:70:88:ae:3e:
         19:cf:40:28:d8:fc:c8:90:bb:5d:99:22:f5:52:e6:58:c5:1f:
         88:31:43:ee:88:1d:d7:c6:8e:3c:43:6a:1d:a7:18:de:7d:3d:
         16:f1:62:f9:ca:90:a8:fd
SHA1 Fingerprint=2B:8F:1B:57:33:0D:BB:A2:D0:7A:6C:51:F7:0E:E9:0D:DA:B9:AD:8E
