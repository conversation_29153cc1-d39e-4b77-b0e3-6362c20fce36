-----<PERSON><PERSON><PERSON> CERTIFICATE-----
MIIB/jCCAYWgAwIBAgIIdJclisc/elQwCgYIKoZIzj0EAwMwRTELMAkGA1UEBhMC
VVMxFDASBgNVBAoMC0FmZmlybVRydXN0MSAwHgYDVQQDDBdBZmZpcm1UcnVzdCBQ
cmVtaXVtIEVDQzAeFw0xMDAxMjkxNDIwMjRaFw00MDEyMzExNDIwMjRaMEUxCzAJ
BgNVBAYTAlVTMRQwEgYDVQQKDAtBZmZpcm1UcnVzdDEgMB4GA1UEAwwXQWZmaXJt
VHJ1c3QgUHJlbWl1bSBFQ0MwdjAQBgcqhkjOPQIBBgUrgQQAIgNiAAQNMF4bFZ0D
0KF5Nbc6PJJ6yhUczWLznCZcBz3lVPqj1swS6vQUX+iOGasvLkjmrBhDeKzQN8O9
ss0s5kfiGuZjuD0uL3jET9v0D6RoTFVya5UdThhClXjMNzyR4ptlKymjQjBAMB0G
A1UdDgQWBBSaryl6wBE1NSZRMADDav5A1a7WPDAPBgNVHRMBAf8EBTADAQH/MA4G
A1UdDwEB/wQEAwIBBjAKBggqhkjOPQQDAwNnADBkAjAXCfOHiFBar8jAQr9HX/Vs
aobgxCd05DhT1wV/GzTjxi+zygk8N53X57hG8f2h4nECMEJZh0PUUd+60wkyWs6I
flc9nF9Ca/UHLbXwgpP5WW+uZPpY5Yse42O+tYHNbwKMeQ==
-----END CERTIFICATE-----
Certificate:
    Data:
        Version: 3 (0x2)
        Serial Number: 8401224907861490260 (0x7497258ac73f7a54)
    Signature Algorithm: ecdsa-with-SHA384
        Issuer: C=US, O=AffirmTrust, CN=AffirmTrust Premium ECC
        Validity
            Not Before: Jan 29 14:20:24 2010 GMT
            Not After : Dec 31 14:20:24 2040 GMT
        Subject: C=US, O=AffirmTrust, CN=AffirmTrust Premium ECC
        Subject Public Key Info:
            Public Key Algorithm: id-ecPublicKey
                Public-Key: (384 bit)
                pub: 
                    04:0d:30:5e:1b:15:9d:03:d0:a1:79:35:b7:3a:3c:
                    92:7a:ca:15:1c:cd:62:f3:9c:26:5c:07:3d:e5:54:
                    fa:a3:d6:cc:12:ea:f4:14:5f:e8:8e:19:ab:2f:2e:
                    48:e6:ac:18:43:78:ac:d0:37:c3:bd:b2:cd:2c:e6:
                    47:e2:1a:e6:63:b8:3d:2e:2f:78:c4:4f:db:f4:0f:
                    a4:68:4c:55:72:6b:95:1d:4e:18:42:95:78:cc:37:
                    3c:91:e2:9b:65:2b:29
                ASN1 OID: secp384r1
        X509v3 extensions:
            X509v3 Subject Key Identifier: 
                9A:AF:29:7A:C0:11:35:35:26:51:30:00:C3:6A:FE:40:D5:AE:D6:3C
            X509v3 Basic Constraints: critical
                CA:TRUE
            X509v3 Key Usage: critical
                Certificate Sign, CRL Sign
    Signature Algorithm: ecdsa-with-SHA384
         30:64:02:30:17:09:f3:87:88:50:5a:af:c8:c0:42:bf:47:5f:
         f5:6c:6a:86:e0:c4:27:74:e4:38:53:d7:05:7f:1b:34:e3:c6:
         2f:b3:ca:09:3c:37:9d:d7:e7:b8:46:f1:fd:a1:e2:71:02:30:
         42:59:87:43:d4:51:df:ba:d3:09:32:5a:ce:88:7e:57:3d:9c:
         5f:42:6b:f5:07:2d:b5:f0:82:93:f9:59:6f:ae:64:fa:58:e5:
         8b:1e:e3:63:be:b5:81:cd:6f:02:8c:79
SHA1 Fingerprint=B8:23:6B:00:2F:1D:16:86:53:01:55:6C:11:A4:37:CA:EB:FF:C3:BB
