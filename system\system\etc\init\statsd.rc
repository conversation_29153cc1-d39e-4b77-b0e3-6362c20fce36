# Copyright (C) 2017 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

service statsd /system/bin/statsd
    class main
    socket statsdw dgram+passcred 0222 statsd statsd
    user statsd
    group statsd log
    writepid /dev/cpuset/system-background/tasks

on property:ro.statsd.enable=false
    stop statsd

on post-fs-data
    # Create directory for statsd
    mkdir /data/misc/stats-data/ 0770 statsd system
    mkdir /data/misc/stats-service/ 0770 statsd system
