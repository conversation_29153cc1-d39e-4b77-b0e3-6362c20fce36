@echo off
echo ========================================
echo   Android System.img Builder
echo   For RK3528 SY910 System
echo ========================================

REM Get system parameters from system_info.txt
echo Reading system parameters...
for /f "tokens=3" %%a in ('findstr "Block count:" system_info.txt') do set BLOCK_COUNT=%%a
for /f "tokens=3" %%a in ('findstr "Block size:" system_info.txt') do set BLOCK_SIZE=%%a
for /f "tokens=3" %%a in ('findstr "Inode count:" system_info.txt') do set INODE_COUNT=%%a

echo Block Count: %BLOCK_COUNT%
echo Block Size: %BLOCK_SIZE%
echo Inode Count: %INODE_COUNT%

REM Calculate size in bytes
set /a SIZE_BYTES=%BLOCK_COUNT% * %BLOCK_SIZE%
set /a SIZE_MB=%SIZE_BYTES% / 1024 / 1024

echo Total Size: %SIZE_MB% MB (%SIZE_BYTES% bytes)
echo.

REM Check for make_ext4fs tool
if exist "make_ext4fs.exe" (
    echo Using make_ext4fs.exe...
    echo Command: make_ext4fs.exe -s -l %SIZE_BYTES% -a system -S system_file_contexts.txt -C system_fs_config.txt system.img system/
    
    make_ext4fs.exe -s -l %SIZE_BYTES% -a system -S system_file_contexts.txt -C system_fs_config.txt system.img system/
    
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo ========================================
        echo   SUCCESS: system.img created!
        echo ========================================
        goto :success
    ) else (
        echo Error: make_ext4fs failed with code %ERRORLEVEL%
        goto :error
    )
) else (
    echo make_ext4fs.exe not found, trying alternative method...
    goto :alternative
)

:alternative
REM Try using mke2fs if available (requires WSL or Linux tools)
echo.
echo Trying alternative method with mke2fs...
echo This requires WSL (Windows Subsystem for Linux) or Linux tools

wsl --version >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo WSL detected, using Linux tools...
    wsl bash -c "
    if command -v mke2fs >/dev/null 2>&1; then
        echo 'Creating empty image file...'
        dd if=/dev/zero of=system.img bs=%BLOCK_SIZE% count=%BLOCK_COUNT%
        echo 'Formatting as ext4...'
        mke2fs -t ext4 -F -b %BLOCK_SIZE% -N %INODE_COUNT% system.img
        echo 'Mounting and copying files...'
        mkdir -p /tmp/system_mount
        sudo mount -o loop system.img /tmp/system_mount
        sudo cp -a system/* /tmp/system_mount/
        sudo umount /tmp/system_mount
        rmdir /tmp/system_mount
        echo 'System image created successfully!'
    else
        echo 'Error: mke2fs not found in WSL'
        exit 1
    fi
    "
    if %ERRORLEVEL% EQU 0 (
        goto :success
    ) else (
        echo WSL method failed
        goto :error
    )
) else (
    echo WSL not available
    goto :download_tools
)

:download_tools
echo.
echo ========================================
echo   TOOLS NEEDED
echo ========================================
echo To create system.img, you need one of these tools:
echo.
echo 1. make_ext4fs.exe (Recommended)
echo    Download from: https://github.com/osm0sis/make_ext4fs-Windows
echo    Place make_ext4fs.exe in this directory
echo.
echo 2. Install WSL (Windows Subsystem for Linux)
echo    Run: wsl --install
echo    Then install e2fsprogs in WSL: sudo apt install e2fsprogs
echo.
echo 3. Use Android SDK tools
echo    Install Android SDK and use the build tools
echo.
goto :end

:success
if exist system.img (
    for %%A in (system.img) do (
        set SIZE=%%~zA
        set /a SIZE_MB=!SIZE! / 1024 / 1024
        echo File: system.img
        echo Size: !SIZE_MB! MB ^(!SIZE! bytes^)
    )
    echo.
    echo The system.img is ready for flashing!
    echo.
    echo Modified features:
    echo - Default launcher: com.kozyax.korgutv
    echo - Original launcher disabled: dangbei.apk.bak
    echo - System properties updated in build.prop
) else (
    echo Error: system.img was not created
)
goto :end

:error
echo.
echo ========================================
echo   BUILD FAILED
echo ========================================
echo Please check the error messages above and try again.
echo.

:end
pause
