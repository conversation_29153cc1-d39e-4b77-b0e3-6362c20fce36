[global]
   netbios name = Android
   workgroup = WORKGROUP
   display charset =UTF-8
   unix charset=UTF-8
   doc charset = cp936
   client lanman auth = yes
   lanman auth = yes
   server string = Rockchip Android
   dns proxy = no
   interfaces = lo wlan0 rndis0 
   #bind interfaces only = yes
 
   security = user
   username map =/etc/smbusers
   encrypt passwords = true
   passdb backend = smbpasswd
   smb passwd file = /system/etc/smbpasswd
 
   #load printers = no
   #printcap name = /dev/null
   #map to guest = bad user
   usershare allow guests = yes

[INTERNAL STORAGE]
   comment=Internal Storages
   path=/mnt/sdcard
   browseable=yes
   writeable=yes
   public = yes
   available = yes

[EXTERNAL SD]
    comment=External SD
    path=/mnt/external_sd
    browseable=yes
    writeable=yes
    public = yes
    available = yes

[USB STORAGES]
    comment=USB Storages
    path=/mnt/usb_storage
    browseable=yes
    writeable=yes
    public = yes
    available = yes

