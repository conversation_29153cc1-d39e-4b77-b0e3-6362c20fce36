#!/system/bin/sh
# ../scripts/pppoe-start.  Generated from pppoe-start.in by configure.
#***********************************************************************
#
# pppoe-start
#
# Shell script to bring up a PPPoE connection
#
# Copyright (C) 2000 Roaring Penguin Software Inc.
#
# $Id$
#
# This file may be distributed under the terms of the GNU General
# Public License.
#
# LIC: GPL
#
# Usage: pppoe-start [config_file]
#        pppoe-start interface user [config_file]
# Second form overrides USER and ETH from config file.
# If config_file is omitted, defaults to /etc/ppp/pppoe.conf
#
#***********************************************************************

# From AUTOCONF
prefix=/usr
exec_prefix=${prefix}

SETPROP=/system/bin/setprop
#CHANGHONG add begin *********: from rk patch 20240808, wifi&ipoe&pppoe passwrod encrypt
PPP_DIR=/data/misc/ppp
#CHANGHONG add end *********

# Paths to programs
CONNECT=/system/bin/pppoe-connect
ECHO="/system/bin/log -t pppoe_start"
#ECHO=echo
#IFCONFIG=$BUSYBOX ifconfig
PPPD_PIDFILE="$PIDFILE.pppd"
BUSYBOX=busybox

$ECHO "setprop net.pppoe.status connecting"
$SETPROP "net.pppoe.status" "connecting"

# Set to "C" locale so we can parse messages from commands
LANG=C
export LANG

# Defaults
#LOG=/data/misc/ppp/log
LOG=/dev/console
CONFIG=/data/misc/ppp/pppoe.conf
USER=""
ETH=""
ME=`$BUSYBOX basename $0`
# Must be root
#if [ "`/usr/bin/id -u`" != 0 ] ; then
#    $ECHO "$ME: You must be root to run this script" >& 2
#    exit 1
#fi

#$SETPROP "net.pppoe.status" ""
#$ECHO "setprop net.pppoe.status"     

# Debugging
if [ "$DEBUG" = "1" ] ; then
    $ECHO "*** Running in debug mode... please be patient..."
    DEBUG=/data/misc/ppp/pppoe-debug-$$
    export DEBUG
    mkdir $DEBUG
    if [ "$?" != 0 ] ; then
	$ECHO "Could not create directory $DEBUG... exiting"
	exit 1
    fi
    DEBUG=$DEBUG/pppoe-debug.txt

    # Initial debug output
    $ECHO "---------------------------------------------" > $DEBUG
    $ECHO "* The following section contains information about your system" >> $DEBUG
    date >> $DEBUG
    $ECHO "Output of uname -a" >> $DEBUG
    $BUSYBOX uname -a >> $DEBUG
    $ECHO "---------------------------------------------" >> $DEBUG
    $ECHO "* The following section contains information about your network" >> $DEBUG
    $ECHO "* interfaces.  The one you chose for PPPoE should contain the words:" >> $DEBUG
    $ECHO "* 'UP' and 'RUNNING'.  If it does not, you probably have an Ethernet" >> $DEBUG
    $ECHO "* driver problem." >> $DEBUG
    $ECHO "Output of ifconfig -a" >> $DEBUG
    $BUSYBOX ifconfig -a >> $DEBUG
    $ECHO "---------------------------------------------" >> $DEBUG
    if [ "`$BUSYBOX uname -s`" = "Linux" ] ; then
        $ECHO "* The following section contains information about kernel modules" >> $DEBUG
	$ECHO "* If the module for your Ethernet card is 'tulip', you might" >> $DEBUG
	$ECHO "* want to look for an updated version at http://www.scyld.com" >> $DEBUG
	$ECHO "Output of lsmod" >> $DEBUG
	lsmod >> $DEBUG
	$ECHO "---------------------------------------------" >> $DEBUG
    fi
    $ECHO "* The following section lists your routing table." >> $DEBUG
    $ECHO "* If you have an entry which starts with '0.0.0.0', you probably" >> $DEBUG
    $ECHO "* have defined a default route and gateway, and pppd will" >> $DEBUG
    $ECHO "* not create a default route using your ISP.  Try getting" >> $DEBUG
    $ECHO "* rid of this route." >> $DEBUG
    $ECHO "Output of netstat -n -r" >> $DEBUG
    $BUSYBOX netstat -n -r >> $DEBUG
    $ECHO "---------------------------------------------" >> $DEBUG
    $ECHO "Contents of /etc/resolv.conf" >> $DEBUG
    $ECHO "* The following section lists DNS setup." >> $DEBUG
    $ECHO "* If you can browse by IP address, but not name, suspect" >> $DEBUG
    $ECHO "* a DNS problem." >> $DEBUG
    cat /etc/resolv.conf >> $DEBUG
    $ECHO "---------------------------------------------" >> $DEBUG
    $ECHO "* The following section lists /etc/ppp/options." >> $DEBUG
    $ECHO "* You should have NOTHING in that file." >> $DEBUG
    $ECHO "Contents of /etc/ppp/options" >> $DEBUG
    cat /etc/ppp/options >> $DEBUG 2>/dev/null
    $ECHO "---------------------------------------------" >> $DEBUG
else
    DEBUG=""
fi

# Sort out command-line arguments
case "$#" in
    1)
	CONFIG="$1"
	;;
    3)
	CONFIG="$3"
	;;
esac


if [ ! -f "$CONFIG" -o ! -r "$CONFIG" ] ; then
    $ECHO "$ME: Cannot read configuration file '$CONFIG'"     
    exit 1
fi
export CONFIG
. $CONFIG

# Check for command-line overriding of ETH and USER
case "$#" in
    2|3)
	ETH="$1"
	USER="$2"
	;;
esac

# Check for pidfile
if [ -r "$PIDFILE" ] ; then
    PID=`cat "$PIDFILE"`
    # Check if still running
    kill -0 $PID > /dev/null 2>&1
    if [ $? = 0 ] ; then

        processLine=`ps | grep $PID`
        ProcessName=`echo $processLine | $BUSYBOX awk '{print $9}'`
        if [ $ProcessName = /system/bin/pppd ]; then
              $ECHO "$ME: There already seems to be a PPPoE connection up (PID $PID)"
              exit 1
        fi

    fi
    # Delete bogus PIDFILE
    $BUSYBOX rm -f "$PIDFILE" "$PPPD_PIDFILE" "$PIDFILE.pppoe" "$PIDFILE.start"
fi

echo $$ > $PIDFILE.start

#save default gateway
#DEFAULT_GW=/data/misc/ppp/default_gw
#$BUSYBOX rm $DEFAULT_GW
#$BUSYBOX route > $DEFAULT_GW
#cat $DEFAULT_GW|while read line
#    do
#      t1=`echo "$line" | $BUSYBOX awk  '{print $1}'`
#      if [ $t1 = "default" ] ; then
#          $BUSYBOX route del default
#      fi
#    done

# Start the connection in the background unless we're debugging
if [ "$DEBUG" != "" ] ; then
    $CONNECT "$@"
    exit 0
fi

$ECHO "$CONNECT $@"
$CONNECT "$@" > /dev/null 2>&1 &
CONNECT_PID=$!

if [ "$CONNECT_TIMEOUT" = "" -o "$CONNECT_TIMEOUT" = 0 ] ; then
    exit 0
fi

# Don't monitor connection if dial-on-demand
if [ "$DEMAND" != "" -a "$DEMAND" != "no" ] ; then
    exit 0
fi

# Monitor connection
TIME=0
while [ true ] ; do
    /system/bin/pppoe-status $CONFIG > /dev/null 2>&1

    # Looks like the interface came up
    if [ $? = 0 ] ; then
	# Print newline if standard input is a TTY
	$BUSYBOX tty -s && $ECHO " Connected!"
	$ECHO "setprop net.pppoe.status connected"     
	$SETPROP "net.pppoe.status" "connected"
	chmod 644 /data/misc/ppp/resolv.conf
    #CHANGHONG add begin *********: from rk patch 20240808, wifi&ipoe&pppoe passwrod encrypt
    rm $PPP_DIR/pap-secrets
    rm $PPP_DIR/pap-secrets-bak
    rm $PPP_DIR/chap-secrets
    rm $PPP_DIR/chap-secrets-bak
    #CHANGHONG add end *********
	exit 0
    fi

    if test -n "$FORCEPING" ; then
	printf "%s" "$FORCEPING"
    else
	$BUSYBOX tty -s && printf "%s" "$PING"
    fi
    sleep $CONNECT_POLL
    TIME=`$BUSYBOX expr $TIME + $CONNECT_POLL`
    if [ $TIME -gt $CONNECT_TIMEOUT ] ; then
	break
    fi
done

$ECHO "setprop net.pppoe.status timeout"     
$SETPROP "net.pppoe.status" "timeout"
$ECHO "TIMED OUT" >& 2

#CHANGHONG add begin *********: from rk patch 20240808, wifi&ipoe&pppoe passwrod encrypt
rm $PPP_DIR/pap-secrets
rm $PPP_DIR/pap-secrets-bak
rm $PPP_DIR/chap-secrets
rm $PPP_DIR/chap-secrets-bak
#CHANGHONG add end *********

#restore ethernet default gateway
#$BUSYBOX route del default
#$BUSYBOX route add default gw DEFAULT_GW
#$ECHO "restore gw"
#cat $DEFAULT_GW|while read line
#    do
#      t1=`echo "$line" | $BUSYBOX awk  '{print $1}'`
#      if [ $t1 = "default" ] ; then
#          t2=`echo "$line" | $BUSYBOX awk  '{print $2}'`
#          t5=`echo "$line" | $BUSYBOX awk  '{print $5}'`
#          $BUSYBOX route add default gw $t2
#      fi
#    done

# Timed out!  Kill the pppoe-connect process and quit
kill $CONNECT_PID > /dev/null 2>&1

# Clean up PIDFILE(s)
$BUSYBOX rm -f "$PIDFILE" "$PPPD_PIDFILE" "$PIDFILE.pppoe" "$PIDFILE.start"

exit 1

