-----<PERSON><PERSON><PERSON> CERTIFICATE-----
MIIFWTCCA0GgAwIBAgIBAjANBgkqhkiG9w0BAQsFADBOMQswCQYDVQQGEwJOTzEd
MBsGA1UECgwUQnV5cGFzcyBBUy05ODMxNjMzMjcxIDAeBgNVBAMMF0J1eXBhc3Mg
Q2xhc3MgMiBSb290IENBMB4XDTEwMTAyNjA4MzgwM1oXDTQwMTAyNjA4MzgwM1ow
TjELMAkGA1UEBhMCTk8xHTAbBgNVBAoMFEJ1eXBhc3MgQVMtOTgzMTYzMzI3MSAw
HgYDVQQDDBdCdXlwYXNzIENsYXNzIDIgUm9vdCBDQTCCAiIwDQYJKoZIhvcNAQEB
BQADggIPADCCAgoCggIBANfHXvfBB9R3+0Mh9PT1aeTuMgHbo4Yf5FkNuud1g1Lr
6hxhFUi7HQfKjK6w3Jad6sNgkoaCKHOcVgb/S2TwDCo3SbXlzwx87vFKu3MwZfPV
L4O2fuPn9Z6rYPnT8Z2SdIrkHJasW4DptfQxh6NR/Md+oW+OU3fUl8FVM5I+GC91
1K2GScuVr1QGbNgGE41b/+EmGVnAJLqBcXmQRFBoJJRfuLMR8SlBYaNByyM21cHx
MlAQTn/0hpPshNOOvEu/XAFOBz3cFIqUCqTqc/sLUegTBxj6DvEr0VQVfTzh97QZ
QmdiXnfgolXsttlpF9U6r0TtSsWe5HonfOV116rLJeffawrbD02TTqigzXsu8lkB
arcNuAeBfos4GzjmCleZPe4h6KP1DBbdi+w0jpwqHAAVF41og9JwnxgIzRFo1clr
Us3ERo/ctfPYV3Me6ZQ5BL/T3jjetFPsaRyifsSP5BtwrfKi+fv3FmRmaZ9JUaLi
FRhnBkp/1Wy1TbMz4GHrXb7pmA8y1x1LPC5aAVKRCfLf6o3YBkBjqhHk/sM3nhRS
P/TizPJhk9H9Z2vXUq6/aKtAQ6BXNVN48FP4YUIHZMbXb5tMOA1jrGKvNouicwoN
9SG9dKpN6nIDSdvHXx1iY8f93ZHsM+71bbRuMGjeyNYmsHVee7QHIJihdjK4TWxP
AgMBAAGjQjBAMA8GA1UdEwEB/wQFMAMBAf8wHQYDVR0OBBYEFMmAd+BikoL1Rpzz
uvdMw964o605MA4GA1UdDwEB/wQEAwIBBjANBgkqhkiG9w0BAQsFAAOCAgEAU18h
9bqwOlI5LJKwbADJ784g7wbylp7ppHR/ehb8t/W2+xUbP6umwHJdELFx7rxP462s
A20ucS6vxOOto70MEae0/0qyexAQH6dXQbLArvQsWdZHEIjzIVEpMMpghq9Gqx3t
OluwlN5E40EIosHsHdb9T7bWR9AUC8rmyrV7d35BH16Dx7aMOZawP5aBQW9gkOLo
+fsicdl9sz1Gv7SEr5AcD48Saq/v7h56rgJKihcrdv6sVIkkLE8/trKnToyokZf7
KcZ7XC25y2a2t6hbElGFtQl+Ynhw/qlqYLYdDnkM/crqJIByw5c/8nerQyIKx+u2
DISCLIBrQYoIwOula9+ZEsuK1V6ADJHgJgg2SMX6OBE1/yWDLfJ6v9r9jv6ly0Us
H8SIU653DtmadsWOLB2jutXsMq7Aqqz30XpN69QH4kj3Io6wpJ9qzo6ysmD0oyLQ
I+uUWnpp3Q+/QFesa1lQ2aOZ4W7+jQF5JyMV3pKdewlNWudLSDBaGOYKbeaP4NK7
5t98biGCwWg5TbSYWGZizEqQXsP6JwSxeRV0mcy+rSDeJmAc61ZRpqPq5KM/p/9h
3PFaTWwyI0PurKju7koSCTxdccK+efrCh2gdC/1cacwG0Jp9VJkqyTkaGa9LKkPz
Y11aWOIv4x3kqdbQCtCev9eBCfHJxyYNrJgWVqA=
-----END CERTIFICATE-----
Certificate:
    Data:
        Version: 3 (0x2)
        Serial Number: 2 (0x2)
    Signature Algorithm: sha256WithRSAEncryption
        Issuer: C=NO, O=Buypass AS-983163327, CN=Buypass Class 2 Root CA
        Validity
            Not Before: Oct 26 08:38:03 2010 GMT
            Not After : Oct 26 08:38:03 2040 GMT
        Subject: C=NO, O=Buypass AS-983163327, CN=Buypass Class 2 Root CA
        Subject Public Key Info:
            Public Key Algorithm: rsaEncryption
                Public-Key: (4096 bit)
                Modulus:
                    00:d7:c7:5e:f7:c1:07:d4:77:fb:43:21:f4:f4:f5:
                    69:e4:ee:32:01:db:a3:86:1f:e4:59:0d:ba:e7:75:
                    83:52:eb:ea:1c:61:15:48:bb:1d:07:ca:8c:ae:b0:
                    dc:96:9d:ea:c3:60:92:86:82:28:73:9c:56:06:ff:
                    4b:64:f0:0c:2a:37:49:b5:e5:cf:0c:7c:ee:f1:4a:
                    bb:73:30:65:f3:d5:2f:83:b6:7e:e3:e7:f5:9e:ab:
                    60:f9:d3:f1:9d:92:74:8a:e4:1c:96:ac:5b:80:e9:
                    b5:f4:31:87:a3:51:fc:c7:7e:a1:6f:8e:53:77:d4:
                    97:c1:55:33:92:3e:18:2f:75:d4:ad:86:49:cb:95:
                    af:54:06:6c:d8:06:13:8d:5b:ff:e1:26:19:59:c0:
                    24:ba:81:71:79:90:44:50:68:24:94:5f:b8:b3:11:
                    f1:29:41:61:a3:41:cb:23:36:d5:c1:f1:32:50:10:
                    4e:7f:f4:86:93:ec:84:d3:8e:bc:4b:bf:5c:01:4e:
                    07:3d:dc:14:8a:94:0a:a4:ea:73:fb:0b:51:e8:13:
                    07:18:fa:0e:f1:2b:d1:54:15:7d:3c:e1:f7:b4:19:
                    42:67:62:5e:77:e0:a2:55:ec:b6:d9:69:17:d5:3a:
                    af:44:ed:4a:c5:9e:e4:7a:27:7c:e5:75:d7:aa:cb:
                    25:e7:df:6b:0a:db:0f:4d:93:4e:a8:a0:cd:7b:2e:
                    f2:59:01:6a:b7:0d:b8:07:81:7e:8b:38:1b:38:e6:
                    0a:57:99:3d:ee:21:e8:a3:f5:0c:16:dd:8b:ec:34:
                    8e:9c:2a:1c:00:15:17:8d:68:83:d2:70:9f:18:08:
                    cd:11:68:d5:c9:6b:52:cd:c4:46:8f:dc:b5:f3:d8:
                    57:73:1e:e9:94:39:04:bf:d3:de:38:de:b4:53:ec:
                    69:1c:a2:7e:c4:8f:e4:1b:70:ad:f2:a2:f9:fb:f7:
                    16:64:66:69:9f:49:51:a2:e2:15:18:67:06:4a:7f:
                    d5:6c:b5:4d:b3:33:e0:61:eb:5d:be:e9:98:0f:32:
                    d7:1d:4b:3c:2e:5a:01:52:91:09:f2:df:ea:8d:d8:
                    06:40:63:aa:11:e4:fe:c3:37:9e:14:52:3f:f4:e2:
                    cc:f2:61:93:d1:fd:67:6b:d7:52:ae:bf:68:ab:40:
                    43:a0:57:35:53:78:f0:53:f8:61:42:07:64:c6:d7:
                    6f:9b:4c:38:0d:63:ac:62:af:36:8b:a2:73:0a:0d:
                    f5:21:bd:74:aa:4d:ea:72:03:49:db:c7:5f:1d:62:
                    63:c7:fd:dd:91:ec:33:ee:f5:6d:b4:6e:30:68:de:
                    c8:d6:26:b0:75:5e:7b:b4:07:20:98:a1:76:32:b8:
                    4d:6c:4f
                Exponent: 65537 (0x10001)
        X509v3 extensions:
            X509v3 Basic Constraints: critical
                CA:TRUE
            X509v3 Subject Key Identifier: 
                C9:80:77:E0:62:92:82:F5:46:9C:F3:BA:F7:4C:C3:DE:B8:A3:AD:39
            X509v3 Key Usage: critical
                Certificate Sign, CRL Sign
    Signature Algorithm: sha256WithRSAEncryption
         53:5f:21:f5:ba:b0:3a:52:39:2c:92:b0:6c:00:c9:ef:ce:20:
         ef:06:f2:96:9e:e9:a4:74:7f:7a:16:fc:b7:f5:b6:fb:15:1b:
         3f:ab:a6:c0:72:5d:10:b1:71:ee:bc:4f:e3:ad:ac:03:6d:2e:
         71:2e:af:c4:e3:ad:a3:bd:0c:11:a7:b4:ff:4a:b2:7b:10:10:
         1f:a7:57:41:b2:c0:ae:f4:2c:59:d6:47:10:88:f3:21:51:29:
         30:ca:60:86:af:46:ab:1d:ed:3a:5b:b0:94:de:44:e3:41:08:
         a2:c1:ec:1d:d6:fd:4f:b6:d6:47:d0:14:0b:ca:e6:ca:b5:7b:
         77:7e:41:1f:5e:83:c7:b6:8c:39:96:b0:3f:96:81:41:6f:60:
         90:e2:e8:f9:fb:22:71:d9:7d:b3:3d:46:bf:b4:84:af:90:1c:
         0f:8f:12:6a:af:ef:ee:1e:7a:ae:02:4a:8a:17:2b:76:fe:ac:
         54:89:24:2c:4f:3f:b6:b2:a7:4e:8c:a8:91:97:fb:29:c6:7b:
         5c:2d:b9:cb:66:b6:b7:a8:5b:12:51:85:b5:09:7e:62:78:70:
         fe:a9:6a:60:b6:1d:0e:79:0c:fd:ca:ea:24:80:72:c3:97:3f:
         f2:77:ab:43:22:0a:c7:eb:b6:0c:84:82:2c:80:6b:41:8a:08:
         c0:eb:a5:6b:df:99:12:cb:8a:d5:5e:80:0c:91:e0:26:08:36:
         48:c5:fa:38:11:35:ff:25:83:2d:f2:7a:bf:da:fd:8e:fe:a5:
         cb:45:2c:1f:c4:88:53:ae:77:0e:d9:9a:76:c5:8e:2c:1d:a3:
         ba:d5:ec:32:ae:c0:aa:ac:f7:d1:7a:4d:eb:d4:07:e2:48:f7:
         22:8e:b0:a4:9f:6a:ce:8e:b2:b2:60:f4:a3:22:d0:23:eb:94:
         5a:7a:69:dd:0f:bf:40:57:ac:6b:59:50:d9:a3:99:e1:6e:fe:
         8d:01:79:27:23:15:de:92:9d:7b:09:4d:5a:e7:4b:48:30:5a:
         18:e6:0a:6d:e6:8f:e0:d2:bb:e6:df:7c:6e:21:82:c1:68:39:
         4d:b4:98:58:66:62:cc:4a:90:5e:c3:fa:27:04:b1:79:15:74:
         99:cc:be:ad:20:de:26:60:1c:eb:56:51:a6:a3:ea:e4:a3:3f:
         a7:ff:61:dc:f1:5a:4d:6c:32:23:43:ee:ac:a8:ee:ee:4a:12:
         09:3c:5d:71:c2:be:79:fa:c2:87:68:1d:0b:fd:5c:69:cc:06:
         d0:9a:7d:54:99:2a:c9:39:1a:19:af:4b:2a:43:f3:63:5d:5a:
         58:e2:2f:e3:1d:e4:a9:d6:d0:0a:d0:9e:bf:d7:81:09:f1:c9:
         c7:26:0d:ac:98:16:56:a0
SHA1 Fingerprint=49:0A:75:74:DE:87:0A:47:FE:58:EE:F6:C7:6B:EB:C6:0B:12:40:99
