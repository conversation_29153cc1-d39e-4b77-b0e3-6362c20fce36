% This file was renamed from hyph-en-gb.tex to hyph-en-gb.tex(unicode-licence) in December
% 2015 following a request from 肖湘晔 (<PERSON><PERSON><PERSON>) to modify the licence in order to allow it
% to be included in the Unicode data files archive.  Following discussions between 肖湘晔,
% <PERSON><PERSON><PERSON> and myself (<PERSON>), it has been agreed that the version uploaded
% to Unicode will have a unique name and carry the Unicode licence.
%
% This file was previously renamed from ukhyphen.tex to hyph-en-gb.tex in June 2008
% for consistency with other files with hyphenation patterns in hyph-utf8 package.
% No other changes made. See http://www.tug.org/tex-hyphen for more details.
%
% Original-file: ukhyphen.tex
% TeX hyphenation patterns for UK English
%
% This file is licensed under the UNICODE, INC. LICENCE AGREEMENT - DATA FILES AND SOFTWARE <http://unicode.org/copyright.html#Exhibit1>
%
% Copyright (c) 1996 Dominik Wu<PERSON>tyk.
% Distributed under the Terms of Use in 
% http://www.unicode.org/copyright.html.
% 
% Permission is hereby granted, free of charge, to any person obtaining
% a copy of the Unicode data files and any associated documentation
% (the "Data Files") or Unicode software and any associated documentation
% (the "Software") to deal in the Data Files or Software
% without restriction, including without limitation the rights to use,
% copy, modify, merge, publish, distribute, and/or sell copies of
% the Data Files or Software, and to permit persons to whom the Data Files
% or Software are furnished to do so, provided that
% (a) this copyright and permission notice appear with all copies 
% of the Data Files or Software,
% (b) this copyright and permission notice appear in associated 
% documentation, and
% (c) there is clear notice in each modified Data File or in the Software
% as well as in the documentation associated with the Data File(s) or
% Software that the data or software has been modified.
% 
% THE DATA FILES AND SOFTWARE ARE PROVIDED "AS IS", WITHOUT WARRANTY OF
% ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
% WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
% NONINFRINGEMENT OF THIRD PARTY RIGHTS.
% IN NO EVENT SHALL THE COPYRIGHT HOLDER OR HOLDERS INCLUDED IN THIS
% NOTICE BE LIABLE FOR ANY CLAIM, OR ANY SPECIAL INDIRECT OR CONSEQUENTIAL
% DAMAGES, OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE,
% DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER
% TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
% PERFORMANCE OF THE DATA FILES OR SOFTWARE.
% 
% Except as contained in this notice, the name of a copyright holder
% shall not be used in advertising or otherwise to promote the sale,
% use or other dealings in these Data Files or Software without prior
% written authorization of the copyright holder.
%
%       $Log: ukhyph.tex $
%       Revision 2.0  1996/09/10 15:04:04  ucgadkw
%       o  added list of hyphenation exceptions at the end of this file.
%
%
% Version 1.0a.  Released 18th October 2005/PT.
%
% Created by Dominik Wujastyk and Graham Toal using Frank Liang's PATGEN 1.0.
% Like the US patterns, these UK patterns correctly hyphenate about 90% of
% the words in the input list, and produce no hyphens not in the list
% (see TeXbook pp. 451--2).
%
% These patterns are based on a file of 114925 British-hyphenated words
% generously made available to Dominik Wujastyk by Oxford University Press.
% This list of words is copyright to the OUP and may not be redistributed.
% The hyphenation break points in the words in the abovementioned file is
% also copyright to the OUP.
%
% We are very grateful to Oxford University Press for allowing us to use
% their list of hyphenated words to produce the following TeX hyphenation
% patterns.  This file of hyphenation patterns may be freely distributed.
%
% These patterns require a value of about 14000 for TeX's pattern memory size.
%
