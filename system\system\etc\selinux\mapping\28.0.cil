(typeattributeset mediadrmserver_service_28_0 (mediadrmserver_service))
(expandtypeattribute (mediadrmserver_service_28_0) true)
(typeattribute mediadrmserver_service_28_0)
(typeattributeset recovery_refresh_28_0 (recovery_refresh))
(expandtypeattribute (recovery_refresh_28_0) true)
(typeattribute recovery_refresh_28_0)
(typeattributeset slice_service_28_0 (slice_service))
(expandtypeattribute (slice_service_28_0) true)
(typeattribute slice_service_28_0)
(typeattributeset pdx_display_dir_28_0 (pdx_display_dir))
(expandtypeattribute (pdx_display_dir_28_0) true)
(typeattribute pdx_display_dir_28_0)
(typeattributeset processinfo_service_28_0 (processinfo_service))
(expandtypeattribute (processinfo_service_28_0) true)
(typeattribute processinfo_service_28_0)
(typeattributeset sysfs_ipv4_28_0 (sysfs_ipv4))
(expandtypeattribute (sysfs_ipv4_28_0) true)
(typeattribute sysfs_ipv4_28_0)
(typeattributeset vdc_28_0 (vdc))
(expandtypeattribute (vdc_28_0) true)
(typeattribute vdc_28_0)
(typeattributeset mediadrmserver_exec_28_0 (mediadrmserver_exec))
(expandtypeattribute (mediadrmserver_exec_28_0) true)
(typeattribute mediadrmserver_exec_28_0)
(typeattributeset serial_service_28_0 (serial_service))
(expandtypeattribute (serial_service_28_0) true)
(typeattribute serial_service_28_0)
(typeattributeset vendor_shell_28_0 (vendor_shell))
(expandtypeattribute (vendor_shell_28_0) true)
(typeattribute vendor_shell_28_0)
(typeattributeset system_ndebug_socket_28_0 (system_ndebug_socket))
(expandtypeattribute (system_ndebug_socket_28_0) true)
(typeattribute system_ndebug_socket_28_0)
(typeattributeset fingerprint_service_28_0 (fingerprint_service))
(expandtypeattribute (fingerprint_service_28_0) true)
(typeattribute fingerprint_service_28_0)
(typeattributeset hal_fingerprint_service_28_0 (hal_fingerprint_service))
(expandtypeattribute (hal_fingerprint_service_28_0) true)
(typeattribute hal_fingerprint_service_28_0)
(typeattributeset recovery_block_device_28_0 (recovery_block_device))
(expandtypeattribute (recovery_block_device_28_0) true)
(typeattribute recovery_block_device_28_0)
(typeattributeset drmserver_28_0 (drmserver))
(expandtypeattribute (drmserver_28_0) true)
(typeattribute drmserver_28_0)
(typeattributeset vr_hwc_service_28_0 (vr_hwc_service))
(expandtypeattribute (vr_hwc_service_28_0) true)
(typeattribute vr_hwc_service_28_0)
(typeattributeset secure_element_device_28_0 (secure_element_device))
(expandtypeattribute (secure_element_device_28_0) true)
(typeattribute secure_element_device_28_0)
(typeattributeset hal_sensors_hwservice_28_0 (hal_sensors_hwservice))
(expandtypeattribute (hal_sensors_hwservice_28_0) true)
(typeattribute hal_sensors_hwservice_28_0)
(typeattributeset audiohal_data_file_28_0 (audiohal_data_file))
(expandtypeattribute (audiohal_data_file_28_0) true)
(typeattribute audiohal_data_file_28_0)
(typeattributeset wifi_service_28_0 (wifi_service))
(expandtypeattribute (wifi_service_28_0) true)
(typeattribute wifi_service_28_0)
(typeattributeset proc_qtaguid_stat_28_0 (proc_qtaguid_stat))
(expandtypeattribute (proc_qtaguid_stat_28_0) true)
(typeattribute proc_qtaguid_stat_28_0)
(typeattributeset exfat_28_0 (exfat))
(expandtypeattribute (exfat_28_0) true)
(typeattribute exfat_28_0)
(typeattributeset system_prop_28_0 (system_prop))
(expandtypeattribute (system_prop_28_0) true)
(typeattribute system_prop_28_0)
(typeattributeset vfat_28_0 (vfat))
(expandtypeattribute (vfat_28_0) true)
(typeattribute vfat_28_0)
(typeattributeset pdx_bufferhub_client_endpoint_socket_28_0 (pdx_bufferhub_client_endpoint_socket))
(expandtypeattribute (pdx_bufferhub_client_endpoint_socket_28_0) true)
(typeattribute pdx_bufferhub_client_endpoint_socket_28_0)
(typeattributeset property_info_28_0 (property_info))
(expandtypeattribute (property_info_28_0) true)
(typeattribute property_info_28_0)
(typeattributeset keystore_exec_28_0 (keystore_exec))
(expandtypeattribute (keystore_exec_28_0) true)
(typeattribute keystore_exec_28_0)
(typeattributeset netpolicy_service_28_0 (netpolicy_service))
(expandtypeattribute (netpolicy_service_28_0) true)
(typeattribute netpolicy_service_28_0)
(typeattributeset video_device_28_0 (video_device))
(expandtypeattribute (video_device_28_0) true)
(typeattribute video_device_28_0)
(typeattributeset serialno_prop_28_0 (serialno_prop))
(expandtypeattribute (serialno_prop_28_0) true)
(typeattribute serialno_prop_28_0)
(typeattributeset midi_service_28_0 (midi_service))
(expandtypeattribute (midi_service_28_0) true)
(typeattribute midi_service_28_0)
(typeattributeset persistent_properties_ready_prop_28_0 (persistent_properties_ready_prop))
(expandtypeattribute (persistent_properties_ready_prop_28_0) true)
(typeattribute persistent_properties_ready_prop_28_0)
(typeattributeset file_contexts_file_28_0 (file_contexts_file))
(expandtypeattribute (file_contexts_file_28_0) true)
(typeattribute file_contexts_file_28_0)
(typeattributeset tzdatacheck_28_0 (tzdatacheck))
(expandtypeattribute (tzdatacheck_28_0) true)
(typeattribute tzdatacheck_28_0)
(typeattributeset proc_diskstats_28_0 (proc_diskstats))
(expandtypeattribute (proc_diskstats_28_0) true)
(typeattribute proc_diskstats_28_0)
(typeattributeset property_contexts_file_28_0 (property_contexts_file))
(expandtypeattribute (property_contexts_file_28_0) true)
(typeattribute property_contexts_file_28_0)
(typeattributeset firstboot_prop_28_0 (firstboot_prop))
(expandtypeattribute (firstboot_prop_28_0) true)
(typeattribute firstboot_prop_28_0)
(typeattributeset pan_result_prop_28_0 (pan_result_prop))
(expandtypeattribute (pan_result_prop_28_0) true)
(typeattribute pan_result_prop_28_0)
(typeattributeset ctl_rildaemon_prop_28_0 (ctl_rildaemon_prop))
(expandtypeattribute (ctl_rildaemon_prop_28_0) true)
(typeattribute ctl_rildaemon_prop_28_0)
(typeattributeset bluetooth_logs_data_file_28_0 (bluetooth_logs_data_file))
(expandtypeattribute (bluetooth_logs_data_file_28_0) true)
(typeattribute bluetooth_logs_data_file_28_0)
(typeattributeset hal_light_hwservice_28_0 (hal_light_hwservice))
(expandtypeattribute (hal_light_hwservice_28_0) true)
(typeattribute hal_light_hwservice_28_0)
(typeattributeset print_service_28_0 (print_service))
(expandtypeattribute (print_service_28_0) true)
(typeattribute print_service_28_0)
(typeattributeset proc_hung_task_28_0 (proc_hung_task))
(expandtypeattribute (proc_hung_task_28_0) true)
(typeattribute proc_hung_task_28_0)
(typeattributeset same_process_hal_file_28_0 (same_process_hal_file))
(expandtypeattribute (same_process_hal_file_28_0) true)
(typeattribute same_process_hal_file_28_0)
(typeattributeset hal_vibrator_hwservice_28_0 (hal_vibrator_hwservice))
(expandtypeattribute (hal_vibrator_hwservice_28_0) true)
(typeattribute hal_vibrator_hwservice_28_0)
(typeattributeset location_service_28_0 (location_service))
(expandtypeattribute (location_service_28_0) true)
(typeattribute location_service_28_0)
(typeattributeset mediaprovider_28_0 (mediaprovider))
(expandtypeattribute (mediaprovider_28_0) true)
(typeattribute mediaprovider_28_0)
(typeattributeset hal_confirmationui_hwservice_28_0 (hal_confirmationui_hwservice))
(expandtypeattribute (hal_confirmationui_hwservice_28_0) true)
(typeattribute hal_confirmationui_hwservice_28_0)
(typeattributeset device_policy_service_28_0 (device_policy_service))
(expandtypeattribute (device_policy_service_28_0) true)
(typeattribute device_policy_service_28_0)
(typeattributeset preopt2cachename_28_0 (preopt2cachename))
(expandtypeattribute (preopt2cachename_28_0) true)
(typeattribute preopt2cachename_28_0)
(typeattributeset autofill_service_28_0 (autofill_service))
(expandtypeattribute (autofill_service_28_0) true)
(typeattribute autofill_service_28_0)
(typeattributeset exported_default_prop_28_0 (exported_default_prop))
(expandtypeattribute (exported_default_prop_28_0) true)
(typeattribute exported_default_prop_28_0)
(typeattributeset proc_swaps_28_0 (proc_swaps))
(expandtypeattribute (proc_swaps_28_0) true)
(typeattribute proc_swaps_28_0)
(typeattributeset debugfs_28_0 (debugfs))
(expandtypeattribute (debugfs_28_0) true)
(typeattribute debugfs_28_0)
(typeattributeset configfs_28_0 (configfs))
(expandtypeattribute (configfs_28_0) true)
(typeattribute configfs_28_0)
(typeattributeset config_prop_28_0 (config_prop))
(expandtypeattribute (config_prop_28_0) true)
(typeattribute config_prop_28_0)
(typeattributeset charger_28_0 (charger))
(expandtypeattribute (charger_28_0) true)
(typeattribute charger_28_0)
(typeattributeset system_block_device_28_0 (system_block_device))
(expandtypeattribute (system_block_device_28_0) true)
(typeattribute system_block_device_28_0)
(typeattributeset postinstall_28_0 (postinstall))
(expandtypeattribute (postinstall_28_0) true)
(typeattribute postinstall_28_0)
(typeattributeset updatelock_service_28_0 (updatelock_service))
(expandtypeattribute (updatelock_service_28_0) true)
(typeattribute updatelock_service_28_0)
(typeattributeset apk_data_file_28_0 (apk_data_file))
(expandtypeattribute (apk_data_file_28_0) true)
(typeattribute apk_data_file_28_0)
(typeattributeset hidl_base_hwservice_28_0 (hidl_base_hwservice))
(expandtypeattribute (hidl_base_hwservice_28_0) true)
(typeattribute hidl_base_hwservice_28_0)
(typeattributeset system_app_28_0 (system_app))
(expandtypeattribute (system_app_28_0) true)
(typeattribute system_app_28_0)
(typeattributeset hidl_manager_hwservice_28_0 (hidl_manager_hwservice))
(expandtypeattribute (hidl_manager_hwservice_28_0) true)
(typeattribute hidl_manager_hwservice_28_0)
(typeattributeset hidl_memory_hwservice_28_0 (hidl_memory_hwservice))
(expandtypeattribute (hidl_memory_hwservice_28_0) true)
(typeattribute hidl_memory_hwservice_28_0)
(typeattributeset preloads_media_file_28_0 (preloads_media_file))
(expandtypeattribute (preloads_media_file_28_0) true)
(typeattribute preloads_media_file_28_0)
(typeattributeset surfaceflinger_28_0 (surfaceflinger))
(expandtypeattribute (surfaceflinger_28_0) true)
(typeattribute surfaceflinger_28_0)
(typeattributeset pdx_display_screenshot_channel_socket_28_0 (pdx_display_screenshot_channel_socket))
(expandtypeattribute (pdx_display_screenshot_channel_socket_28_0) true)
(typeattribute pdx_display_screenshot_channel_socket_28_0)
(typeattributeset debugfs_tracing_instances_28_0 (debugfs_tracing_instances))
(expandtypeattribute (debugfs_tracing_instances_28_0) true)
(typeattribute debugfs_tracing_instances_28_0)
(typeattributeset hal_contexthub_hwservice_28_0 (hal_contexthub_hwservice))
(expandtypeattribute (hal_contexthub_hwservice_28_0) true)
(typeattribute hal_contexthub_hwservice_28_0)
(typeattributeset proc_zoneinfo_28_0 (proc_zoneinfo))
(expandtypeattribute (proc_zoneinfo_28_0) true)
(typeattribute proc_zoneinfo_28_0)
(typeattributeset proc_tty_drivers_28_0 (proc_tty_drivers))
(expandtypeattribute (proc_tty_drivers_28_0) true)
(typeattribute proc_tty_drivers_28_0)
(typeattributeset sec_key_att_app_id_provider_service_28_0 (sec_key_att_app_id_provider_service))
(expandtypeattribute (sec_key_att_app_id_provider_service_28_0) true)
(typeattribute sec_key_att_app_id_provider_service_28_0)
(typeattributeset activity_service_28_0 (activity_service))
(expandtypeattribute (activity_service_28_0) true)
(typeattribute activity_service_28_0)
(typeattributeset mediaextractor_exec_28_0 (mediaextractor_exec))
(expandtypeattribute (mediaextractor_exec_28_0) true)
(typeattribute mediaextractor_exec_28_0)
(typeattributeset tombstoned_java_trace_socket_28_0 (tombstoned_java_trace_socket))
(expandtypeattribute (tombstoned_java_trace_socket_28_0) true)
(typeattribute tombstoned_java_trace_socket_28_0)
(typeattributeset watchdog_device_28_0 (watchdog_device))
(expandtypeattribute (watchdog_device_28_0) true)
(typeattribute watchdog_device_28_0)
(typeattributeset graphics_device_28_0 (graphics_device))
(expandtypeattribute (graphics_device_28_0) true)
(typeattribute graphics_device_28_0)
(typeattributeset method_trace_data_file_28_0 (method_trace_data_file))
(expandtypeattribute (method_trace_data_file_28_0) true)
(typeattribute method_trace_data_file_28_0)
(typeattributeset vndservicemanager_28_0 (vndservicemanager))
(expandtypeattribute (vndservicemanager_28_0) true)
(typeattribute vndservicemanager_28_0)
(typeattributeset tombstone_wifi_data_file_28_0 (tombstone_wifi_data_file))
(expandtypeattribute (tombstone_wifi_data_file_28_0) true)
(typeattribute tombstone_wifi_data_file_28_0)
(typeattributeset apk_private_data_file_28_0 (apk_private_data_file))
(expandtypeattribute (apk_private_data_file_28_0) true)
(typeattribute apk_private_data_file_28_0)
(typeattributeset vold_prepare_subdirs_28_0 (vold_prepare_subdirs))
(expandtypeattribute (vold_prepare_subdirs_28_0) true)
(typeattribute vold_prepare_subdirs_28_0)
(typeattributeset hwservicemanager_28_0 (hwservicemanager))
(expandtypeattribute (hwservicemanager_28_0) true)
(typeattribute hwservicemanager_28_0)
(typeattributeset keychord_device_28_0 (keychord_device))
(expandtypeattribute (keychord_device_28_0) true)
(typeattribute keychord_device_28_0)
(typeattributeset secure_element_28_0 (secure_element))
(expandtypeattribute (secure_element_28_0) true)
(typeattribute secure_element_28_0)
(typeattributeset hal_graphics_composer_hwservice_28_0 (hal_graphics_composer_hwservice))
(expandtypeattribute (hal_graphics_composer_hwservice_28_0) true)
(typeattribute hal_graphics_composer_hwservice_28_0)
(typeattributeset servicemanager_28_0 (servicemanager))
(expandtypeattribute (servicemanager_28_0) true)
(typeattribute servicemanager_28_0)
(typeattributeset ashmem_device_28_0 (ashmem_device))
(expandtypeattribute (ashmem_device_28_0) true)
(typeattribute ashmem_device_28_0)
(typeattributeset virtual_touchpad_28_0 (virtual_touchpad))
(expandtypeattribute (virtual_touchpad_28_0) true)
(typeattribute virtual_touchpad_28_0)
(typeattributeset hal_telephony_hwservice_28_0 (hal_telephony_hwservice))
(expandtypeattribute (hal_telephony_hwservice_28_0) true)
(typeattribute hal_telephony_hwservice_28_0)
(typeattributeset fingerprint_prop_28_0 (fingerprint_prop))
(expandtypeattribute (fingerprint_prop_28_0) true)
(typeattribute fingerprint_prop_28_0)
(typeattributeset exported2_vold_prop_28_0 (exported2_vold_prop))
(expandtypeattribute (exported2_vold_prop_28_0) true)
(typeattribute exported2_vold_prop_28_0)
(typeattributeset proc_stat_28_0 (proc_stat))
(expandtypeattribute (proc_stat_28_0) true)
(typeattribute proc_stat_28_0)
(typeattributeset rootfs_28_0 (rootfs))
(expandtypeattribute (rootfs_28_0) true)
(typeattribute rootfs_28_0)
(typeattributeset sdcardfs_28_0 (sdcardfs))
(expandtypeattribute (sdcardfs_28_0) true)
(typeattribute sdcardfs_28_0)
(typeattributeset exported_overlay_prop_28_0 (exported_overlay_prop))
(expandtypeattribute (exported_overlay_prop_28_0) true)
(typeattribute exported_overlay_prop_28_0)
(typeattributeset netd_28_0 (netd))
(expandtypeattribute (netd_28_0) true)
(typeattribute netd_28_0)
(typeattributeset sysfs_usb_28_0 (sysfs_usb))
(expandtypeattribute (sysfs_usb_28_0) true)
(typeattribute sysfs_usb_28_0)
(typeattributeset proc_perf_28_0 (proc_perf))
(expandtypeattribute (proc_perf_28_0) true)
(typeattribute proc_perf_28_0)
(typeattributeset mqueue_28_0 (mqueue))
(expandtypeattribute (mqueue_28_0) true)
(typeattribute mqueue_28_0)
(typeattributeset tee_28_0 (tee))
(expandtypeattribute (tee_28_0) true)
(typeattribute tee_28_0)
(typeattributeset media_session_service_28_0 (media_session_service))
(expandtypeattribute (media_session_service_28_0) true)
(typeattribute media_session_service_28_0)
(typeattributeset adbd_28_0 (adbd))
(expandtypeattribute (adbd_28_0) true)
(typeattribute adbd_28_0)
(typeattributeset proc_dirty_28_0 (proc_dirty))
(expandtypeattribute (proc_dirty_28_0) true)
(typeattribute proc_dirty_28_0)
(typeattributeset sysfs_fs_ext4_features_28_0 (sysfs_fs_ext4_features))
(expandtypeattribute (sysfs_fs_ext4_features_28_0) true)
(typeattribute sysfs_fs_ext4_features_28_0)
(typeattributeset textclassifier_data_file_28_0 (textclassifier_data_file))
(expandtypeattribute (textclassifier_data_file_28_0) true)
(typeattribute textclassifier_data_file_28_0)
(typeattributeset untrusted_v2_app_28_0 (untrusted_v2_app))
(expandtypeattribute (untrusted_v2_app_28_0) true)
(typeattribute untrusted_v2_app_28_0)
(typeattributeset fuse_28_0 (fuse))
(expandtypeattribute (fuse_28_0) true)
(typeattribute fuse_28_0)
(typeattributeset recovery_28_0 (recovery))
(expandtypeattribute (recovery_28_0) true)
(typeattribute recovery_28_0)
(typeattributeset bootstat_28_0 (bootstat))
(expandtypeattribute (bootstat_28_0) true)
(typeattribute bootstat_28_0)
(typeattributeset labeledfs_28_0 (labeledfs))
(expandtypeattribute (labeledfs_28_0) true)
(typeattribute labeledfs_28_0)
(typeattributeset exported_fingerprint_prop_28_0 (exported_fingerprint_prop))
(expandtypeattribute (exported_fingerprint_prop_28_0) true)
(typeattribute exported_fingerprint_prop_28_0)
(typeattributeset proc_loadavg_28_0 (proc_loadavg))
(expandtypeattribute (proc_loadavg_28_0) true)
(typeattribute proc_loadavg_28_0)
(typeattributeset pdx_bufferhub_dir_28_0 (pdx_bufferhub_dir))
(expandtypeattribute (pdx_bufferhub_dir_28_0) true)
(typeattribute pdx_bufferhub_dir_28_0)
(typeattributeset usermodehelper_28_0 (usermodehelper))
(expandtypeattribute (usermodehelper_28_0) true)
(typeattribute usermodehelper_28_0)
(typeattributeset mediaextractor_service_28_0 (mediaextractor_service))
(expandtypeattribute (mediaextractor_service_28_0) true)
(typeattribute mediaextractor_service_28_0)
(typeattributeset debug_prop_28_0 (debug_prop))
(expandtypeattribute (debug_prop_28_0) true)
(typeattribute debug_prop_28_0)
(typeattributeset hal_omx_hwservice_28_0 (hal_omx_hwservice))
(expandtypeattribute (hal_omx_hwservice_28_0) true)
(typeattribute hal_omx_hwservice_28_0)
(typeattributeset update_engine_service_28_0 (update_engine_service))
(expandtypeattribute (update_engine_service_28_0) true)
(typeattribute update_engine_service_28_0)
(typeattributeset lock_settings_service_28_0 (lock_settings_service))
(expandtypeattribute (lock_settings_service_28_0) true)
(typeattribute lock_settings_service_28_0)
(typeattributeset ctl_interface_restart_prop_28_0 (ctl_interface_restart_prop))
(expandtypeattribute (ctl_interface_restart_prop_28_0) true)
(typeattribute ctl_interface_restart_prop_28_0)
(typeattributeset cameraproxy_service_28_0 (cameraproxy_service))
(expandtypeattribute (cameraproxy_service_28_0) true)
(typeattribute cameraproxy_service_28_0)
(typeattributeset audio_prop_28_0 (audio_prop))
(expandtypeattribute (audio_prop_28_0) true)
(typeattribute audio_prop_28_0)
(typeattributeset healthd_28_0 (healthd))
(expandtypeattribute (healthd_28_0) true)
(typeattribute healthd_28_0)
(typeattributeset keychain_data_file_28_0 (keychain_data_file))
(expandtypeattribute (keychain_data_file_28_0) true)
(typeattribute keychain_data_file_28_0)
(typeattributeset kmem_device_28_0 (kmem_device))
(expandtypeattribute (kmem_device_28_0) true)
(typeattribute kmem_device_28_0)
(typeattributeset kmsg_device_28_0 (kmsg_device))
(expandtypeattribute (kmsg_device_28_0) true)
(typeattribute kmsg_device_28_0)
(typeattributeset netif_28_0 (netif))
(expandtypeattribute (netif_28_0) true)
(typeattribute netif_28_0)
(typeattributeset webview_zygote_exec_28_0 (webview_zygote_exec))
(expandtypeattribute (webview_zygote_exec_28_0) true)
(typeattribute webview_zygote_exec_28_0)
(typeattributeset hal_tetheroffload_hwservice_28_0 (hal_tetheroffload_hwservice))
(expandtypeattribute (hal_tetheroffload_hwservice_28_0) true)
(typeattribute hal_tetheroffload_hwservice_28_0)
(typeattributeset zoneinfo_data_file_28_0 (zoneinfo_data_file))
(expandtypeattribute (zoneinfo_data_file_28_0) true)
(typeattribute zoneinfo_data_file_28_0)
(typeattributeset radio_28_0 (radio))
(expandtypeattribute (radio_28_0) true)
(typeattribute radio_28_0)
(typeattributeset pipefs_28_0 (pipefs))
(expandtypeattribute (pipefs_28_0) true)
(typeattribute pipefs_28_0)
(typeattributeset pstorefs_28_0 (pstorefs))
(expandtypeattribute (pstorefs_28_0) true)
(typeattribute pstorefs_28_0)
(typeattributeset proc_sysrq_28_0 (proc_sysrq))
(expandtypeattribute (proc_sysrq_28_0) true)
(typeattribute proc_sysrq_28_0)
(typeattributeset gatekeeperd_28_0 (gatekeeperd))
(expandtypeattribute (gatekeeperd_28_0) true)
(typeattribute gatekeeperd_28_0)
(typeattributeset consumer_ir_service_28_0 (consumer_ir_service))
(expandtypeattribute (consumer_ir_service_28_0) true)
(typeattribute consumer_ir_service_28_0)
(typeattributeset exported_wifi_prop_28_0 (exported_wifi_prop))
(expandtypeattribute (exported_wifi_prop_28_0) true)
(typeattribute exported_wifi_prop_28_0)
(typeattributeset exported_vold_prop_28_0 (exported_vold_prop))
(expandtypeattribute (exported_vold_prop_28_0) true)
(typeattribute exported_vold_prop_28_0)
(typeattributeset sysfs_zram_28_0 (sysfs_zram))
(expandtypeattribute (sysfs_zram_28_0) true)
(typeattribute sysfs_zram_28_0)
(typeattributeset cgroup_bpf_28_0 (cgroup_bpf))
(expandtypeattribute (cgroup_bpf_28_0) true)
(typeattribute cgroup_bpf_28_0)
(typeattributeset wifi_data_file_28_0 (wifi_data_file))
(expandtypeattribute (wifi_data_file_28_0) true)
(typeattribute wifi_data_file_28_0)
(typeattributeset mediametrics_service_28_0 (mediametrics_service))
(expandtypeattribute (mediametrics_service_28_0) true)
(typeattribute mediametrics_service_28_0)
(typeattributeset system_boot_reason_prop_28_0 (system_boot_reason_prop))
(expandtypeattribute (system_boot_reason_prop_28_0) true)
(typeattribute system_boot_reason_prop_28_0)
(typeattributeset hal_lowpan_hwservice_28_0 (hal_lowpan_hwservice))
(expandtypeattribute (hal_lowpan_hwservice_28_0) true)
(typeattribute hal_lowpan_hwservice_28_0)
(typeattributeset assetatlas_service_28_0 (assetatlas_service))
(expandtypeattribute (assetatlas_service_28_0) true)
(typeattribute assetatlas_service_28_0)
(typeattributeset proc_filesystems_28_0 (proc_filesystems))
(expandtypeattribute (proc_filesystems_28_0) true)
(typeattribute proc_filesystems_28_0)
(typeattributeset backup_service_28_0 (backup_service))
(expandtypeattribute (backup_service_28_0) true)
(typeattribute backup_service_28_0)
(typeattributeset pdx_display_manager_channel_socket_28_0 (pdx_display_manager_channel_socket))
(expandtypeattribute (pdx_display_manager_channel_socket_28_0) true)
(typeattribute pdx_display_manager_channel_socket_28_0)
(typeattributeset traced_probes_28_0 (traced_probes))
(expandtypeattribute (traced_probes_28_0) true)
(typeattribute traced_probes_28_0)
(typeattributeset zero_device_28_0 (zero_device))
(expandtypeattribute (zero_device_28_0) true)
(typeattribute zero_device_28_0)
(typeattributeset wpantund_28_0 (wpantund))
(expandtypeattribute (wpantund_28_0) true)
(typeattribute wpantund_28_0)
(typeattributeset hwservice_contexts_file_28_0 (hwservice_contexts_file))
(expandtypeattribute (hwservice_contexts_file_28_0) true)
(typeattribute hwservice_contexts_file_28_0)
(typeattributeset service_contexts_file_28_0 (service_contexts_file))
(expandtypeattribute (service_contexts_file_28_0) true)
(typeattribute service_contexts_file_28_0)
(typeattributeset exported_bluetooth_prop_28_0 (exported_bluetooth_prop))
(expandtypeattribute (exported_bluetooth_prop_28_0) true)
(typeattribute exported_bluetooth_prop_28_0)
(typeattributeset sysfs_uio_28_0 (sysfs_uio))
(expandtypeattribute (sysfs_uio_28_0) true)
(typeattribute sysfs_uio_28_0)
(typeattributeset preopt2cachename_exec_28_0 (preopt2cachename_exec))
(expandtypeattribute (preopt2cachename_exec_28_0) true)
(typeattribute preopt2cachename_exec_28_0)
(typeattributeset vndservice_contexts_file_28_0 (vndservice_contexts_file))
(expandtypeattribute (vndservice_contexts_file_28_0) true)
(typeattribute vndservice_contexts_file_28_0)
(typeattributeset incident_28_0 (incident))
(expandtypeattribute (incident_28_0) true)
(typeattribute incident_28_0)
(typeattributeset cache_block_device_28_0 (cache_block_device))
(expandtypeattribute (cache_block_device_28_0) true)
(typeattribute cache_block_device_28_0)
(typeattributeset crash_dump_exec_28_0 (crash_dump_exec))
(expandtypeattribute (crash_dump_exec_28_0) true)
(typeattribute crash_dump_exec_28_0)
(typeattributeset shell_28_0 (shell))
(expandtypeattribute (shell_28_0) true)
(typeattribute shell_28_0)
(typeattributeset network_time_update_service_28_0 (network_time_update_service))
(expandtypeattribute (network_time_update_service_28_0) true)
(typeattribute network_time_update_service_28_0)
(typeattributeset exported2_radio_prop_28_0 (exported2_radio_prop))
(expandtypeattribute (exported2_radio_prop_28_0) true)
(typeattribute exported2_radio_prop_28_0)
(typeattributeset exported3_radio_prop_28_0 (exported3_radio_prop))
(expandtypeattribute (exported3_radio_prop_28_0) true)
(typeattribute exported3_radio_prop_28_0)
(typeattributeset seapp_contexts_file_28_0 (seapp_contexts_file))
(expandtypeattribute (seapp_contexts_file_28_0) true)
(typeattribute seapp_contexts_file_28_0)
(typeattributeset textclassification_service_28_0 (textclassification_service))
(expandtypeattribute (textclassification_service_28_0) true)
(typeattribute textclassification_service_28_0)
(typeattributeset blkid_untrusted_28_0 (blkid_untrusted))
(expandtypeattribute (blkid_untrusted_28_0) true)
(typeattribute blkid_untrusted_28_0)
(typeattributeset dumpstate_prop_28_0 (dumpstate_prop))
(expandtypeattribute (dumpstate_prop_28_0) true)
(typeattribute dumpstate_prop_28_0)
(typeattributeset zygote_exec_28_0 (zygote_exec))
(expandtypeattribute (zygote_exec_28_0) true)
(typeattribute zygote_exec_28_0)
(typeattributeset cppreopt_prop_28_0 (cppreopt_prop))
(expandtypeattribute (cppreopt_prop_28_0) true)
(typeattribute cppreopt_prop_28_0)
(typeattributeset radio_prop_28_0 (radio_prop))
(expandtypeattribute (radio_prop_28_0) true)
(typeattribute radio_prop_28_0)
(typeattributeset postinstall_mnt_dir_28_0 (postinstall_mnt_dir))
(expandtypeattribute (postinstall_mnt_dir_28_0) true)
(typeattribute postinstall_mnt_dir_28_0)
(typeattributeset misc_user_data_file_28_0 (misc_user_data_file))
(expandtypeattribute (misc_user_data_file_28_0) true)
(typeattribute misc_user_data_file_28_0)
(typeattributeset debugfs_wifi_tracing_28_0 (debugfs_wifi_tracing))
(expandtypeattribute (debugfs_wifi_tracing_28_0) true)
(typeattribute debugfs_wifi_tracing_28_0)
(typeattributeset shell_prop_28_0 (shell_prop))
(expandtypeattribute (shell_prop_28_0) true)
(typeattribute shell_prop_28_0)
(typeattributeset isolated_app_28_0 (isolated_app))
(expandtypeattribute (isolated_app_28_0) true)
(typeattribute isolated_app_28_0)
(typeattributeset sysfs_vibrator_28_0 (sysfs_vibrator))
(expandtypeattribute (sysfs_vibrator_28_0) true)
(typeattribute sysfs_vibrator_28_0)
(typeattributeset network_management_service_28_0 (network_management_service))
(expandtypeattribute (network_management_service_28_0) true)
(typeattribute network_management_service_28_0)
(typeattributeset sysfs_hwrandom_28_0 (sysfs_hwrandom))
(expandtypeattribute (sysfs_hwrandom_28_0) true)
(typeattribute sysfs_hwrandom_28_0)
(typeattributeset system_server_28_0 (system_server))
(expandtypeattribute (system_server_28_0) true)
(typeattribute system_server_28_0)
(typeattributeset device_identifiers_service_28_0 (device_identifiers_service))
(expandtypeattribute (device_identifiers_service_28_0) true)
(typeattribute device_identifiers_service_28_0)
(typeattributeset network_score_service_28_0 (network_score_service))
(expandtypeattribute (network_score_service_28_0) true)
(typeattribute network_score_service_28_0)
(typeattributeset e2fs_28_0 (e2fs))
(expandtypeattribute (e2fs_28_0) true)
(typeattribute e2fs_28_0)
(typeattributeset usbfs_28_0 (usbfs))
(expandtypeattribute (usbfs_28_0) true)
(typeattribute usbfs_28_0)
(typeattributeset debugfs_wakeup_sources_28_0 (debugfs_wakeup_sources))
(expandtypeattribute (debugfs_wakeup_sources_28_0) true)
(typeattribute debugfs_wakeup_sources_28_0)
(typeattributeset incident_helper_28_0 (incident_helper))
(expandtypeattribute (incident_helper_28_0) true)
(typeattribute incident_helper_28_0)
(typeattributeset system_wifi_keystore_hwservice_28_0 (system_wifi_keystore_hwservice))
(expandtypeattribute (system_wifi_keystore_hwservice_28_0) true)
(typeattribute system_wifi_keystore_hwservice_28_0)
(typeattributeset sdcardd_28_0 (sdcardd))
(expandtypeattribute (sdcardd_28_0) true)
(typeattribute sdcardd_28_0)
(typeattributeset thermal_service_28_0 (thermal_service))
(expandtypeattribute (thermal_service_28_0) true)
(typeattribute thermal_service_28_0)
(typeattributeset logdr_socket_28_0 (logdr_socket))
(expandtypeattribute (logdr_socket_28_0) true)
(typeattribute logdr_socket_28_0)
(typeattributeset logdw_socket_28_0 (logdw_socket))
(expandtypeattribute (logdw_socket_28_0) true)
(typeattribute logdw_socket_28_0)
(typeattributeset vpn_data_file_28_0 (vpn_data_file))
(expandtypeattribute (vpn_data_file_28_0) true)
(typeattribute vpn_data_file_28_0)
(typeattributeset dalvikcache_data_file_28_0 (dalvikcache_data_file))
(expandtypeattribute (dalvikcache_data_file_28_0) true)
(typeattribute dalvikcache_data_file_28_0)
(typeattributeset mediaserver_service_28_0 (mediaserver_service))
(expandtypeattribute (mediaserver_service_28_0) true)
(typeattribute mediaserver_service_28_0)
(typeattributeset property_data_file_28_0 (property_data_file))
(expandtypeattribute (property_data_file_28_0) true)
(typeattribute property_data_file_28_0)
(typeattributeset wifi_log_prop_28_0 (wifi_log_prop))
(expandtypeattribute (wifi_log_prop_28_0) true)
(typeattribute wifi_log_prop_28_0)
(typeattributeset accessibility_service_28_0 (accessibility_service))
(expandtypeattribute (accessibility_service_28_0) true)
(typeattribute accessibility_service_28_0)
(typeattributeset camera_device_28_0 (camera_device))
(expandtypeattribute (camera_device_28_0) true)
(typeattribute camera_device_28_0)
(typeattributeset recovery_data_file_28_0 (recovery_data_file))
(expandtypeattribute (recovery_data_file_28_0) true)
(typeattribute recovery_data_file_28_0)
(typeattributeset system_app_data_file_28_0 (system_app_data_file))
(expandtypeattribute (system_app_data_file_28_0) true)
(typeattribute system_app_data_file_28_0)
(typeattributeset sysfs_leds_28_0 (sysfs_leds))
(expandtypeattribute (sysfs_leds_28_0) true)
(typeattribute sysfs_leds_28_0)
(typeattributeset mediacodec_28_0 (mediacodec))
(expandtypeattribute (mediacodec_28_0) true)
(typeattribute mediacodec_28_0)
(typeattributeset pm_prop_28_0 (pm_prop))
(expandtypeattribute (pm_prop_28_0) true)
(typeattribute pm_prop_28_0)
(typeattributeset restrictions_service_28_0 (restrictions_service))
(expandtypeattribute (restrictions_service_28_0) true)
(typeattribute restrictions_service_28_0)
(typeattributeset ffs_prop_28_0 (ffs_prop))
(expandtypeattribute (ffs_prop_28_0) true)
(typeattribute ffs_prop_28_0)
(typeattributeset log_prop_28_0 (log_prop))
(expandtypeattribute (log_prop_28_0) true)
(typeattribute log_prop_28_0)
(typeattributeset mmc_prop_28_0 (mmc_prop))
(expandtypeattribute (mmc_prop_28_0) true)
(typeattribute mmc_prop_28_0)
(typeattributeset nfc_prop_28_0 (nfc_prop))
(expandtypeattribute (nfc_prop_28_0) true)
(typeattribute nfc_prop_28_0)
(typeattributeset toolbox_28_0 (toolbox))
(expandtypeattribute (toolbox_28_0) true)
(typeattribute toolbox_28_0)
(typeattributeset IProxyService_service_28_0 (IProxyService_service))
(expandtypeattribute (IProxyService_service_28_0) true)
(typeattribute IProxyService_service_28_0)
(typeattributeset mnt_media_rw_stub_file_28_0 (mnt_media_rw_stub_file))
(expandtypeattribute (mnt_media_rw_stub_file_28_0) true)
(typeattribute mnt_media_rw_stub_file_28_0)
(typeattributeset proc_28_0 (proc))
(expandtypeattribute (proc_28_0) true)
(typeattribute proc_28_0)
(typeattributeset bluetooth_socket_28_0 (bluetooth_socket))
(expandtypeattribute (bluetooth_socket_28_0) true)
(typeattribute bluetooth_socket_28_0)
(typeattributeset logcat_exec_28_0 (logcat_exec))
(expandtypeattribute (logcat_exec_28_0) true)
(typeattribute logcat_exec_28_0)
(typeattributeset inputflinger_exec_28_0 (inputflinger_exec))
(expandtypeattribute (inputflinger_exec_28_0) true)
(typeattribute inputflinger_exec_28_0)
(typeattributeset overlay_prop_28_0 (overlay_prop))
(expandtypeattribute (overlay_prop_28_0) true)
(typeattribute overlay_prop_28_0)
(typeattributeset last_boot_reason_prop_28_0 (last_boot_reason_prop))
(expandtypeattribute (last_boot_reason_prop_28_0) true)
(typeattribute last_boot_reason_prop_28_0)
(typeattributeset test_boot_reason_prop_28_0 (test_boot_reason_prop))
(expandtypeattribute (test_boot_reason_prop_28_0) true)
(typeattribute test_boot_reason_prop_28_0)
(typeattributeset hal_wifi_hostapd_hwservice_28_0 (hal_wifi_hostapd_hwservice))
(expandtypeattribute (hal_wifi_hostapd_hwservice_28_0) true)
(typeattribute hal_wifi_hostapd_hwservice_28_0)
(typeattributeset hal_wifi_offload_hwservice_28_0 (hal_wifi_offload_hwservice))
(expandtypeattribute (hal_wifi_offload_hwservice_28_0) true)
(typeattribute hal_wifi_offload_hwservice_28_0)
(typeattributeset safemode_prop_28_0 (safemode_prop))
(expandtypeattribute (safemode_prop_28_0) true)
(typeattribute safemode_prop_28_0)
(typeattributeset wallpaper_file_28_0 (wallpaper_file))
(expandtypeattribute (wallpaper_file_28_0) true)
(typeattribute wallpaper_file_28_0)
(typeattributeset shortcut_manager_icons_28_0 (shortcut_manager_icons))
(expandtypeattribute (shortcut_manager_icons_28_0) true)
(typeattribute shortcut_manager_icons_28_0)
(typeattributeset proc_asound_28_0 (proc_asound))
(expandtypeattribute (proc_asound_28_0) true)
(typeattribute proc_asound_28_0)
(typeattributeset hal_neuralnetworks_hwservice_28_0 (hal_neuralnetworks_hwservice))
(expandtypeattribute (hal_neuralnetworks_hwservice_28_0) true)
(typeattribute hal_neuralnetworks_hwservice_28_0)
(typeattributeset dhcp_prop_28_0 (dhcp_prop))
(expandtypeattribute (dhcp_prop_28_0) true)
(typeattribute dhcp_prop_28_0)
(typeattributeset gps_control_28_0 (gps_control))
(expandtypeattribute (gps_control_28_0) true)
(typeattribute gps_control_28_0)
(typeattributeset logd_prop_28_0 (logd_prop))
(expandtypeattribute (logd_prop_28_0) true)
(typeattribute logd_prop_28_0)
(typeattributeset dumpstate_options_prop_28_0 (dumpstate_options_prop))
(expandtypeattribute (dumpstate_options_prop_28_0) true)
(typeattribute dumpstate_options_prop_28_0)
(typeattributeset proc_uid_concurrent_active_time_28_0 (proc_uid_concurrent_active_time))
(expandtypeattribute (proc_uid_concurrent_active_time_28_0) true)
(typeattribute proc_uid_concurrent_active_time_28_0)
(typeattributeset vold_prop_28_0 (vold_prop))
(expandtypeattribute (vold_prop_28_0) true)
(typeattribute vold_prop_28_0)
(typeattributeset wifi_prop_28_0 (wifi_prop))
(expandtypeattribute (wifi_prop_28_0) true)
(typeattribute wifi_prop_28_0)
(typeattributeset imms_service_28_0 (imms_service))
(expandtypeattribute (imms_service_28_0) true)
(typeattribute imms_service_28_0)
(typeattributeset netd_socket_28_0 (netd_socket))
(expandtypeattribute (netd_socket_28_0) true)
(typeattribute netd_socket_28_0)
(typeattributeset sysfs_28_0 (sysfs))
(expandtypeattribute (sysfs_28_0) true)
(typeattribute sysfs_28_0)
(typeattributeset default_android_service_28_0 (default_android_service))
(expandtypeattribute (default_android_service_28_0) true)
(typeattribute default_android_service_28_0)
(typeattributeset bufferhubd_28_0 (bufferhubd))
(expandtypeattribute (bufferhubd_28_0) true)
(typeattribute bufferhubd_28_0)
(typeattributeset thermalserviced_exec_28_0 (thermalserviced_exec))
(expandtypeattribute (thermalserviced_exec_28_0) true)
(typeattribute thermalserviced_exec_28_0)
(typeattributeset sysfs_devices_system_cpu_28_0 (sysfs_devices_system_cpu))
(expandtypeattribute (sysfs_devices_system_cpu_28_0) true)
(typeattribute sysfs_devices_system_cpu_28_0)
(typeattributeset untrusted_app_28_0 (untrusted_app))
(expandtypeattribute (untrusted_app_28_0) true)
(typeattribute untrusted_app_28_0)
(typeattributeset shared_relro_28_0 (shared_relro))
(expandtypeattribute (shared_relro_28_0) true)
(typeattribute shared_relro_28_0)
(typeattributeset hal_wifi_hwservice_28_0 (hal_wifi_hwservice))
(expandtypeattribute (hal_wifi_hwservice_28_0) true)
(typeattribute hal_wifi_hwservice_28_0)
(typeattributeset proc_vmstat_28_0 (proc_vmstat))
(expandtypeattribute (proc_vmstat_28_0) true)
(typeattribute proc_vmstat_28_0)
(typeattributeset alarm_device_28_0 (alarm_device))
(expandtypeattribute (alarm_device_28_0) true)
(typeattribute alarm_device_28_0)
(typeattributeset hal_graphics_mapper_hwservice_28_0 (hal_graphics_mapper_hwservice))
(expandtypeattribute (hal_graphics_mapper_hwservice_28_0) true)
(typeattribute hal_graphics_mapper_hwservice_28_0)
(typeattributeset proc_uid_concurrent_policy_time_28_0 (proc_uid_concurrent_policy_time))
(expandtypeattribute (proc_uid_concurrent_policy_time_28_0) true)
(typeattribute proc_uid_concurrent_policy_time_28_0)
(typeattributeset audio_data_file_28_0 (audio_data_file))
(expandtypeattribute (audio_data_file_28_0) true)
(typeattribute audio_data_file_28_0)
(typeattributeset radio_data_file_28_0 (radio_data_file))
(expandtypeattribute (radio_data_file_28_0) true)
(typeattribute radio_data_file_28_0)
(typeattributeset traceur_app_28_0 (traceur_app))
(expandtypeattribute (traceur_app_28_0) true)
(typeattribute traceur_app_28_0)
(typeattributeset display_service_28_0 (display_service))
(expandtypeattribute (display_service_28_0) true)
(typeattribute display_service_28_0)
(typeattributeset overlay_service_28_0 (overlay_service))
(expandtypeattribute (overlay_service_28_0) true)
(typeattribute overlay_service_28_0)
(typeattributeset hal_wifi_supplicant_hwservice_28_0 (hal_wifi_supplicant_hwservice))
(expandtypeattribute (hal_wifi_supplicant_hwservice_28_0) true)
(typeattribute hal_wifi_supplicant_hwservice_28_0)
(typeattributeset performanced_exec_28_0 (performanced_exec))
(expandtypeattribute (performanced_exec_28_0) true)
(typeattribute performanced_exec_28_0)
(typeattributeset usbd_28_0 (usbd))
(expandtypeattribute (usbd_28_0) true)
(typeattribute usbd_28_0)
(typeattributeset proc_page_cluster_28_0 (proc_page_cluster))
(expandtypeattribute (proc_page_cluster_28_0) true)
(typeattribute proc_page_cluster_28_0)
(typeattributeset hal_health_hwservice_28_0 (hal_health_hwservice))
(expandtypeattribute (hal_health_hwservice_28_0) true)
(typeattribute hal_health_hwservice_28_0)
(typeattributeset system_wpa_socket_28_0 (system_wpa_socket))
(expandtypeattribute (system_wpa_socket_28_0) true)
(typeattribute system_wpa_socket_28_0)
(typeattributeset sysfs_rtc_28_0 (sysfs_rtc))
(expandtypeattribute (sysfs_rtc_28_0) true)
(typeattribute sysfs_rtc_28_0)
(typeattributeset fsck_28_0 (fsck))
(expandtypeattribute (fsck_28_0) true)
(typeattribute fsck_28_0)
(typeattributeset fs_bpf_28_0 (fs_bpf))
(expandtypeattribute (fs_bpf_28_0) true)
(typeattribute fs_bpf_28_0)
(typeattributeset netd_listener_service_28_0 (netd_listener_service))
(expandtypeattribute (netd_listener_service_28_0) true)
(typeattribute netd_listener_service_28_0)
(typeattributeset app_fusefs_28_0 (app_fusefs))
(expandtypeattribute (app_fusefs_28_0) true)
(typeattribute app_fusefs_28_0)
(typeattributeset proc_overcommit_memory_28_0 (proc_overcommit_memory))
(expandtypeattribute (proc_overcommit_memory_28_0) true)
(typeattribute proc_overcommit_memory_28_0)
(typeattributeset ctl_dumpstate_prop_28_0 (ctl_dumpstate_prop))
(expandtypeattribute (ctl_dumpstate_prop_28_0) true)
(typeattribute ctl_dumpstate_prop_28_0)
(typeattributeset connmetrics_service_28_0 (connmetrics_service))
(expandtypeattribute (connmetrics_service_28_0) true)
(typeattribute connmetrics_service_28_0)
(typeattributeset proc_uid_cputime_removeuid_28_0 (proc_uid_cputime_removeuid))
(expandtypeattribute (proc_uid_cputime_removeuid_28_0) true)
(typeattribute proc_uid_cputime_removeuid_28_0)
(typeattributeset boot_block_device_28_0 (boot_block_device))
(expandtypeattribute (boot_block_device_28_0) true)
(typeattribute boot_block_device_28_0)
(typeattributeset powerctl_prop_28_0 (powerctl_prop))
(expandtypeattribute (powerctl_prop_28_0) true)
(typeattribute powerctl_prop_28_0)
(typeattributeset sysfs_android_usb_28_0 (sysfs_android_usb))
(expandtypeattribute (sysfs_android_usb_28_0) true)
(typeattribute sysfs_android_usb_28_0)
(typeattributeset misc_block_device_28_0 (misc_block_device))
(expandtypeattribute (misc_block_device_28_0) true)
(typeattribute misc_block_device_28_0)
(typeattributeset root_block_device_28_0 (root_block_device))
(expandtypeattribute (root_block_device_28_0) true)
(typeattribute root_block_device_28_0)
(typeattributeset swap_block_device_28_0 (swap_block_device))
(expandtypeattribute (swap_block_device_28_0) true)
(typeattribute swap_block_device_28_0)
(typeattributeset block_device_28_0 (block_device))
(expandtypeattribute (block_device_28_0) true)
(typeattribute block_device_28_0)
(typeattributeset frp_block_device_28_0 (frp_block_device))
(expandtypeattribute (frp_block_device_28_0) true)
(typeattribute frp_block_device_28_0)
(typeattributeset sysfs_power_28_0 (sysfs_power))
(expandtypeattribute (sysfs_power_28_0) true)
(typeattribute sysfs_power_28_0)
(typeattributeset loop_device_28_0 (loop_device))
(expandtypeattribute (loop_device_28_0) true)
(typeattribute loop_device_28_0)
(typeattributeset proc_random_28_0 (proc_random))
(expandtypeattribute (proc_random_28_0) true)
(typeattribute proc_random_28_0)
(typeattributeset dnsproxyd_socket_28_0 (dnsproxyd_socket))
(expandtypeattribute (dnsproxyd_socket_28_0) true)
(typeattribute dnsproxyd_socket_28_0)
(typeattributeset crash_dump_28_0 (crash_dump))
(expandtypeattribute (crash_dump_28_0) true)
(typeattribute crash_dump_28_0)
(typeattributeset hal_keymaster_hwservice_28_0 (hal_keymaster_hwservice))
(expandtypeattribute (hal_keymaster_hwservice_28_0) true)
(typeattribute hal_keymaster_hwservice_28_0)
(typeattributeset ctl_interface_start_prop_28_0 (ctl_interface_start_prop))
(expandtypeattribute (ctl_interface_start_prop_28_0) true)
(typeattribute ctl_interface_start_prop_28_0)
(typeattributeset sysfs_wakeup_reasons_28_0 (sysfs_wakeup_reasons))
(expandtypeattribute (sysfs_wakeup_reasons_28_0) true)
(typeattribute sysfs_wakeup_reasons_28_0)
(typeattributeset shm_28_0 (shm))
(expandtypeattribute (shm_28_0) true)
(typeattribute shm_28_0)
(typeattributeset installd_service_28_0 (installd_service))
(expandtypeattribute (installd_service_28_0) true)
(typeattribute installd_service_28_0)
(typeattributeset proc_extra_free_kbytes_28_0 (proc_extra_free_kbytes))
(expandtypeattribute (proc_extra_free_kbytes_28_0) true)
(typeattribute proc_extra_free_kbytes_28_0)
(typeattributeset proc_uid_cputime_showstat_28_0 (proc_uid_cputime_showstat))
(expandtypeattribute (proc_uid_cputime_showstat_28_0) true)
(typeattribute proc_uid_cputime_showstat_28_0)
(typeattributeset bootstat_exec_28_0 (bootstat_exec))
(expandtypeattribute (bootstat_exec_28_0) true)
(typeattribute bootstat_exec_28_0)
(typeattributeset mdns_socket_28_0 (mdns_socket))
(expandtypeattribute (mdns_socket_28_0) true)
(typeattribute mdns_socket_28_0)
(typeattributeset oem_lock_service_28_0 (oem_lock_service))
(expandtypeattribute (oem_lock_service_28_0) true)
(typeattribute oem_lock_service_28_0)
(typeattributeset audioserver_28_0 (audioserver))
(expandtypeattribute (audioserver_28_0) true)
(typeattribute audioserver_28_0)
(typeattributeset tmpfs_28_0 (tmpfs))
(expandtypeattribute (tmpfs_28_0) true)
(typeattribute tmpfs_28_0)
(typeattributeset bootanim_exec_28_0 (bootanim_exec))
(expandtypeattribute (bootanim_exec_28_0) true)
(typeattribute bootanim_exec_28_0)
(typeattributeset vendor_configs_file_28_0 (vendor_configs_file))
(expandtypeattribute (vendor_configs_file_28_0) true)
(typeattribute vendor_configs_file_28_0)
(typeattributeset vendor_shell_exec_28_0 (vendor_shell_exec))
(expandtypeattribute (vendor_shell_exec_28_0) true)
(typeattribute vendor_shell_exec_28_0)
(typeattributeset hwservicemanager_prop_28_0 (hwservicemanager_prop))
(expandtypeattribute (hwservicemanager_prop_28_0) true)
(typeattribute hwservicemanager_prop_28_0)
(typeattributeset perfprofd_service_28_0 (perfprofd_service))
(expandtypeattribute (perfprofd_service_28_0) true)
(typeattribute perfprofd_service_28_0)
(typeattributeset clatd_28_0 (clatd))
(expandtypeattribute (clatd_28_0) true)
(typeattribute clatd_28_0)
(typeattributeset input_device_28_0 (input_device))
(expandtypeattribute (input_device_28_0) true)
(typeattribute input_device_28_0)
(typeattributeset DockObserver_service_28_0 (DockObserver_service))
(expandtypeattribute (DockObserver_service_28_0) true)
(typeattribute DockObserver_service_28_0)
(typeattributeset shell_data_file_28_0 (shell_data_file))
(expandtypeattribute (shell_data_file_28_0) true)
(typeattribute shell_data_file_28_0)
(typeattributeset bluetooth_manager_service_28_0 (bluetooth_manager_service))
(expandtypeattribute (bluetooth_manager_service_28_0) true)
(typeattribute bluetooth_manager_service_28_0)
(typeattributeset contexthub_service_28_0 (contexthub_service))
(expandtypeattribute (contexthub_service_28_0) true)
(typeattribute contexthub_service_28_0)
(typeattributeset dnsmasq_28_0 (dnsmasq))
(expandtypeattribute (dnsmasq_28_0) true)
(typeattribute dnsmasq_28_0)
(typeattributeset dumpstate_28_0 (dumpstate))
(expandtypeattribute (dumpstate_28_0) true)
(typeattribute dumpstate_28_0)
(typeattributeset hal_vehicle_hwservice_28_0 (hal_vehicle_hwservice))
(expandtypeattribute (hal_vehicle_hwservice_28_0) true)
(typeattribute hal_vehicle_hwservice_28_0)
(typeattributeset bootloader_boot_reason_prop_28_0 (bootloader_boot_reason_prop))
(expandtypeattribute (bootloader_boot_reason_prop_28_0) true)
(typeattribute bootloader_boot_reason_prop_28_0)
(typeattributeset install_data_file_28_0 (install_data_file))
(expandtypeattribute (install_data_file_28_0) true)
(typeattribute install_data_file_28_0)
(typeattributeset asec_image_file_28_0 (asec_image_file))
(expandtypeattribute (asec_image_file_28_0) true)
(typeattribute asec_image_file_28_0)
(typeattributeset hardware_properties_service_28_0 (hardware_properties_service))
(expandtypeattribute (hardware_properties_service_28_0) true)
(typeattribute hardware_properties_service_28_0)
(typeattributeset owntty_device_28_0 (owntty_device))
(expandtypeattribute (owntty_device_28_0) true)
(typeattribute owntty_device_28_0)
(typeattributeset null_device_28_0 (null_device))
(expandtypeattribute (null_device_28_0) true)
(typeattribute null_device_28_0)
(typeattributeset uimode_service_28_0 (uimode_service))
(expandtypeattribute (uimode_service_28_0) true)
(typeattribute uimode_service_28_0)
(typeattributeset uncrypt_exec_28_0 (uncrypt_exec))
(expandtypeattribute (uncrypt_exec_28_0) true)
(typeattribute uncrypt_exec_28_0)
(typeattributeset priv_app_28_0 (priv_app))
(expandtypeattribute (priv_app_28_0) true)
(typeattribute priv_app_28_0)
(typeattributeset hal_bluetooth_hwservice_28_0 (hal_bluetooth_hwservice))
(expandtypeattribute (hal_bluetooth_hwservice_28_0) true)
(typeattribute hal_bluetooth_hwservice_28_0)
(typeattributeset wifiscanner_service_28_0 (wifiscanner_service))
(expandtypeattribute (wifiscanner_service_28_0) true)
(typeattribute wifiscanner_service_28_0)
(typeattributeset audio_timer_device_28_0 (audio_timer_device))
(expandtypeattribute (audio_timer_device_28_0) true)
(typeattribute audio_timer_device_28_0)
(typeattributeset bluetooth_data_file_28_0 (bluetooth_data_file))
(expandtypeattribute (bluetooth_data_file_28_0) true)
(typeattribute bluetooth_data_file_28_0)
(typeattributeset loop_control_device_28_0 (loop_control_device))
(expandtypeattribute (loop_control_device_28_0) true)
(typeattribute loop_control_device_28_0)
(typeattributeset pdx_display_client_endpoint_socket_28_0 (pdx_display_client_endpoint_socket))
(expandtypeattribute (pdx_display_client_endpoint_socket_28_0) true)
(typeattribute pdx_display_client_endpoint_socket_28_0)
(typeattributeset net_dns_prop_28_0 (net_dns_prop))
(expandtypeattribute (net_dns_prop_28_0) true)
(typeattribute net_dns_prop_28_0)
(typeattributeset fingerprintd_28_0 (fingerprintd))
(expandtypeattribute (fingerprintd_28_0) true)
(typeattribute fingerprintd_28_0)
(typeattributeset bluetooth_prop_28_0 (bluetooth_prop))
(expandtypeattribute (bluetooth_prop_28_0) true)
(typeattribute bluetooth_prop_28_0)
(typeattributeset hal_power_hwservice_28_0 (hal_power_hwservice))
(expandtypeattribute (hal_power_hwservice_28_0) true)
(typeattribute hal_power_hwservice_28_0)
(typeattributeset hal_fingerprint_hwservice_28_0 (hal_fingerprint_hwservice))
(expandtypeattribute (hal_fingerprint_hwservice_28_0) true)
(typeattribute hal_fingerprint_hwservice_28_0)
(typeattributeset exported_pm_prop_28_0 (exported_pm_prop))
(expandtypeattribute (exported_pm_prop_28_0) true)
(typeattribute exported_pm_prop_28_0)
(typeattributeset ctl_restart_prop_28_0 (ctl_restart_prop))
(expandtypeattribute (ctl_restart_prop_28_0) true)
(typeattribute ctl_restart_prop_28_0)
(typeattributeset font_service_28_0 (font_service))
(expandtypeattribute (font_service_28_0) true)
(typeattribute font_service_28_0)
(typeattributeset timezone_service_28_0 (timezone_service))
(expandtypeattribute (timezone_service_28_0) true)
(typeattribute timezone_service_28_0)
(typeattributeset wificond_service_28_0 (wificond_service))
(expandtypeattribute (wificond_service_28_0) true)
(typeattribute wificond_service_28_0)
(typeattributeset postinstall_file_28_0 (postinstall_file))
(expandtypeattribute (postinstall_file_28_0) true)
(typeattribute postinstall_file_28_0)
(typeattributeset exported2_system_prop_28_0 (exported2_system_prop))
(expandtypeattribute (exported2_system_prop_28_0) true)
(typeattribute exported2_system_prop_28_0)
(typeattributeset exported3_system_prop_28_0 (exported3_system_prop))
(expandtypeattribute (exported3_system_prop_28_0) true)
(typeattribute exported3_system_prop_28_0)
(typeattributeset exported_dumpstate_prop_28_0 (exported_dumpstate_prop))
(expandtypeattribute (exported_dumpstate_prop_28_0) true)
(typeattribute exported_dumpstate_prop_28_0)
(typeattributeset qtaguid_proc_28_0 (qtaguid_proc))
(expandtypeattribute (qtaguid_proc_28_0) true)
(typeattribute qtaguid_proc_28_0)
(typeattributeset ctl_bugreport_prop_28_0 (ctl_bugreport_prop))
(expandtypeattribute (ctl_bugreport_prop_28_0) true)
(typeattribute ctl_bugreport_prop_28_0)
(typeattributeset vold_service_28_0 (vold_service))
(expandtypeattribute (vold_service_28_0) true)
(typeattribute vold_service_28_0)
(typeattributeset hal_tv_input_hwservice_28_0 (hal_tv_input_hwservice))
(expandtypeattribute (hal_tv_input_hwservice_28_0) true)
(typeattribute hal_tv_input_hwservice_28_0)
(typeattributeset dalvik_prop_28_0 (dalvik_prop))
(expandtypeattribute (dalvik_prop_28_0) true)
(typeattribute dalvik_prop_28_0)
(typeattributeset inputflinger_service_28_0 (inputflinger_service))
(expandtypeattribute (inputflinger_service_28_0) true)
(typeattribute inputflinger_service_28_0)
(typeattributeset audio_seq_device_28_0 (audio_seq_device))
(expandtypeattribute (audio_seq_device_28_0) true)
(typeattribute audio_seq_device_28_0)
(typeattributeset mtpd_socket_28_0 (mtpd_socket))
(expandtypeattribute (mtpd_socket_28_0) true)
(typeattribute mtpd_socket_28_0)
(typeattributeset keystore_service_28_0 (keystore_service))
(expandtypeattribute (keystore_service_28_0) true)
(typeattribute keystore_service_28_0)
(typeattributeset bluetooth_service_28_0 (bluetooth_service))
(expandtypeattribute (bluetooth_service_28_0) true)
(typeattribute bluetooth_service_28_0)
(typeattributeset appops_service_28_0 (appops_service))
(expandtypeattribute (appops_service_28_0) true)
(typeattribute appops_service_28_0)
(typeattributeset sysfs_kernel_notes_28_0 (sysfs_kernel_notes))
(expandtypeattribute (sysfs_kernel_notes_28_0) true)
(typeattribute sysfs_kernel_notes_28_0)
(typeattributeset system_data_file_28_0 (system_data_file))
(expandtypeattribute (system_data_file_28_0) true)
(typeattribute system_data_file_28_0)
(typeattributeset devpts_28_0 (devpts))
(expandtypeattribute (devpts_28_0) true)
(typeattribute devpts_28_0)
(typeattributeset drm_data_file_28_0 (drm_data_file))
(expandtypeattribute (drm_data_file_28_0) true)
(typeattribute drm_data_file_28_0)
(typeattributeset hal_broadcastradio_hwservice_28_0 (hal_broadcastradio_hwservice))
(expandtypeattribute (hal_broadcastradio_hwservice_28_0) true)
(typeattribute hal_broadcastradio_hwservice_28_0)
(typeattributeset system_net_netd_hwservice_28_0 (system_net_netd_hwservice))
(expandtypeattribute (system_net_netd_hwservice_28_0) true)
(typeattribute system_net_netd_hwservice_28_0)
(typeattributeset su_28_0 (su))
(expandtypeattribute (su_28_0) true)
(typeattribute su_28_0)
(typeattributeset update_engine_log_data_file_28_0 (update_engine_log_data_file))
(expandtypeattribute (update_engine_log_data_file_28_0) true)
(typeattribute update_engine_log_data_file_28_0)
(typeattributeset otadexopt_service_28_0 (otadexopt_service))
(expandtypeattribute (otadexopt_service_28_0) true)
(typeattribute otadexopt_service_28_0)
(typeattributeset proc_abi_28_0 (proc_abi))
(expandtypeattribute (proc_abi_28_0) true)
(typeattribute proc_abi_28_0)
(typeattributeset proc_security_28_0 (proc_security))
(expandtypeattribute (proc_security_28_0) true)
(typeattribute proc_security_28_0)
(typeattributeset nfc_service_28_0 (nfc_service))
(expandtypeattribute (nfc_service_28_0) true)
(typeattribute nfc_service_28_0)
(typeattributeset settings_service_28_0 (settings_service))
(expandtypeattribute (settings_service_28_0) true)
(typeattribute settings_service_28_0)
(typeattributeset cppreopts_28_0 (cppreopts))
(expandtypeattribute (cppreopts_28_0) true)
(typeattribute cppreopts_28_0)
(typeattributeset debugfs_tracing_debug_28_0 (debugfs_tracing_debug))
(expandtypeattribute (debugfs_tracing_debug_28_0) true)
(typeattribute debugfs_tracing_debug_28_0)
(typeattributeset cpuinfo_service_28_0 (cpuinfo_service))
(expandtypeattribute (cpuinfo_service_28_0) true)
(typeattribute cpuinfo_service_28_0)
(typeattributeset dbinfo_service_28_0 (dbinfo_service))
(expandtypeattribute (dbinfo_service_28_0) true)
(typeattribute dbinfo_service_28_0)
(typeattributeset gfxinfo_service_28_0 (gfxinfo_service))
(expandtypeattribute (gfxinfo_service_28_0) true)
(typeattribute gfxinfo_service_28_0)
(typeattributeset meminfo_service_28_0 (meminfo_service))
(expandtypeattribute (meminfo_service_28_0) true)
(typeattribute meminfo_service_28_0)
(typeattributeset dnsmasq_exec_28_0 (dnsmasq_exec))
(expandtypeattribute (dnsmasq_exec_28_0) true)
(typeattribute dnsmasq_exec_28_0)
(typeattributeset ppp_28_0 (ppp))
(expandtypeattribute (ppp_28_0) true)
(typeattribute ppp_28_0)
(typeattributeset vndbinder_device_28_0 (vndbinder_device))
(expandtypeattribute (vndbinder_device_28_0) true)
(typeattribute vndbinder_device_28_0)
(typeattributeset ethernet_service_28_0 (ethernet_service))
(expandtypeattribute (ethernet_service_28_0) true)
(typeattribute ethernet_service_28_0)
(typeattributeset pinner_service_28_0 (pinner_service))
(expandtypeattribute (pinner_service_28_0) true)
(typeattribute pinner_service_28_0)
(typeattributeset performanced_28_0 (performanced))
(expandtypeattribute (performanced_28_0) true)
(typeattribute performanced_28_0)
(typeattributeset systemkeys_data_file_28_0 (systemkeys_data_file))
(expandtypeattribute (systemkeys_data_file_28_0) true)
(typeattribute systemkeys_data_file_28_0)
(typeattributeset network_watchlist_data_file_28_0 (network_watchlist_data_file))
(expandtypeattribute (network_watchlist_data_file_28_0) true)
(typeattribute network_watchlist_data_file_28_0)
(typeattributeset binder_device_28_0 (binder_device))
(expandtypeattribute (binder_device_28_0) true)
(typeattribute binder_device_28_0)
(typeattributeset hwbinder_device_28_0 (hwbinder_device))
(expandtypeattribute (hwbinder_device_28_0) true)
(typeattribute hwbinder_device_28_0)
(typeattributeset hw_random_device_28_0 (hw_random_device))
(expandtypeattribute (hw_random_device_28_0) true)
(typeattribute hw_random_device_28_0)
(typeattributeset random_device_28_0 (random_device))
(expandtypeattribute (random_device_28_0) true)
(typeattribute random_device_28_0)
(typeattributeset ctl_default_prop_28_0 (ctl_default_prop))
(expandtypeattribute (ctl_default_prop_28_0) true)
(typeattribute ctl_default_prop_28_0)
(typeattributeset userdata_block_device_28_0 (userdata_block_device))
(expandtypeattribute (userdata_block_device_28_0) true)
(typeattribute userdata_block_device_28_0)
(typeattributeset default_prop_28_0 (default_prop))
(expandtypeattribute (default_prop_28_0) true)
(typeattribute default_prop_28_0)
(typeattributeset vndk_sp_file_28_0 (vndk_sp_file))
(expandtypeattribute (vndk_sp_file_28_0) true)
(typeattribute vndk_sp_file_28_0)
(typeattributeset cameraserver_28_0 (cameraserver))
(expandtypeattribute (cameraserver_28_0) true)
(typeattribute cameraserver_28_0)
(typeattributeset otapreopt_slot_exec_28_0 (otapreopt_slot_exec))
(expandtypeattribute (otapreopt_slot_exec_28_0) true)
(typeattribute otapreopt_slot_exec_28_0)
(typeattributeset debuggerd_prop_28_0 (debuggerd_prop))
(expandtypeattribute (debuggerd_prop_28_0) true)
(typeattribute debuggerd_prop_28_0)
(typeattributeset vendor_toolbox_exec_28_0 (vendor_toolbox_exec))
(expandtypeattribute (vendor_toolbox_exec_28_0) true)
(typeattribute vendor_toolbox_exec_28_0)
(typeattributeset shared_relro_file_28_0 (shared_relro_file))
(expandtypeattribute (shared_relro_file_28_0) true)
(typeattribute shared_relro_file_28_0)
(typeattributeset lmkd_socket_28_0 (lmkd_socket))
(expandtypeattribute (lmkd_socket_28_0) true)
(typeattribute lmkd_socket_28_0)
(typeattributeset exported2_config_prop_28_0 (exported2_config_prop))
(expandtypeattribute (exported2_config_prop_28_0) true)
(typeattribute exported2_config_prop_28_0)
(typeattributeset hal_drm_hwservice_28_0 (hal_drm_hwservice))
(expandtypeattribute (hal_drm_hwservice_28_0) true)
(typeattribute hal_drm_hwservice_28_0)
(typeattributeset ringtone_file_28_0 (ringtone_file))
(expandtypeattribute (ringtone_file_28_0) true)
(typeattribute ringtone_file_28_0)
(typeattributeset toolbox_exec_28_0 (toolbox_exec))
(expandtypeattribute (toolbox_exec_28_0) true)
(typeattribute toolbox_exec_28_0)
(typeattributeset permission_service_28_0 (permission_service))
(expandtypeattribute (permission_service_28_0) true)
(typeattribute permission_service_28_0)
(typeattributeset metadata_block_device_28_0 (metadata_block_device))
(expandtypeattribute (metadata_block_device_28_0) true)
(typeattribute metadata_block_device_28_0)
(typeattributeset logd_socket_28_0 (logd_socket))
(expandtypeattribute (logd_socket_28_0) true)
(typeattribute logd_socket_28_0)
(typeattributeset vendor_default_prop_28_0 (vendor_default_prop))
(expandtypeattribute (vendor_default_prop_28_0) true)
(typeattribute vendor_default_prop_28_0)
(typeattributeset mac_perms_file_28_0 (mac_perms_file))
(expandtypeattribute (mac_perms_file_28_0) true)
(typeattribute mac_perms_file_28_0)
(typeattributeset vendor_app_file_28_0 (vendor_app_file))
(expandtypeattribute (vendor_app_file_28_0) true)
(typeattribute vendor_app_file_28_0)
(typeattributeset vendor_hal_file_28_0 (vendor_hal_file))
(expandtypeattribute (vendor_hal_file_28_0) true)
(typeattribute vendor_hal_file_28_0)
(typeattributeset exported_ffs_prop_28_0 (exported_ffs_prop))
(expandtypeattribute (exported_ffs_prop_28_0) true)
(typeattribute exported_ffs_prop_28_0)
(typeattributeset sysfs_wlan_fwpath_28_0 (sysfs_wlan_fwpath))
(expandtypeattribute (sysfs_wlan_fwpath_28_0) true)
(typeattribute sysfs_wlan_fwpath_28_0)
(typeattributeset nfc_28_0 (nfc))
(expandtypeattribute (nfc_28_0) true)
(typeattribute nfc_28_0)
(typeattributeset default_android_vndservice_28_0 (default_android_vndservice))
(expandtypeattribute (default_android_vndservice_28_0) true)
(typeattribute default_android_vndservice_28_0)
(typeattributeset rtc_device_28_0 (rtc_device))
(expandtypeattribute (rtc_device_28_0) true)
(typeattribute rtc_device_28_0)
(typeattributeset ram_device_28_0 (ram_device))
(expandtypeattribute (ram_device_28_0) true)
(typeattribute ram_device_28_0)
(typeattributeset perfprofd_data_file_28_0 (perfprofd_data_file))
(expandtypeattribute (perfprofd_data_file_28_0) true)
(typeattribute perfprofd_data_file_28_0)
(typeattributeset nativetest_data_file_28_0 (nativetest_data_file))
(expandtypeattribute (nativetest_data_file_28_0) true)
(typeattribute nativetest_data_file_28_0)
(typeattributeset country_detector_service_28_0 (country_detector_service))
(expandtypeattribute (country_detector_service_28_0) true)
(typeattribute country_detector_service_28_0)
(typeattributeset cgroup_28_0 (cgroup))
(expandtypeattribute (cgroup_28_0) true)
(typeattribute cgroup_28_0)
(typeattributeset drmserver_socket_28_0 (drmserver_socket))
(expandtypeattribute (drmserver_socket_28_0) true)
(typeattribute drmserver_socket_28_0)
(typeattributeset ppp_device_28_0 (ppp_device))
(expandtypeattribute (ppp_device_28_0) true)
(typeattribute ppp_device_28_0)
(typeattributeset proc_net_28_0 (proc_net))
(expandtypeattribute (proc_net_28_0) true)
(typeattribute proc_net_28_0)
(typeattributeset zygote_28_0 (zygote))
(expandtypeattribute (zygote_28_0) true)
(typeattribute zygote_28_0)
(typeattributeset vendor_data_file_28_0 (vendor_data_file))
(expandtypeattribute (vendor_data_file_28_0) true)
(typeattribute vendor_data_file_28_0)
(typeattributeset untrusted_app_25_28_0 (untrusted_app_25))
(expandtypeattribute (untrusted_app_25_28_0) true)
(typeattribute untrusted_app_25_28_0)
(typeattributeset untrusted_app_27_28_0 (untrusted_app_27))
(expandtypeattribute (untrusted_app_27_28_0) true)
(typeattribute untrusted_app_27_28_0)
(typeattributeset adb_data_file_28_0 (adb_data_file))
(expandtypeattribute (adb_data_file_28_0) true)
(typeattribute adb_data_file_28_0)
(typeattributeset anr_data_file_28_0 (anr_data_file))
(expandtypeattribute (anr_data_file_28_0) true)
(typeattribute anr_data_file_28_0)
(typeattributeset adb_keys_file_28_0 (adb_keys_file))
(expandtypeattribute (adb_keys_file_28_0) true)
(typeattribute adb_keys_file_28_0)
(typeattributeset kernel_28_0 (kernel))
(expandtypeattribute (kernel_28_0) true)
(typeattribute kernel_28_0)
(typeattributeset servicediscovery_service_28_0 (servicediscovery_service))
(expandtypeattribute (servicediscovery_service_28_0) true)
(typeattribute servicediscovery_service_28_0)
(typeattributeset coverage_service_28_0 (coverage_service))
(expandtypeattribute (coverage_service_28_0) true)
(typeattribute coverage_service_28_0)
(typeattributeset keystore_28_0 (keystore))
(expandtypeattribute (keystore_28_0) true)
(typeattribute keystore_28_0)
(typeattributeset package_service_28_0 (package_service))
(expandtypeattribute (package_service_28_0) true)
(typeattribute package_service_28_0)
(typeattributeset ephemeral_app_28_0 (ephemeral_app))
(expandtypeattribute (ephemeral_app_28_0) true)
(typeattribute ephemeral_app_28_0)
(typeattributeset bluetooth_28_0 (bluetooth))
(expandtypeattribute (bluetooth_28_0) true)
(typeattribute bluetooth_28_0)
(typeattributeset inputflinger_28_0 (inputflinger))
(expandtypeattribute (inputflinger_28_0) true)
(typeattribute inputflinger_28_0)
(typeattributeset sysfs_net_28_0 (sysfs_net))
(expandtypeattribute (sysfs_net_28_0) true)
(typeattribute sysfs_net_28_0)
(typeattributeset vcs_device_28_0 (vcs_device))
(expandtypeattribute (vcs_device_28_0) true)
(typeattribute vcs_device_28_0)
(typeattributeset hal_oemlock_hwservice_28_0 (hal_oemlock_hwservice))
(expandtypeattribute (hal_oemlock_hwservice_28_0) true)
(typeattribute hal_oemlock_hwservice_28_0)
(typeattributeset port_28_0 (port))
(expandtypeattribute (port_28_0) true)
(typeattribute port_28_0)
(typeattributeset virtual_touchpad_exec_28_0 (virtual_touchpad_exec))
(expandtypeattribute (virtual_touchpad_exec_28_0) true)
(typeattribute virtual_touchpad_exec_28_0)
(typeattributeset runas_28_0 (runas))
(expandtypeattribute (runas_28_0) true)
(typeattribute runas_28_0)
(typeattributeset tty_device_28_0 (tty_device))
(expandtypeattribute (tty_device_28_0) true)
(typeattribute tty_device_28_0)
(typeattributeset tun_device_28_0 (tun_device))
(expandtypeattribute (tun_device_28_0) true)
(typeattribute tun_device_28_0)
(typeattributeset clatd_exec_28_0 (clatd_exec))
(expandtypeattribute (clatd_exec_28_0) true)
(typeattribute clatd_exec_28_0)
(typeattributeset uio_device_28_0 (uio_device))
(expandtypeattribute (uio_device_28_0) true)
(typeattribute uio_device_28_0)
(typeattributeset usb_device_28_0 (usb_device))
(expandtypeattribute (usb_device_28_0) true)
(typeattribute usb_device_28_0)
(typeattributeset tv_input_service_28_0 (tv_input_service))
(expandtypeattribute (tv_input_service_28_0) true)
(typeattribute tv_input_service_28_0)
(typeattributeset input_service_28_0 (input_service))
(expandtypeattribute (input_service_28_0) true)
(typeattribute input_service_28_0)
(typeattributeset tee_device_28_0 (tee_device))
(expandtypeattribute (tee_device_28_0) true)
(typeattribute tee_device_28_0)
(typeattributeset hal_memtrack_hwservice_28_0 (hal_memtrack_hwservice))
(expandtypeattribute (hal_memtrack_hwservice_28_0) true)
(typeattribute hal_memtrack_hwservice_28_0)
(typeattributeset radio_device_28_0 (radio_device))
(expandtypeattribute (radio_device_28_0) true)
(typeattribute radio_device_28_0)
(typeattributeset bootstat_data_file_28_0 (bootstat_data_file))
(expandtypeattribute (bootstat_data_file_28_0) true)
(typeattribute bootstat_data_file_28_0)
(typeattributeset ptmx_device_28_0 (ptmx_device))
(expandtypeattribute (ptmx_device_28_0) true)
(typeattribute ptmx_device_28_0)
(typeattributeset textservices_service_28_0 (textservices_service))
(expandtypeattribute (textservices_service_28_0) true)
(typeattribute textservices_service_28_0)
(typeattributeset usbaccessory_device_28_0 (usbaccessory_device))
(expandtypeattribute (usbaccessory_device_28_0) true)
(typeattribute usbaccessory_device_28_0)
(typeattributeset asec_public_file_28_0 (asec_public_file))
(expandtypeattribute (asec_public_file_28_0) true)
(typeattribute asec_public_file_28_0)
(typeattributeset proc_min_free_order_shift_28_0 (proc_min_free_order_shift))
(expandtypeattribute (proc_min_free_order_shift_28_0) true)
(typeattribute proc_min_free_order_shift_28_0)
(typeattributeset hal_usb_hwservice_28_0 (hal_usb_hwservice))
(expandtypeattribute (hal_usb_hwservice_28_0) true)
(typeattribute hal_usb_hwservice_28_0)
(typeattributeset recovery_refresh_exec_28_0 (recovery_refresh_exec))
(expandtypeattribute (recovery_refresh_exec_28_0) true)
(typeattribute recovery_refresh_exec_28_0)
(typeattributeset pdx_performance_client_endpoint_socket_28_0 (pdx_performance_client_endpoint_socket))
(expandtypeattribute (pdx_performance_client_endpoint_socket_28_0) true)
(typeattribute pdx_performance_client_endpoint_socket_28_0)
(typeattributeset user_profile_data_file_28_0 (user_profile_data_file))
(expandtypeattribute (user_profile_data_file_28_0) true)
(typeattribute user_profile_data_file_28_0)
(typeattributeset input_method_service_28_0 (input_method_service))
(expandtypeattribute (input_method_service_28_0) true)
(typeattribute input_method_service_28_0)
(typeattributeset media_projection_service_28_0 (media_projection_service))
(expandtypeattribute (media_projection_service_28_0) true)
(typeattribute media_projection_service_28_0)
(typeattributeset racoon_socket_28_0 (racoon_socket))
(expandtypeattribute (racoon_socket_28_0) true)
(typeattribute racoon_socket_28_0)
(typeattributeset proc_vmallocinfo_28_0 (proc_vmallocinfo))
(expandtypeattribute (proc_vmallocinfo_28_0) true)
(typeattribute proc_vmallocinfo_28_0)
(typeattributeset cameraserver_service_28_0 (cameraserver_service))
(expandtypeattribute (cameraserver_service_28_0) true)
(typeattribute cameraserver_service_28_0)
(typeattributeset idmap_exec_28_0 (idmap_exec))
(expandtypeattribute (idmap_exec_28_0) true)
(typeattribute idmap_exec_28_0)
(typeattributeset uncrypt_socket_28_0 (uncrypt_socket))
(expandtypeattribute (uncrypt_socket_28_0) true)
(typeattribute uncrypt_socket_28_0)
(typeattributeset install_recovery_28_0 (install_recovery))
(expandtypeattribute (install_recovery_28_0) true)
(typeattribute install_recovery_28_0)
(typeattributeset logpersistd_logging_prop_28_0 (logpersistd_logging_prop))
(expandtypeattribute (logpersistd_logging_prop_28_0) true)
(typeattribute logpersistd_logging_prop_28_0)
(typeattributeset hal_configstore_ISurfaceFlingerConfigs_28_0 (hal_configstore_ISurfaceFlingerConfigs))
(expandtypeattribute (hal_configstore_ISurfaceFlingerConfigs_28_0) true)
(typeattribute hal_configstore_ISurfaceFlingerConfigs_28_0)
(typeattributeset hal_ir_hwservice_28_0 (hal_ir_hwservice))
(expandtypeattribute (hal_ir_hwservice_28_0) true)
(typeattribute hal_ir_hwservice_28_0)
(typeattributeset hal_vr_hwservice_28_0 (hal_vr_hwservice))
(expandtypeattribute (hal_vr_hwservice_28_0) true)
(typeattribute hal_vr_hwservice_28_0)
(typeattributeset incident_data_file_28_0 (incident_data_file))
(expandtypeattribute (incident_data_file_28_0) true)
(typeattribute incident_data_file_28_0)
(typeattributeset webview_zygote_28_0 (webview_zygote))
(expandtypeattribute (webview_zygote_28_0) true)
(typeattribute webview_zygote_28_0)
(typeattributeset proc_uid_procstat_set_28_0 (proc_uid_procstat_set))
(expandtypeattribute (proc_uid_procstat_set_28_0) true)
(typeattribute proc_uid_procstat_set_28_0)
(typeattributeset vendor_init_28_0 (vendor_init))
(expandtypeattribute (vendor_init_28_0) true)
(typeattribute vendor_init_28_0)
(typeattributeset connectivity_service_28_0 (connectivity_service))
(expandtypeattribute (connectivity_service_28_0) true)
(typeattribute connectivity_service_28_0)
(typeattributeset notification_service_28_0 (notification_service))
(expandtypeattribute (notification_service_28_0) true)
(typeattribute notification_service_28_0)
(typeattributeset init_28_0 (init))
(expandtypeattribute (init_28_0) true)
(typeattribute init_28_0)
(typeattributeset logpersist_28_0 (logpersist))
(expandtypeattribute (logpersist_28_0) true)
(typeattribute logpersist_28_0)
(typeattributeset dreams_service_28_0 (dreams_service))
(expandtypeattribute (dreams_service_28_0) true)
(typeattribute dreams_service_28_0)
(typeattributeset companion_device_service_28_0 (companion_device_service))
(expandtypeattribute (companion_device_service_28_0) true)
(typeattribute companion_device_service_28_0)
(typeattributeset bootanim_28_0 (bootanim))
(expandtypeattribute (bootanim_28_0) true)
(typeattribute bootanim_28_0)
(typeattributeset proc_uptime_28_0 (proc_uptime))
(expandtypeattribute (proc_uptime_28_0) true)
(typeattribute proc_uptime_28_0)
(typeattributeset ota_package_file_28_0 (ota_package_file))
(expandtypeattribute (ota_package_file_28_0) true)
(typeattribute ota_package_file_28_0)
(typeattributeset diskstats_service_28_0 (diskstats_service))
(expandtypeattribute (diskstats_service_28_0) true)
(typeattribute diskstats_service_28_0)
(typeattributeset wallpaper_service_28_0 (wallpaper_service))
(expandtypeattribute (wallpaper_service_28_0) true)
(typeattribute wallpaper_service_28_0)
(typeattributeset fscklogs_28_0 (fscklogs))
(expandtypeattribute (fscklogs_28_0) true)
(typeattribute fscklogs_28_0)
(typeattributeset task_service_28_0 (task_service))
(expandtypeattribute (task_service_28_0) true)
(typeattribute task_service_28_0)
(typeattributeset hardware_service_28_0 (hardware_service))
(expandtypeattribute (hardware_service_28_0) true)
(typeattribute hardware_service_28_0)
(typeattributeset logd_28_0 (logd))
(expandtypeattribute (logd_28_0) true)
(typeattribute logd_28_0)
(typeattributeset procstats_service_28_0 (procstats_service))
(expandtypeattribute (procstats_service_28_0) true)
(typeattribute procstats_service_28_0)
(typeattributeset dumpstate_service_28_0 (dumpstate_service))
(expandtypeattribute (dumpstate_service_28_0) true)
(typeattribute dumpstate_service_28_0)
(typeattributeset fingerprintd_exec_28_0 (fingerprintd_exec))
(expandtypeattribute (fingerprintd_exec_28_0) true)
(typeattribute fingerprintd_exec_28_0)
(typeattributeset alarm_service_28_0 (alarm_service))
(expandtypeattribute (alarm_service_28_0) true)
(typeattribute alarm_service_28_0)
(typeattributeset rttmanager_service_28_0 (rttmanager_service))
(expandtypeattribute (rttmanager_service_28_0) true)
(typeattribute rttmanager_service_28_0)
(typeattributeset fwk_sensor_hwservice_28_0 (fwk_sensor_hwservice))
(expandtypeattribute (fwk_sensor_hwservice_28_0) true)
(typeattribute fwk_sensor_hwservice_28_0)
(typeattributeset ueventd_28_0 (ueventd))
(expandtypeattribute (ueventd_28_0) true)
(typeattribute ueventd_28_0)
(typeattributeset node_28_0 (node))
(expandtypeattribute (node_28_0) true)
(typeattribute node_28_0)
(typeattributeset vold_prepare_subdirs_exec_28_0 (vold_prepare_subdirs_exec))
(expandtypeattribute (vold_prepare_subdirs_exec_28_0) true)
(typeattribute vold_prepare_subdirs_exec_28_0)
(typeattributeset sysfs_dm_28_0 (sysfs_dm))
(expandtypeattribute (sysfs_dm_28_0) true)
(typeattribute sysfs_dm_28_0)
(typeattributeset lowpan_service_28_0 (lowpan_service))
(expandtypeattribute (lowpan_service_28_0) true)
(typeattribute lowpan_service_28_0)
(typeattributeset nfc_data_file_28_0 (nfc_data_file))
(expandtypeattribute (nfc_data_file_28_0) true)
(typeattribute nfc_data_file_28_0)
(typeattributeset misc_logd_file_28_0 (misc_logd_file))
(expandtypeattribute (misc_logd_file_28_0) true)
(typeattribute misc_logd_file_28_0)
(typeattributeset sepolicy_file_28_0 (sepolicy_file))
(expandtypeattribute (sepolicy_file_28_0) true)
(typeattribute sepolicy_file_28_0)
(typeattributeset audioserver_service_28_0 (audioserver_service))
(expandtypeattribute (audioserver_service_28_0) true)
(typeattribute audioserver_service_28_0)
(typeattributeset proc_sched_28_0 (proc_sched))
(expandtypeattribute (proc_sched_28_0) true)
(typeattribute proc_sched_28_0)
(typeattributeset sysfs_mac_address_28_0 (sysfs_mac_address))
(expandtypeattribute (sysfs_mac_address_28_0) true)
(typeattribute sysfs_mac_address_28_0)
(typeattributeset modprobe_28_0 (modprobe))
(expandtypeattribute (modprobe_28_0) true)
(typeattribute modprobe_28_0)
(typeattributeset incidentd_28_0 (incidentd))
(expandtypeattribute (incidentd_28_0) true)
(typeattribute incidentd_28_0)
(typeattributeset apk_tmp_file_28_0 (apk_tmp_file))
(expandtypeattribute (apk_tmp_file_28_0) true)
(typeattribute apk_tmp_file_28_0)
(typeattributeset gpu_device_28_0 (gpu_device))
(expandtypeattribute (gpu_device_28_0) true)
(typeattribute gpu_device_28_0)
(typeattributeset mdnsd_28_0 (mdnsd))
(expandtypeattribute (mdnsd_28_0) true)
(typeattribute mdnsd_28_0)
(typeattributeset proc_uid_io_stats_28_0 (proc_uid_io_stats))
(expandtypeattribute (proc_uid_io_stats_28_0) true)
(typeattribute proc_uid_io_stats_28_0)
(typeattributeset sensorservice_service_28_0 (sensorservice_service))
(expandtypeattribute (sensorservice_service_28_0) true)
(typeattribute sensorservice_service_28_0)
(typeattributeset runas_exec_28_0 (runas_exec))
(expandtypeattribute (runas_exec_28_0) true)
(typeattribute runas_exec_28_0)
(typeattributeset dex2oat_28_0 (dex2oat))
(expandtypeattribute (dex2oat_28_0) true)
(typeattribute dex2oat_28_0)
(typeattributeset wifiaware_service_28_0 (wifiaware_service))
(expandtypeattribute (wifiaware_service_28_0) true)
(typeattribute wifiaware_service_28_0)
(typeattributeset netstats_service_28_0 (netstats_service))
(expandtypeattribute (netstats_service_28_0) true)
(typeattribute netstats_service_28_0)
(typeattributeset vr_manager_service_28_0 (vr_manager_service))
(expandtypeattribute (vr_manager_service_28_0) true)
(typeattribute vr_manager_service_28_0)
(typeattributeset watchdogd_28_0 (watchdogd))
(expandtypeattribute (watchdogd_28_0) true)
(typeattribute watchdogd_28_0)
(typeattributeset vendor_security_patch_level_prop_28_0 (vendor_security_patch_level_prop))
(expandtypeattribute (vendor_security_patch_level_prop_28_0) true)
(typeattribute vendor_security_patch_level_prop_28_0)
(typeattributeset mediacodec_exec_28_0 (mediacodec_exec))
(expandtypeattribute (mediacodec_exec_28_0) true)
(typeattribute mediacodec_exec_28_0)
(typeattributeset tzdatacheck_exec_28_0 (tzdatacheck_exec))
(expandtypeattribute (tzdatacheck_exec_28_0) true)
(typeattribute tzdatacheck_exec_28_0)
(typeattributeset shell_exec_28_0 (shell_exec))
(expandtypeattribute (shell_exec_28_0) true)
(typeattribute shell_exec_28_0)
(typeattributeset hdmi_control_service_28_0 (hdmi_control_service))
(expandtypeattribute (hdmi_control_service_28_0) true)
(typeattribute hdmi_control_service_28_0)
(typeattributeset clipboard_service_28_0 (clipboard_service))
(expandtypeattribute (clipboard_service_28_0) true)
(typeattribute clipboard_service_28_0)
(typeattributeset dumpstate_exec_28_0 (dumpstate_exec))
(expandtypeattribute (dumpstate_exec_28_0) true)
(typeattribute dumpstate_exec_28_0)
(typeattributeset perfprofd_28_0 (perfprofd))
(expandtypeattribute (perfprofd_28_0) true)
(typeattribute perfprofd_28_0)
(typeattributeset netutils_wrapper_exec_28_0 (netutils_wrapper_exec))
(expandtypeattribute (netutils_wrapper_exec_28_0) true)
(typeattribute netutils_wrapper_exec_28_0)
(typeattributeset proc_max_map_count_28_0 (proc_max_map_count))
(expandtypeattribute (proc_max_map_count_28_0) true)
(typeattribute proc_max_map_count_28_0)
(typeattributeset tombstoned_intercept_socket_28_0 (tombstoned_intercept_socket))
(expandtypeattribute (tombstoned_intercept_socket_28_0) true)
(typeattribute tombstoned_intercept_socket_28_0)
(typeattributeset wificond_28_0 (wificond))
(expandtypeattribute (wificond_28_0) true)
(typeattribute wificond_28_0)
(typeattributeset debugfs_mmc_28_0 (debugfs_mmc))
(expandtypeattribute (debugfs_mmc_28_0) true)
(typeattribute debugfs_mmc_28_0)
(typeattributeset netutils_wrapper_28_0 (netutils_wrapper))
(expandtypeattribute (netutils_wrapper_28_0) true)
(typeattribute netutils_wrapper_28_0)
(typeattributeset exported_dalvik_prop_28_0 (exported_dalvik_prop))
(expandtypeattribute (exported_dalvik_prop_28_0) true)
(typeattribute exported_dalvik_prop_28_0)
(typeattributeset racoon_28_0 (racoon))
(expandtypeattribute (racoon_28_0) true)
(typeattribute racoon_28_0)
(typeattributeset wpantund_exec_28_0 (wpantund_exec))
(expandtypeattribute (wpantund_exec_28_0) true)
(typeattribute wpantund_exec_28_0)
(typeattributeset hal_cas_hwservice_28_0 (hal_cas_hwservice))
(expandtypeattribute (hal_cas_hwservice_28_0) true)
(typeattribute hal_cas_hwservice_28_0)
(typeattributeset hal_evs_hwservice_28_0 (hal_evs_hwservice))
(expandtypeattribute (hal_evs_hwservice_28_0) true)
(typeattribute hal_evs_hwservice_28_0)
(typeattributeset hal_nfc_hwservice_28_0 (hal_nfc_hwservice))
(expandtypeattribute (hal_nfc_hwservice_28_0) true)
(typeattribute hal_nfc_hwservice_28_0)
(typeattributeset vold_28_0 (vold))
(expandtypeattribute (vold_28_0) true)
(typeattribute vold_28_0)
(typeattributeset iio_device_28_0 (iio_device))
(expandtypeattribute (iio_device_28_0) true)
(typeattribute iio_device_28_0)
(typeattributeset pdx_display_manager_endpoint_socket_28_0 (pdx_display_manager_endpoint_socket))
(expandtypeattribute (pdx_display_manager_endpoint_socket_28_0) true)
(typeattribute pdx_display_manager_endpoint_socket_28_0)
(typeattributeset ion_device_28_0 (ion_device))
(expandtypeattribute (ion_device_28_0) true)
(typeattribute ion_device_28_0)
(typeattributeset hal_secure_element_hwservice_28_0 (hal_secure_element_hwservice))
(expandtypeattribute (hal_secure_element_hwservice_28_0) true)
(typeattribute hal_secure_element_hwservice_28_0)
(typeattributeset port_device_28_0 (port_device))
(expandtypeattribute (port_device_28_0) true)
(typeattribute port_device_28_0)
(typeattributeset nfc_device_28_0 (nfc_device))
(expandtypeattribute (nfc_device_28_0) true)
(typeattribute nfc_device_28_0)
(typeattributeset rild_socket_28_0 (rild_socket))
(expandtypeattribute (rild_socket_28_0) true)
(typeattribute rild_socket_28_0)
(typeattributeset keystore_data_file_28_0 (keystore_data_file))
(expandtypeattribute (keystore_data_file_28_0) true)
(typeattribute keystore_data_file_28_0)
(typeattributeset recovery_persist_exec_28_0 (recovery_persist_exec))
(expandtypeattribute (recovery_persist_exec_28_0) true)
(typeattribute recovery_persist_exec_28_0)
(typeattributeset pmsg_device_28_0 (pmsg_device))
(expandtypeattribute (pmsg_device_28_0) true)
(typeattribute pmsg_device_28_0)
(typeattributeset rpmsg_device_28_0 (rpmsg_device))
(expandtypeattribute (rpmsg_device_28_0) true)
(typeattribute rpmsg_device_28_0)
(typeattributeset i2c_device_28_0 (i2c_device))
(expandtypeattribute (i2c_device_28_0) true)
(typeattribute i2c_device_28_0)
(typeattributeset cache_file_28_0 (cache_file))
(expandtypeattribute (cache_file_28_0) true)
(typeattribute cache_file_28_0)
(typeattributeset fingerprintd_service_28_0 (fingerprintd_service))
(expandtypeattribute (fingerprintd_service_28_0) true)
(typeattribute fingerprintd_service_28_0)
(typeattributeset mtp_device_28_0 (mtp_device))
(expandtypeattribute (mtp_device_28_0) true)
(typeattribute mtp_device_28_0)
(typeattributeset netd_stable_secret_prop_28_0 (netd_stable_secret_prop))
(expandtypeattribute (netd_stable_secret_prop_28_0) true)
(typeattribute netd_stable_secret_prop_28_0)
(typeattributeset mtd_device_28_0 (mtd_device))
(expandtypeattribute (mtd_device_28_0) true)
(typeattribute mtd_device_28_0)
(typeattributeset adbd_socket_28_0 (adbd_socket))
(expandtypeattribute (adbd_socket_28_0) true)
(typeattribute adbd_socket_28_0)
(typeattributeset debugfs_trace_marker_28_0 (debugfs_trace_marker))
(expandtypeattribute (debugfs_trace_marker_28_0) true)
(typeattribute debugfs_trace_marker_28_0)
(typeattributeset otapreopt_chroot_28_0 (otapreopt_chroot))
(expandtypeattribute (otapreopt_chroot_28_0) true)
(typeattribute otapreopt_chroot_28_0)
(typeattributeset hal_renderscript_hwservice_28_0 (hal_renderscript_hwservice))
(expandtypeattribute (hal_renderscript_hwservice_28_0) true)
(typeattribute hal_renderscript_hwservice_28_0)
(typeattributeset cppreopts_exec_28_0 (cppreopts_exec))
(expandtypeattribute (cppreopts_exec_28_0) true)
(typeattribute cppreopts_exec_28_0)
(typeattributeset installd_exec_28_0 (installd_exec))
(expandtypeattribute (installd_exec_28_0) true)
(typeattribute installd_exec_28_0)
(typeattributeset tombstoned_exec_28_0 (tombstoned_exec))
(expandtypeattribute (tombstoned_exec_28_0) true)
(typeattribute tombstoned_exec_28_0)
(typeattributeset runtime_event_log_tags_file_28_0 (runtime_event_log_tags_file))
(expandtypeattribute (runtime_event_log_tags_file_28_0) true)
(typeattribute runtime_event_log_tags_file_28_0)
(typeattributeset proc_kmsg_28_0 (proc_kmsg))
(expandtypeattribute (proc_kmsg_28_0) true)
(typeattribute proc_kmsg_28_0)
(typeattributeset wpa_socket_28_0 (wpa_socket))
(expandtypeattribute (wpa_socket_28_0) true)
(typeattribute wpa_socket_28_0)
(typeattributeset mtp_28_0 (mtp))
(expandtypeattribute (mtp_28_0) true)
(typeattribute mtp_28_0)
(typeattributeset backup_data_file_28_0 (backup_data_file))
(expandtypeattribute (backup_data_file_28_0) true)
(typeattribute backup_data_file_28_0)
(typeattributeset app_fuse_file_28_0 (app_fuse_file))
(expandtypeattribute (app_fuse_file_28_0) true)
(typeattribute app_fuse_file_28_0)
(typeattributeset app_data_file_28_0 (app_data_file))
(expandtypeattribute (app_data_file_28_0) true)
(typeattribute app_data_file_28_0)
(typeattributeset dhcp_data_file_28_0 (dhcp_data_file))
(expandtypeattribute (dhcp_data_file_28_0) true)
(typeattribute dhcp_data_file_28_0)
(typeattributeset racoon_exec_28_0 (racoon_exec))
(expandtypeattribute (racoon_exec_28_0) true)
(typeattribute racoon_exec_28_0)
(typeattributeset unlabeled_28_0 (unlabeled))
(expandtypeattribute (unlabeled_28_0) true)
(typeattribute unlabeled_28_0)
(typeattributeset binder_calls_stats_service_28_0 (binder_calls_stats_service))
(expandtypeattribute (binder_calls_stats_service_28_0) true)
(typeattribute binder_calls_stats_service_28_0)
(typeattributeset ipsec_service_28_0 (ipsec_service))
(expandtypeattribute (ipsec_service_28_0) true)
(typeattribute ipsec_service_28_0)
(typeattributeset user_service_28_0 (user_service))
(expandtypeattribute (user_service_28_0) true)
(typeattribute user_service_28_0)
(typeattributeset persistent_data_block_service_28_0 (persistent_data_block_service))
(expandtypeattribute (persistent_data_block_service_28_0) true)
(typeattribute persistent_data_block_service_28_0)
(typeattributeset profman_dump_data_file_28_0 (profman_dump_data_file))
(expandtypeattribute (profman_dump_data_file_28_0) true)
(typeattribute profman_dump_data_file_28_0)
(typeattributeset socket_device_28_0 (socket_device))
(expandtypeattribute (socket_device_28_0) true)
(typeattribute socket_device_28_0)
(typeattributeset broadcastradio_service_28_0 (broadcastradio_service))
(expandtypeattribute (broadcastradio_service_28_0) true)
(typeattribute broadcastradio_service_28_0)
(typeattributeset scheduling_policy_service_28_0 (scheduling_policy_service))
(expandtypeattribute (scheduling_policy_service_28_0) true)
(typeattribute scheduling_policy_service_28_0)
(typeattributeset update_engine_exec_28_0 (update_engine_exec))
(expandtypeattribute (update_engine_exec_28_0) true)
(typeattribute update_engine_exec_28_0)
(typeattributeset installd_28_0 (installd))
(expandtypeattribute (installd_28_0) true)
(typeattribute installd_28_0)
(typeattributeset profman_exec_28_0 (profman_exec))
(expandtypeattribute (profman_exec_28_0) true)
(typeattribute profman_exec_28_0)
(typeattributeset bootchart_data_file_28_0 (bootchart_data_file))
(expandtypeattribute (bootchart_data_file_28_0) true)
(typeattribute bootchart_data_file_28_0)
(typeattributeset persist_debug_prop_28_0 (persist_debug_prop))
(expandtypeattribute (persist_debug_prop_28_0) true)
(typeattribute persist_debug_prop_28_0)
(typeattributeset telecom_service_28_0 (telecom_service))
(expandtypeattribute (telecom_service_28_0) true)
(typeattribute telecom_service_28_0)
(typeattributeset audioserver_data_file_28_0 (audioserver_data_file))
(expandtypeattribute (audioserver_data_file_28_0) true)
(typeattribute audioserver_data_file_28_0)
(typeattributeset console_device_28_0 (console_device))
(expandtypeattribute (console_device_28_0) true)
(typeattribute console_device_28_0)
(typeattributeset sensors_device_28_0 (sensors_device))
(expandtypeattribute (sensors_device_28_0) true)
(typeattribute sensors_device_28_0)
(typeattributeset vold_metadata_file_28_0 (vold_metadata_file))
(expandtypeattribute (vold_metadata_file_28_0) true)
(typeattribute vold_metadata_file_28_0)
(typeattributeset nonplat_service_contexts_file_28_0 (nonplat_service_contexts_file))
(expandtypeattribute (nonplat_service_contexts_file_28_0) true)
(typeattribute nonplat_service_contexts_file_28_0)
(typeattributeset samplingprofiler_service_28_0 (samplingprofiler_service))
(expandtypeattribute (samplingprofiler_service_28_0) true)
(typeattribute samplingprofiler_service_28_0)
(typeattributeset hal_graphics_allocator_hwservice_28_0 (hal_graphics_allocator_hwservice))
(expandtypeattribute (hal_graphics_allocator_hwservice_28_0) true)
(typeattribute hal_graphics_allocator_hwservice_28_0)
(typeattributeset proc_version_28_0 (proc_version))
(expandtypeattribute (proc_version_28_0) true)
(typeattribute proc_version_28_0)
(typeattributeset search_service_28_0 (search_service))
(expandtypeattribute (search_service_28_0) true)
(typeattribute search_service_28_0)
(typeattributeset mediaserver_28_0 (mediaserver))
(expandtypeattribute (mediaserver_28_0) true)
(typeattribute mediaserver_28_0)
(typeattributeset mediaserver_exec_28_0 (mediaserver_exec))
(expandtypeattribute (mediaserver_exec_28_0) true)
(typeattribute mediaserver_exec_28_0)
(typeattributeset oemfs_28_0 (oemfs))
(expandtypeattribute (oemfs_28_0) true)
(typeattribute oemfs_28_0)
(typeattributeset proc_cmdline_28_0 (proc_cmdline))
(expandtypeattribute (proc_cmdline_28_0) true)
(typeattribute proc_cmdline_28_0)
(typeattributeset drmserver_exec_28_0 (drmserver_exec))
(expandtypeattribute (drmserver_exec_28_0) true)
(typeattribute drmserver_exec_28_0)
(typeattributeset sgdisk_exec_28_0 (sgdisk_exec))
(expandtypeattribute (sgdisk_exec_28_0) true)
(typeattribute sgdisk_exec_28_0)
(typeattributeset pdx_display_screenshot_endpoint_socket_28_0 (pdx_display_screenshot_endpoint_socket))
(expandtypeattribute (pdx_display_screenshot_endpoint_socket_28_0) true)
(typeattribute pdx_display_screenshot_endpoint_socket_28_0)
(typeattributeset camera_data_file_28_0 (camera_data_file))
(expandtypeattribute (camera_data_file_28_0) true)
(typeattribute camera_data_file_28_0)
(typeattributeset bluetooth_efs_file_28_0 (bluetooth_efs_file))
(expandtypeattribute (bluetooth_efs_file_28_0) true)
(typeattribute bluetooth_efs_file_28_0)
(typeattributeset media_data_file_28_0 (media_data_file))
(expandtypeattribute (media_data_file_28_0) true)
(typeattribute media_data_file_28_0)
(typeattributeset ota_data_file_28_0 (ota_data_file))
(expandtypeattribute (ota_data_file_28_0) true)
(typeattribute ota_data_file_28_0)
(typeattributeset system_file_28_0 (system_file))
(expandtypeattribute (system_file_28_0) true)
(typeattribute system_file_28_0)
(typeattributeset apk_private_tmp_file_28_0 (apk_private_tmp_file))
(expandtypeattribute (apk_private_tmp_file_28_0) true)
(typeattribute apk_private_tmp_file_28_0)
(typeattributeset hci_attach_dev_28_0 (hci_attach_dev))
(expandtypeattribute (hci_attach_dev_28_0) true)
(typeattribute hci_attach_dev_28_0)
(typeattributeset statusbar_service_28_0 (statusbar_service))
(expandtypeattribute (statusbar_service_28_0) true)
(typeattribute statusbar_service_28_0)
(typeattributeset traced_producer_socket_28_0 (traced_producer_socket))
(expandtypeattribute (traced_producer_socket_28_0) true)
(typeattribute traced_producer_socket_28_0)
(typeattributeset idmap_28_0 (idmap))
(expandtypeattribute (idmap_28_0) true)
(typeattribute idmap_28_0)
(typeattributeset fwmarkd_socket_28_0 (fwmarkd_socket))
(expandtypeattribute (fwmarkd_socket_28_0) true)
(typeattribute fwmarkd_socket_28_0)
(typeattributeset exported_system_radio_prop_28_0 (exported_system_radio_prop))
(expandtypeattribute (exported_system_radio_prop_28_0) true)
(typeattribute exported_system_radio_prop_28_0)
(typeattributeset cameraserver_exec_28_0 (cameraserver_exec))
(expandtypeattribute (cameraserver_exec_28_0) true)
(typeattribute cameraserver_exec_28_0)
(typeattributeset shortcut_service_28_0 (shortcut_service))
(expandtypeattribute (shortcut_service_28_0) true)
(typeattribute shortcut_service_28_0)
(typeattributeset profman_28_0 (profman))
(expandtypeattribute (profman_28_0) true)
(typeattribute profman_28_0)
(typeattributeset ctl_sigstop_prop_28_0 (ctl_sigstop_prop))
(expandtypeattribute (ctl_sigstop_prop_28_0) true)
(typeattribute ctl_sigstop_prop_28_0)
(typeattributeset media_rw_data_file_28_0 (media_rw_data_file))
(expandtypeattribute (media_rw_data_file_28_0) true)
(typeattribute media_rw_data_file_28_0)
(typeattributeset coredump_file_28_0 (coredump_file))
(expandtypeattribute (coredump_file_28_0) true)
(typeattribute coredump_file_28_0)
(typeattributeset ctl_interface_stop_prop_28_0 (ctl_interface_stop_prop))
(expandtypeattribute (ctl_interface_stop_prop_28_0) true)
(typeattribute ctl_interface_stop_prop_28_0)
(typeattributeset serial_device_28_0 (serial_device))
(expandtypeattribute (serial_device_28_0) true)
(typeattribute serial_device_28_0)
(typeattributeset traced_consumer_socket_28_0 (traced_consumer_socket))
(expandtypeattribute (traced_consumer_socket_28_0) true)
(typeattribute traced_consumer_socket_28_0)
(typeattributeset devicestoragemonitor_service_28_0 (devicestoragemonitor_service))
(expandtypeattribute (devicestoragemonitor_service_28_0) true)
(typeattribute devicestoragemonitor_service_28_0)
(typeattributeset boottrace_data_file_28_0 (boottrace_data_file))
(expandtypeattribute (boottrace_data_file_28_0) true)
(typeattribute boottrace_data_file_28_0)
(typeattributeset proc_uid_time_in_state_28_0 (proc_uid_time_in_state))
(expandtypeattribute (proc_uid_time_in_state_28_0) true)
(typeattribute proc_uid_time_in_state_28_0)
(typeattributeset hal_audio_hwservice_28_0 (hal_audio_hwservice))
(expandtypeattribute (hal_audio_hwservice_28_0) true)
(typeattribute hal_audio_hwservice_28_0)
(typeattributeset cache_backup_file_28_0 (cache_backup_file))
(expandtypeattribute (cache_backup_file_28_0) true)
(typeattribute cache_backup_file_28_0)
(typeattributeset hal_usb_gadget_hwservice_28_0 (hal_usb_gadget_hwservice))
(expandtypeattribute (hal_usb_gadget_hwservice_28_0) true)
(typeattribute hal_usb_gadget_hwservice_28_0)
(typeattributeset mediacodec_service_28_0 (mediacodec_service))
(expandtypeattribute (mediacodec_service_28_0) true)
(typeattribute mediacodec_service_28_0)
(typeattributeset lmkd_28_0 (lmkd))
(expandtypeattribute (lmkd_28_0) true)
(typeattribute lmkd_28_0)
(typeattributeset deviceidle_service_28_0 (deviceidle_service))
(expandtypeattribute (deviceidle_service_28_0) true)
(typeattribute deviceidle_service_28_0)
(typeattributeset dropbox_service_28_0 (dropbox_service))
(expandtypeattribute (dropbox_service_28_0) true)
(typeattribute dropbox_service_28_0)
(typeattributeset hidl_token_hwservice_28_0 (hidl_token_hwservice))
(expandtypeattribute (hidl_token_hwservice_28_0) true)
(typeattribute hidl_token_hwservice_28_0)
(typeattributeset storagestats_service_28_0 (storagestats_service))
(expandtypeattribute (storagestats_service_28_0) true)
(typeattribute storagestats_service_28_0)
(typeattributeset thermalcallback_hwservice_28_0 (thermalcallback_hwservice))
(expandtypeattribute (thermalcallback_hwservice_28_0) true)
(typeattribute thermalcallback_hwservice_28_0)
(typeattributeset wifip2p_service_28_0 (wifip2p_service))
(expandtypeattribute (wifip2p_service_28_0) true)
(typeattribute wifip2p_service_28_0)
(typeattributeset sysfs_switch_28_0 (sysfs_switch))
(expandtypeattribute (sysfs_switch_28_0) true)
(typeattribute sysfs_switch_28_0)
(typeattributeset registry_service_28_0 (registry_service))
(expandtypeattribute (registry_service_28_0) true)
(typeattribute registry_service_28_0)
(typeattributeset platform_app_28_0 (platform_app))
(expandtypeattribute (platform_app_28_0) true)
(typeattribute platform_app_28_0)
(typeattributeset cpuctl_device_28_0 (cpuctl_device))
(expandtypeattribute (cpuctl_device_28_0) true)
(typeattribute cpuctl_device_28_0)
(typeattributeset sysfs_batteryinfo_28_0 (sysfs_batteryinfo))
(expandtypeattribute (sysfs_batteryinfo_28_0) true)
(typeattribute sysfs_batteryinfo_28_0)
(typeattributeset recovery_persist_28_0 (recovery_persist))
(expandtypeattribute (recovery_persist_28_0) true)
(typeattribute recovery_persist_28_0)
(typeattributeset package_native_service_28_0 (package_native_service))
(expandtypeattribute (package_native_service_28_0) true)
(typeattribute package_native_service_28_0)
(typeattributeset jobscheduler_service_28_0 (jobscheduler_service))
(expandtypeattribute (jobscheduler_service_28_0) true)
(typeattribute jobscheduler_service_28_0)
(typeattributeset proc_iomem_28_0 (proc_iomem))
(expandtypeattribute (proc_iomem_28_0) true)
(typeattribute proc_iomem_28_0)
(typeattributeset hal_camera_hwservice_28_0 (hal_camera_hwservice))
(expandtypeattribute (hal_camera_hwservice_28_0) true)
(typeattribute hal_camera_hwservice_28_0)
(typeattributeset proc_timer_28_0 (proc_timer))
(expandtypeattribute (proc_timer_28_0) true)
(typeattribute proc_timer_28_0)
(typeattributeset pdx_performance_client_channel_socket_28_0 (pdx_performance_client_channel_socket))
(expandtypeattribute (pdx_performance_client_channel_socket_28_0) true)
(typeattribute pdx_performance_client_channel_socket_28_0)
(typeattributeset sdcardd_exec_28_0 (sdcardd_exec))
(expandtypeattribute (sdcardd_exec_28_0) true)
(typeattribute sdcardd_exec_28_0)
(typeattributeset kmsg_debug_device_28_0 (kmsg_debug_device))
(expandtypeattribute (kmsg_debug_device_28_0) true)
(typeattribute kmsg_debug_device_28_0)
(typeattributeset mediametrics_28_0 (mediametrics))
(expandtypeattribute (mediametrics_28_0) true)
(typeattribute mediametrics_28_0)
(typeattributeset mediametrics_exec_28_0 (mediametrics_exec))
(expandtypeattribute (mediametrics_exec_28_0) true)
(typeattribute mediametrics_exec_28_0)
(typeattributeset audio_device_28_0 (audio_device))
(expandtypeattribute (audio_device_28_0) true)
(typeattribute audio_device_28_0)
(typeattributeset webviewupdate_service_28_0 (webviewupdate_service))
(expandtypeattribute (webviewupdate_service_28_0) true)
(typeattribute webviewupdate_service_28_0)
(typeattributeset bufferhubd_exec_28_0 (bufferhubd_exec))
(expandtypeattribute (bufferhubd_exec_28_0) true)
(typeattribute bufferhubd_exec_28_0)
(typeattributeset dex2oat_exec_28_0 (dex2oat_exec))
(expandtypeattribute (dex2oat_exec_28_0) true)
(typeattribute dex2oat_exec_28_0)
(typeattributeset sysfs_lowmemorykiller_28_0 (sysfs_lowmemorykiller))
(expandtypeattribute (sysfs_lowmemorykiller_28_0) true)
(typeattribute sysfs_lowmemorykiller_28_0)
(typeattributeset hwservicemanager_exec_28_0 (hwservicemanager_exec))
(expandtypeattribute (hwservicemanager_exec_28_0) true)
(typeattribute hwservicemanager_exec_28_0)
(typeattributeset servicemanager_exec_28_0 (servicemanager_exec))
(expandtypeattribute (servicemanager_exec_28_0) true)
(typeattribute servicemanager_exec_28_0)
(typeattributeset pdx_performance_dir_28_0 (pdx_performance_dir))
(expandtypeattribute (pdx_performance_dir_28_0) true)
(typeattribute pdx_performance_dir_28_0)
(typeattributeset proc_cpuinfo_28_0 (proc_cpuinfo))
(expandtypeattribute (proc_cpuinfo_28_0) true)
(typeattribute proc_cpuinfo_28_0)
(typeattributeset proc_meminfo_28_0 (proc_meminfo))
(expandtypeattribute (proc_meminfo_28_0) true)
(typeattribute proc_meminfo_28_0)
(typeattributeset zygote_socket_28_0 (zygote_socket))
(expandtypeattribute (zygote_socket_28_0) true)
(typeattribute zygote_socket_28_0)
(typeattributeset vendor_framework_file_28_0 (vendor_framework_file))
(expandtypeattribute (vendor_framework_file_28_0) true)
(typeattribute vendor_framework_file_28_0)
(typeattributeset boottime_prop_28_0 (boottime_prop))
(expandtypeattribute (boottime_prop_28_0) true)
(typeattribute boottime_prop_28_0)
(typeattributeset system_radio_prop_28_0 (system_radio_prop))
(expandtypeattribute (system_radio_prop_28_0) true)
(typeattribute system_radio_prop_28_0)
(typeattributeset fsck_untrusted_28_0 (fsck_untrusted))
(expandtypeattribute (fsck_untrusted_28_0) true)
(typeattribute fsck_untrusted_28_0)
(typeattributeset uhid_device_28_0 (uhid_device))
(expandtypeattribute (uhid_device_28_0) true)
(typeattribute uhid_device_28_0)
(typeattributeset ctl_start_prop_28_0 (ctl_start_prop))
(expandtypeattribute (ctl_start_prop_28_0) true)
(typeattribute ctl_start_prop_28_0)
(typeattributeset incident_service_28_0 (incident_service))
(expandtypeattribute (incident_service_28_0) true)
(typeattribute incident_service_28_0)
(typeattributeset ctl_bootanim_prop_28_0 (ctl_bootanim_prop))
(expandtypeattribute (ctl_bootanim_prop_28_0) true)
(typeattribute ctl_bootanim_prop_28_0)
(typeattributeset restorecon_prop_28_0 (restorecon_prop))
(expandtypeattribute (restorecon_prop_28_0) true)
(typeattribute restorecon_prop_28_0)
(typeattributeset account_service_28_0 (account_service))
(expandtypeattribute (account_service_28_0) true)
(typeattribute account_service_28_0)
(typeattributeset content_service_28_0 (content_service))
(expandtypeattribute (content_service_28_0) true)
(typeattribute content_service_28_0)
(typeattributeset mount_service_28_0 (mount_service))
(expandtypeattribute (mount_service_28_0) true)
(typeattribute mount_service_28_0)
(typeattributeset net_radio_prop_28_0 (net_radio_prop))
(expandtypeattribute (net_radio_prop_28_0) true)
(typeattribute net_radio_prop_28_0)
(typeattributeset sysfs_usermodehelper_28_0 (sysfs_usermodehelper))
(expandtypeattribute (sysfs_usermodehelper_28_0) true)
(typeattribute sysfs_usermodehelper_28_0)
(typeattributeset asec_apk_file_28_0 (asec_apk_file))
(expandtypeattribute (asec_apk_file_28_0) true)
(typeattribute asec_apk_file_28_0)
(typeattributeset wpantund_service_28_0 (wpantund_service))
(expandtypeattribute (wpantund_service_28_0) true)
(typeattribute wpantund_service_28_0)
(typeattributeset heapdump_data_file_28_0 (heapdump_data_file))
(expandtypeattribute (heapdump_data_file_28_0) true)
(typeattribute heapdump_data_file_28_0)
(typeattributeset sysfs_dt_firmware_android_28_0 (sysfs_dt_firmware_android))
(expandtypeattribute (sysfs_dt_firmware_android_28_0) true)
(typeattribute sysfs_dt_firmware_android_28_0)
(typeattributeset hal_authsecret_hwservice_28_0 (hal_authsecret_hwservice))
(expandtypeattribute (hal_authsecret_hwservice_28_0) true)
(typeattribute hal_authsecret_hwservice_28_0)
(typeattributeset update_verifier_exec_28_0 (update_verifier_exec))
(expandtypeattribute (update_verifier_exec_28_0) true)
(typeattribute update_verifier_exec_28_0)
(typeattributeset hal_dumpstate_hwservice_28_0 (hal_dumpstate_hwservice))
(expandtypeattribute (hal_dumpstate_hwservice_28_0) true)
(typeattribute hal_dumpstate_hwservice_28_0)
(typeattributeset cache_private_backup_file_28_0 (cache_private_backup_file))
(expandtypeattribute (cache_private_backup_file_28_0) true)
(typeattribute cache_private_backup_file_28_0)
(typeattributeset usb_service_28_0 (usb_service))
(expandtypeattribute (usb_service_28_0) true)
(typeattribute usb_service_28_0)
(typeattributeset ctl_stop_prop_28_0 (ctl_stop_prop))
(expandtypeattribute (ctl_stop_prop_28_0) true)
(typeattribute ctl_stop_prop_28_0)
(typeattributeset battery_service_28_0 (battery_service))
(expandtypeattribute (battery_service_28_0) true)
(typeattribute battery_service_28_0)
(typeattributeset recovery_service_28_0 (recovery_service))
(expandtypeattribute (recovery_service_28_0) true)
(typeattribute recovery_service_28_0)
(typeattributeset ctl_fuse_prop_28_0 (ctl_fuse_prop))
(expandtypeattribute (ctl_fuse_prop_28_0) true)
(typeattribute ctl_fuse_prop_28_0)
(typeattributeset ctl_console_prop_28_0 (ctl_console_prop))
(expandtypeattribute (ctl_console_prop_28_0) true)
(typeattribute ctl_console_prop_28_0)
(typeattributeset gatekeeperd_exec_28_0 (gatekeeperd_exec))
(expandtypeattribute (gatekeeperd_exec_28_0) true)
(typeattribute gatekeeperd_exec_28_0)
(typeattributeset sockfs_28_0 (sockfs))
(expandtypeattribute (sockfs_28_0) true)
(typeattribute sockfs_28_0)
(typeattributeset trust_service_28_0 (trust_service))
(expandtypeattribute (trust_service_28_0) true)
(typeattribute trust_service_28_0)
(typeattributeset binfmt_miscfs_28_0 (binfmt_miscfs))
(expandtypeattribute (binfmt_miscfs_28_0) true)
(typeattribute binfmt_miscfs_28_0)
(typeattributeset storage_file_28_0 (storage_file))
(expandtypeattribute (storage_file_28_0) true)
(typeattribute storage_file_28_0)
(typeattributeset update_verifier_28_0 (update_verifier))
(expandtypeattribute (update_verifier_28_0) true)
(typeattribute update_verifier_28_0)
(typeattributeset ctl_mdnsd_prop_28_0 (ctl_mdnsd_prop))
(expandtypeattribute (ctl_mdnsd_prop_28_0) true)
(typeattribute ctl_mdnsd_prop_28_0)
(typeattributeset mnt_media_rw_file_28_0 (mnt_media_rw_file))
(expandtypeattribute (mnt_media_rw_file_28_0) true)
(typeattribute mnt_media_rw_file_28_0)
(typeattributeset update_engine_data_file_28_0 (update_engine_data_file))
(expandtypeattribute (update_engine_data_file_28_0) true)
(typeattribute update_engine_data_file_28_0)
(typeattributeset healthd_exec_28_0 (healthd_exec))
(expandtypeattribute (healthd_exec_28_0) true)
(typeattribute healthd_exec_28_0)
(typeattributeset mnt_expand_file_28_0 (mnt_expand_file))
(expandtypeattribute (mnt_expand_file_28_0) true)
(typeattribute mnt_expand_file_28_0)
(typeattributeset system_update_service_28_0 (system_update_service))
(expandtypeattribute (system_update_service_28_0) true)
(typeattribute system_update_service_28_0)
(typeattributeset fwk_display_hwservice_28_0 (fwk_display_hwservice))
(expandtypeattribute (fwk_display_hwservice_28_0) true)
(typeattribute fwk_display_hwservice_28_0)
(typeattributeset postinstall_dexopt_28_0 (postinstall_dexopt))
(expandtypeattribute (postinstall_dexopt_28_0) true)
(typeattribute postinstall_dexopt_28_0)
(typeattributeset tombstoned_crash_socket_28_0 (tombstoned_crash_socket))
(expandtypeattribute (tombstoned_crash_socket_28_0) true)
(typeattribute tombstoned_crash_socket_28_0)
(typeattributeset proc_drop_caches_28_0 (proc_drop_caches))
(expandtypeattribute (proc_drop_caches_28_0) true)
(typeattribute proc_drop_caches_28_0)
(typeattributeset resourcecache_data_file_28_0 (resourcecache_data_file))
(expandtypeattribute (resourcecache_data_file_28_0) true)
(typeattribute resourcecache_data_file_28_0)
(typeattributeset netd_service_28_0 (netd_service))
(expandtypeattribute (netd_service_28_0) true)
(typeattribute netd_service_28_0)
(typeattributeset fwk_scheduler_hwservice_28_0 (fwk_scheduler_hwservice))
(expandtypeattribute (fwk_scheduler_hwservice_28_0) true)
(typeattribute fwk_scheduler_hwservice_28_0)
(typeattributeset log_tag_prop_28_0 (log_tag_prop))
(expandtypeattribute (log_tag_prop_28_0) true)
(typeattribute log_tag_prop_28_0)
(typeattributeset bluetooth_a2dp_offload_prop_28_0 (bluetooth_a2dp_offload_prop))
(expandtypeattribute (bluetooth_a2dp_offload_prop_28_0) true)
(typeattribute bluetooth_a2dp_offload_prop_28_0)
(typeattributeset tombstone_data_file_28_0 (tombstone_data_file))
(expandtypeattribute (tombstone_data_file_28_0) true)
(typeattribute tombstone_data_file_28_0)
(typeattributeset audio_service_28_0 (audio_service))
(expandtypeattribute (audio_service_28_0) true)
(typeattribute audio_service_28_0)
(typeattributeset radio_service_28_0 (radio_service))
(expandtypeattribute (radio_service_28_0) true)
(typeattribute radio_service_28_0)
(typeattributeset otapreopt_chroot_exec_28_0 (otapreopt_chroot_exec))
(expandtypeattribute (otapreopt_chroot_exec_28_0) true)
(typeattribute otapreopt_chroot_exec_28_0)
(typeattributeset unencrypted_data_file_28_0 (unencrypted_data_file))
(expandtypeattribute (unencrypted_data_file_28_0) true)
(typeattribute unencrypted_data_file_28_0)
(typeattributeset otapreopt_slot_28_0 (otapreopt_slot))
(expandtypeattribute (otapreopt_slot_28_0) true)
(typeattribute otapreopt_slot_28_0)
(typeattributeset properties_device_28_0 (properties_device))
(expandtypeattribute (properties_device_28_0) true)
(typeattribute properties_device_28_0)
(typeattributeset vibrator_service_28_0 (vibrator_service))
(expandtypeattribute (vibrator_service_28_0) true)
(typeattribute vibrator_service_28_0)
(typeattributeset metadata_file_28_0 (metadata_file))
(expandtypeattribute (metadata_file_28_0) true)
(typeattribute metadata_file_28_0)
(typeattributeset window_service_28_0 (window_service))
(expandtypeattribute (window_service_28_0) true)
(typeattribute window_service_28_0)
(typeattributeset update_engine_28_0 (update_engine))
(expandtypeattribute (update_engine_28_0) true)
(typeattribute update_engine_28_0)
(typeattributeset mediaextractor_28_0 (mediaextractor))
(expandtypeattribute (mediaextractor_28_0) true)
(typeattribute mediaextractor_28_0)
(typeattributeset blkid_28_0 (blkid))
(expandtypeattribute (blkid_28_0) true)
(typeattribute blkid_28_0)
(typeattributeset properties_serial_28_0 (properties_serial))
(expandtypeattribute (properties_serial_28_0) true)
(typeattribute properties_serial_28_0)
(typeattributeset functionfs_28_0 (functionfs))
(expandtypeattribute (functionfs_28_0) true)
(typeattribute functionfs_28_0)
(typeattributeset lowpan_prop_28_0 (lowpan_prop))
(expandtypeattribute (lowpan_prop_28_0) true)
(typeattribute lowpan_prop_28_0)
(typeattributeset rild_debug_socket_28_0 (rild_debug_socket))
(expandtypeattribute (rild_debug_socket_28_0) true)
(typeattribute rild_debug_socket_28_0)
(typeattributeset surfaceflinger_service_28_0 (surfaceflinger_service))
(expandtypeattribute (surfaceflinger_service_28_0) true)
(typeattribute surfaceflinger_service_28_0)
(typeattributeset appwidget_service_28_0 (appwidget_service))
(expandtypeattribute (appwidget_service_28_0) true)
(typeattribute appwidget_service_28_0)
(typeattributeset gatekeeper_data_file_28_0 (gatekeeper_data_file))
(expandtypeattribute (gatekeeper_data_file_28_0) true)
(typeattribute gatekeeper_data_file_28_0)
(typeattributeset launcherapps_service_28_0 (launcherapps_service))
(expandtypeattribute (launcherapps_service_28_0) true)
(typeattribute launcherapps_service_28_0)
(typeattributeset proc_misc_28_0 (proc_misc))
(expandtypeattribute (proc_misc_28_0) true)
(typeattribute proc_misc_28_0)
(typeattributeset mnt_user_file_28_0 (mnt_user_file))
(expandtypeattribute (mnt_user_file_28_0) true)
(typeattribute mnt_user_file_28_0)
(typeattributeset exported_radio_prop_28_0 (exported_radio_prop))
(expandtypeattribute (exported_radio_prop_28_0) true)
(typeattribute exported_radio_prop_28_0)
(typeattributeset su_exec_28_0 (su_exec))
(expandtypeattribute (su_exec_28_0) true)
(typeattribute su_exec_28_0)
(typeattributeset ppp_exec_28_0 (ppp_exec))
(expandtypeattribute (ppp_exec_28_0) true)
(typeattribute ppp_exec_28_0)
(typeattributeset vdc_exec_28_0 (vdc_exec))
(expandtypeattribute (vdc_exec_28_0) true)
(typeattribute vdc_exec_28_0)
(typeattributeset mtp_exec_28_0 (mtp_exec))
(expandtypeattribute (mtp_exec_28_0) true)
(typeattribute mtp_exec_28_0)
(typeattributeset net_data_file_28_0 (net_data_file))
(expandtypeattribute (net_data_file_28_0) true)
(typeattribute net_data_file_28_0)
(typeattributeset vold_data_file_28_0 (vold_data_file))
(expandtypeattribute (vold_data_file_28_0) true)
(typeattribute vold_data_file_28_0)
(typeattributeset dhcp_28_0 (dhcp))
(expandtypeattribute (dhcp_28_0) true)
(typeattribute dhcp_28_0)
(typeattributeset usbd_exec_28_0 (usbd_exec))
(expandtypeattribute (usbd_exec_28_0) true)
(typeattribute usbd_exec_28_0)
(typeattributeset preloads_data_file_28_0 (preloads_data_file))
(expandtypeattribute (preloads_data_file_28_0) true)
(typeattribute preloads_data_file_28_0)
(typeattributeset vold_exec_28_0 (vold_exec))
(expandtypeattribute (vold_exec_28_0) true)
(typeattribute vold_exec_28_0)
(typeattributeset usagestats_service_28_0 (usagestats_service))
(expandtypeattribute (usagestats_service_28_0) true)
(typeattribute usagestats_service_28_0)
(typeattributeset proc_uid_cpupower_28_0 (proc_uid_cpupower))
(expandtypeattribute (proc_uid_cpupower_28_0) true)
(typeattribute proc_uid_cpupower_28_0)
(typeattributeset thermalserviced_28_0 (thermalserviced))
(expandtypeattribute (thermalserviced_28_0) true)
(typeattribute thermalserviced_28_0)
(typeattributeset dhcp_exec_28_0 (dhcp_exec))
(expandtypeattribute (dhcp_exec_28_0) true)
(typeattribute dhcp_exec_28_0)
(typeattributeset fsck_exec_28_0 (fsck_exec))
(expandtypeattribute (fsck_exec_28_0) true)
(typeattribute fsck_exec_28_0)
(typeattributeset hal_tv_cec_hwservice_28_0 (hal_tv_cec_hwservice))
(expandtypeattribute (hal_tv_cec_hwservice_28_0) true)
(typeattribute hal_tv_cec_hwservice_28_0)
(typeattributeset adbd_exec_28_0 (adbd_exec))
(expandtypeattribute (adbd_exec_28_0) true)
(typeattribute adbd_exec_28_0)
(typeattributeset e2fs_exec_28_0 (e2fs_exec))
(expandtypeattribute (e2fs_exec_28_0) true)
(typeattribute e2fs_exec_28_0)
(typeattributeset lmkd_exec_28_0 (lmkd_exec))
(expandtypeattribute (lmkd_exec_28_0) true)
(typeattribute lmkd_exec_28_0)
(typeattributeset logd_exec_28_0 (logd_exec))
(expandtypeattribute (logd_exec_28_0) true)
(typeattribute logd_exec_28_0)
(typeattributeset netd_exec_28_0 (netd_exec))
(expandtypeattribute (netd_exec_28_0) true)
(typeattribute netd_exec_28_0)
(typeattributeset sgdisk_28_0 (sgdisk))
(expandtypeattribute (sgdisk_28_0) true)
(typeattribute sgdisk_28_0)
(typeattributeset init_exec_28_0 (init_exec))
(expandtypeattribute (init_exec_28_0) true)
(typeattribute init_exec_28_0)
(typeattributeset media_router_service_28_0 (media_router_service))
(expandtypeattribute (media_router_service_28_0) true)
(typeattribute media_router_service_28_0)
(typeattributeset batteryproperties_service_28_0 (batteryproperties_service))
(expandtypeattribute (batteryproperties_service_28_0) true)
(typeattribute batteryproperties_service_28_0)
(typeattributeset storaged_service_28_0 (storaged_service))
(expandtypeattribute (storaged_service_28_0) true)
(typeattribute storaged_service_28_0)
(typeattributeset selinuxfs_28_0 (selinuxfs))
(expandtypeattribute (selinuxfs_28_0) true)
(typeattribute selinuxfs_28_0)
(typeattributeset sysfs_thermal_28_0 (sysfs_thermal))
(expandtypeattribute (sysfs_thermal_28_0) true)
(typeattribute sysfs_thermal_28_0)
(typeattributeset system_app_service_28_0 (system_app_service))
(expandtypeattribute (system_app_service_28_0) true)
(typeattribute system_app_service_28_0)
(typeattributeset full_device_28_0 (full_device))
(expandtypeattribute (full_device_28_0) true)
(typeattribute full_device_28_0)
(typeattributeset exported_config_prop_28_0 (exported_config_prop))
(expandtypeattribute (exported_config_prop_28_0) true)
(typeattribute exported_config_prop_28_0)
(typeattributeset fuse_device_28_0 (fuse_device))
(expandtypeattribute (fuse_device_28_0) true)
(typeattribute fuse_device_28_0)
(typeattributeset power_service_28_0 (power_service))
(expandtypeattribute (power_service_28_0) true)
(typeattribute power_service_28_0)
(typeattributeset uncrypt_28_0 (uncrypt))
(expandtypeattribute (uncrypt_28_0) true)
(typeattribute uncrypt_28_0)
(typeattributeset proc_mounts_28_0 (proc_mounts))
(expandtypeattribute (proc_mounts_28_0) true)
(typeattribute proc_mounts_28_0)
(typeattributeset pdx_display_client_channel_socket_28_0 (pdx_display_client_channel_socket))
(expandtypeattribute (pdx_display_client_channel_socket_28_0) true)
(typeattribute pdx_display_client_channel_socket_28_0)
(typeattributeset secure_element_service_28_0 (secure_element_service))
(expandtypeattribute (secure_element_service_28_0) true)
(typeattribute secure_element_service_28_0)
(typeattributeset debugfs_tracing_28_0 (debugfs_tracing))
(expandtypeattribute (debugfs_tracing_28_0) true)
(typeattribute debugfs_tracing_28_0)
(typeattributeset proc_buddyinfo_28_0 (proc_buddyinfo))
(expandtypeattribute (proc_buddyinfo_28_0) true)
(typeattribute proc_buddyinfo_28_0)
(typeattributeset sysfs_zram_uevent_28_0 (sysfs_zram_uevent))
(expandtypeattribute (sysfs_zram_uevent_28_0) true)
(typeattribute sysfs_zram_uevent_28_0)
(typeattributeset fingerprint_vendor_data_file_28_0 (fingerprint_vendor_data_file))
(expandtypeattribute (fingerprint_vendor_data_file_28_0) true)
(typeattribute fingerprint_vendor_data_file_28_0)
(typeattributeset proc_modules_28_0 (proc_modules))
(expandtypeattribute (proc_modules_28_0) true)
(typeattribute proc_modules_28_0)
(typeattributeset virtual_touchpad_service_28_0 (virtual_touchpad_service))
(expandtypeattribute (virtual_touchpad_service_28_0) true)
(typeattribute virtual_touchpad_service_28_0)
(typeattributeset wificond_exec_28_0 (wificond_exec))
(expandtypeattribute (wificond_exec_28_0) true)
(typeattribute wificond_exec_28_0)
(typeattributeset commontime_management_service_28_0 (commontime_management_service))
(expandtypeattribute (commontime_management_service_28_0) true)
(typeattribute commontime_management_service_28_0)
(typeattributeset proc_interrupts_28_0 (proc_interrupts))
(expandtypeattribute (proc_interrupts_28_0) true)
(typeattribute proc_interrupts_28_0)
(typeattributeset exported_secure_prop_28_0 (exported_secure_prop))
(expandtypeattribute (exported_secure_prop_28_0) true)
(typeattribute exported_secure_prop_28_0)
(typeattributeset proc_hostname_28_0 (proc_hostname))
(expandtypeattribute (proc_hostname_28_0) true)
(typeattribute proc_hostname_28_0)
(typeattributeset pdx_bufferhub_client_channel_socket_28_0 (pdx_bufferhub_client_channel_socket))
(expandtypeattribute (pdx_bufferhub_client_channel_socket_28_0) true)
(typeattribute pdx_bufferhub_client_channel_socket_28_0)
(typeattributeset hal_gatekeeper_hwservice_28_0 (hal_gatekeeper_hwservice))
(expandtypeattribute (hal_gatekeeper_hwservice_28_0) true)
(typeattribute hal_gatekeeper_hwservice_28_0)
(typeattributeset slideshow_28_0 (slideshow))
(expandtypeattribute (slideshow_28_0) true)
(typeattribute slideshow_28_0)
(typeattributeset proc_pipe_conf_28_0 (proc_pipe_conf))
(expandtypeattribute (proc_pipe_conf_28_0) true)
(typeattribute proc_pipe_conf_28_0)
(typeattributeset exported_system_prop_28_0 (exported_system_prop))
(expandtypeattribute (exported_system_prop_28_0) true)
(typeattribute exported_system_prop_28_0)
(typeattributeset graphicsstats_service_28_0 (graphicsstats_service))
(expandtypeattribute (graphicsstats_service_28_0) true)
(typeattribute graphicsstats_service_28_0)
(typeattributeset drmserver_service_28_0 (drmserver_service))
(expandtypeattribute (drmserver_service_28_0) true)
(typeattribute drmserver_service_28_0)
(typeattributeset fingerprintd_data_file_28_0 (fingerprintd_data_file))
(expandtypeattribute (fingerprintd_data_file_28_0) true)
(typeattribute fingerprintd_data_file_28_0)
(typeattributeset vr_hwc_28_0 (vr_hwc))
(expandtypeattribute (vr_hwc_28_0) true)
(typeattribute vr_hwc_28_0)
(typeattributeset exported_audio_prop_28_0 (exported_audio_prop))
(expandtypeattribute (exported_audio_prop_28_0) true)
(typeattribute exported_audio_prop_28_0)
(typeattributeset inotify_28_0 (inotify))
(expandtypeattribute (inotify_28_0) true)
(typeattribute inotify_28_0)
(typeattributeset tombstoned_28_0 (tombstoned))
(expandtypeattribute (tombstoned_28_0) true)
(typeattribute tombstoned_28_0)
(typeattributeset pdx_display_vsync_channel_socket_28_0 (pdx_display_vsync_channel_socket))
(expandtypeattribute (pdx_display_vsync_channel_socket_28_0) true)
(typeattribute pdx_display_vsync_channel_socket_28_0)
(typeattributeset gpu_service_28_0 (gpu_service))
(expandtypeattribute (gpu_service_28_0) true)
(typeattribute gpu_service_28_0)
(typeattributeset hal_gnss_hwservice_28_0 (hal_gnss_hwservice))
(expandtypeattribute (hal_gnss_hwservice_28_0) true)
(typeattribute hal_gnss_hwservice_28_0)
(typeattributeset sysfs_bluetooth_writable_28_0 (sysfs_bluetooth_writable))
(expandtypeattribute (sysfs_bluetooth_writable_28_0) true)
(typeattribute sysfs_bluetooth_writable_28_0)
(typeattributeset lowpan_device_28_0 (lowpan_device))
(expandtypeattribute (lowpan_device_28_0) true)
(typeattribute lowpan_device_28_0)
(typeattributeset proc_bluetooth_writable_28_0 (proc_bluetooth_writable))
(expandtypeattribute (proc_bluetooth_writable_28_0) true)
(typeattribute proc_bluetooth_writable_28_0)
(typeattributeset dm_device_28_0 (dm_device))
(expandtypeattribute (dm_device_28_0) true)
(typeattribute dm_device_28_0)
(typeattributeset tee_data_file_28_0 (tee_data_file))
(expandtypeattribute (tee_data_file_28_0) true)
(typeattribute tee_data_file_28_0)
(typeattributeset trace_data_file_28_0 (trace_data_file))
(expandtypeattribute (trace_data_file_28_0) true)
(typeattribute trace_data_file_28_0)
(typeattributeset default_android_hwservice_28_0 (default_android_hwservice))
(expandtypeattribute (default_android_hwservice_28_0) true)
(typeattribute default_android_hwservice_28_0)
(typeattributeset dumpstate_socket_28_0 (dumpstate_socket))
(expandtypeattribute (dumpstate_socket_28_0) true)
(typeattribute dumpstate_socket_28_0)
(typeattributeset pdx_display_vsync_endpoint_socket_28_0 (pdx_display_vsync_endpoint_socket))
(expandtypeattribute (pdx_display_vsync_endpoint_socket_28_0) true)
(typeattribute pdx_display_vsync_endpoint_socket_28_0)
(typeattributeset perfprofd_exec_28_0 (perfprofd_exec))
(expandtypeattribute (perfprofd_exec_28_0) true)
(typeattribute perfprofd_exec_28_0)
(typeattributeset hal_thermal_hwservice_28_0 (hal_thermal_hwservice))
(expandtypeattribute (hal_thermal_hwservice_28_0) true)
(typeattribute hal_thermal_hwservice_28_0)
(typeattributeset vr_hwc_exec_28_0 (vr_hwc_exec))
(expandtypeattribute (vr_hwc_exec_28_0) true)
(typeattribute vr_hwc_exec_28_0)
(typeattributeset cache_recovery_file_28_0 (cache_recovery_file))
(expandtypeattribute (cache_recovery_file_28_0) true)
(typeattribute cache_recovery_file_28_0)
(typeattributeset batterystats_service_28_0 (batterystats_service))
(expandtypeattribute (batterystats_service_28_0) true)
(typeattribute batterystats_service_28_0)
(typeattributeset mediadrmserver_28_0 (mediadrmserver))
(expandtypeattribute (mediadrmserver_28_0) true)
(typeattribute mediadrmserver_28_0)
(typeattributeset hal_weaver_hwservice_28_0 (hal_weaver_hwservice))
(expandtypeattribute (hal_weaver_hwservice_28_0) true)
(typeattribute hal_weaver_hwservice_28_0)
(typeattributeset hal_codec2_hwservice_28_0 (hal_codec2_hwservice))
(expandtypeattribute (hal_codec2_hwservice_28_0) true)
(typeattribute hal_codec2_hwservice_28_0)
(typeattributeset device_28_0 (device))
(expandtypeattribute (device_28_0) true)
(typeattribute device_28_0)
(typeattributeset storage_stub_file_28_0 (storage_stub_file))
(expandtypeattribute (storage_stub_file_28_0) true)
(typeattribute storage_stub_file_28_0)
(typeattributeset sysfs_nfc_power_writable_28_0 (sysfs_nfc_power_writable))
(expandtypeattribute (sysfs_nfc_power_writable_28_0) true)
(typeattribute sysfs_nfc_power_writable_28_0)
(typeattributeset voiceinteraction_service_28_0 (voiceinteraction_service))
(expandtypeattribute (voiceinteraction_service_28_0) true)
(typeattribute voiceinteraction_service_28_0)
(typeattributeset hal_bootctl_hwservice_28_0 (hal_bootctl_hwservice))
(expandtypeattribute (hal_bootctl_hwservice_28_0) true)
(typeattribute hal_bootctl_hwservice_28_0)
(typeattributeset gatekeeper_service_28_0 (gatekeeper_service))
(expandtypeattribute (gatekeeper_service_28_0) true)
(typeattribute gatekeeper_service_28_0)
(typeattributeset hidl_allocator_hwservice_28_0 (hidl_allocator_hwservice))
(expandtypeattribute (hidl_allocator_hwservice_28_0) true)
(typeattribute hidl_allocator_hwservice_28_0)
(typeattributeset proc_pagetypeinfo_28_0 (proc_pagetypeinfo))
(expandtypeattribute (proc_pagetypeinfo_28_0) true)
(typeattribute proc_pagetypeinfo_28_0)
(typeattributeset qtaguid_device_28_0 (qtaguid_device))
(expandtypeattribute (qtaguid_device_28_0) true)
(typeattribute qtaguid_device_28_0)
(typeattributeset crossprofileapps_service_28_0 (crossprofileapps_service))
(expandtypeattribute (crossprofileapps_service_28_0) true)
(typeattribute crossprofileapps_service_28_0)
(typeattributeset property_socket_28_0 (property_socket))
(expandtypeattribute (property_socket_28_0) true)
(typeattribute property_socket_28_0)
(typeattributeset proc_panic_28_0 (proc_panic))
(expandtypeattribute (proc_panic_28_0) true)
(typeattribute proc_panic_28_0)
(typeattributeset install_recovery_exec_28_0 (install_recovery_exec))
(expandtypeattribute (install_recovery_exec_28_0) true)
(typeattribute install_recovery_exec_28_0)
(typeattributeset mnt_vendor_file_28_0 (mnt_vendor_file))
(expandtypeattribute (mnt_vendor_file_28_0) true)
(typeattribute mnt_vendor_file_28_0)
(typeattributeset vendor_file_28_0 (vendor_file))
(expandtypeattribute (vendor_file_28_0) true)
(typeattribute vendor_file_28_0)
(typeattributeset efs_file_28_0 (efs_file))
(expandtypeattribute (efs_file_28_0) true)
(typeattribute efs_file_28_0)
(typeattributeset mediaextractor_update_service_28_0 (mediaextractor_update_service))
(expandtypeattribute (mediaextractor_update_service_28_0) true)
(typeattribute mediaextractor_update_service_28_0)
(typeattributeset device_logging_prop_28_0 (device_logging_prop))
(expandtypeattribute (device_logging_prop_28_0) true)
(typeattribute device_logging_prop_28_0)
(typeattributeset mdnsd_socket_28_0 (mdnsd_socket))
(expandtypeattribute (mdnsd_socket_28_0) true)
(typeattribute mdnsd_socket_28_0)
(typeattributeset traced_enabled_prop_28_0 (traced_enabled_prop))
(expandtypeattribute (traced_enabled_prop_28_0) true)
(typeattribute traced_enabled_prop_28_0)
(typeattributeset hal_audiocontrol_hwservice_28_0 (hal_audiocontrol_hwservice))
(expandtypeattribute (hal_audiocontrol_hwservice_28_0) true)
(typeattribute hal_audiocontrol_hwservice_28_0)
(typeattributeset vendor_overlay_file_28_0 (vendor_overlay_file))
(expandtypeattribute (vendor_overlay_file_28_0) true)
(typeattribute vendor_overlay_file_28_0)
(typeattributeset icon_file_28_0 (icon_file))
(expandtypeattribute (icon_file_28_0) true)
(typeattribute icon_file_28_0)
(typeattributeset vold_device_28_0 (vold_device))
(expandtypeattribute (vold_device_28_0) true)
(typeattribute vold_device_28_0)
(typeattributeset exported2_default_prop_28_0 (exported2_default_prop))
(expandtypeattribute (exported2_default_prop_28_0) true)
(typeattribute exported2_default_prop_28_0)
(typeattributeset exported3_default_prop_28_0 (exported3_default_prop))
(expandtypeattribute (exported3_default_prop_28_0) true)
(typeattribute exported3_default_prop_28_0)
(typeattributeset sysfs_wake_lock_28_0 (sysfs_wake_lock))
(expandtypeattribute (sysfs_wake_lock_28_0) true)
(typeattribute sysfs_wake_lock_28_0)
(typeattributeset network_watchlist_service_28_0 (network_watchlist_service))
(expandtypeattribute (network_watchlist_service_28_0) true)
(typeattribute network_watchlist_service_28_0)
(typeattributeset proc_pid_max_28_0 (proc_pid_max))
(expandtypeattribute (proc_pid_max_28_0) true)
(typeattribute proc_pid_max_28_0)
