#!/bin/bash

echo "========================================"
echo "  Android System Image Packing Tool"
echo "  Using APF Tool from aihaven.top"
echo "========================================"

# Check if apftool exists
if [ ! -f "apftool" ]; then
    echo "Error: apftool not found!"
    echo "Please download APF Tool from: https://apftool-rs.aihaven.top/"
    echo "And place the apftool binary in this directory."
    echo "Make sure to make it executable: chmod +x apftool"
    echo
    exit 1
fi

# Make sure apftool is executable
chmod +x apftool

# Get system info from system_info.txt
echo "Reading system parameters..."
BLOCK_COUNT=$(grep "Block count:" system_info.txt | awk '{print $3}')
BLOCK_SIZE=$(grep "Block size:" system_info.txt | awk '{print $3}')
INODE_COUNT=$(grep "Inode count:" system_info.txt | awk '{print $3}')

echo "Block Count: $BLOCK_COUNT"
echo "Block Size: $BLOCK_SIZE"
echo "Inode Count: $INODE_COUNT"

# Calculate size in bytes
SIZE_BYTES=$((BLOCK_COUNT * BLOCK_SIZE))
SIZE_MB=$((SIZE_BYTES / 1024 / 1024))

echo "Total Size: ${SIZE_MB} MB (${SIZE_BYTES} bytes)"
echo

# Create system.img using APF tool
echo "Creating system.img using APF tool..."
echo "Command: ./apftool pack system system.img"

./apftool pack system system.img

if [ $? -eq 0 ]; then
    echo
    echo "========================================"
    echo "  SUCCESS: system.img created!"
    echo "========================================"
    echo
    if [ -f "system.img" ]; then
        SIZE=$(stat -c%s system.img)
        SIZE_MB=$((SIZE / 1024 / 1024))
        echo "File: system.img"
        echo "Size: ${SIZE_MB} MB (${SIZE} bytes)"
    fi
    echo
    echo "The system.img is ready for flashing!"
else
    echo
    echo "========================================"
    echo "  ERROR: Failed to create system.img"
    echo "========================================"
    echo "Error code: $?"
    echo
    echo "Possible solutions:"
    echo "1. Make sure apftool is the correct version"
    echo "2. Check if system directory is complete"
    echo "3. Ensure sufficient disk space"
    echo "4. Try running with sudo if permission issues"
fi

echo
