-----<PERSON><PERSON><PERSON> CERTIFICATE-----
MIIDqDCCApCgAwIBAgIJAP7c4wEPyUj/MA0GCSqGSIb3DQEBBQUAMDQxCzAJBgNV
BAYTAkZSMRIwEAYDVQQKDAlEaGlteW90aXMxETAPBgNVBAMMCENlcnRpZ25hMB4X
DTA3MDYyOTE1MTMwNVoXDTI3MDYyOTE1MTMwNVowNDELMAkGA1UEBhMCRlIxEjAQ
BgNVBAoMCURoaW15b3RpczERMA8GA1UEAwwIQ2VydGlnbmEwggEiMA0GCSqGSIb3
DQEBAQUAA4IBDwAwggEKAoIBAQDIaPHJ1tazNHUmgh7stL7qXOEm7RFHYeGifBZ4
QCHkYJ5ayGPhxLGWkv8YbWkj4Sti993iNi+RB7lIzw7sebYs5zRLcAglozyHGxny
gQcPOJAZ0xH+hrTy0V4eHpbNgGzOOzGTtvKg0KmVEn2lmsxryIRWijOp5yIVUxbw
zBfsV1/pogqYCd7jX5xv3EjjhQsVWqa6n6xI4wmy9/Qy3l40vhx4XUJbzg4ij02Q
130yGLMLLGq/jj8UEYkgDncUtT2UCIf3JR7VsmAA7G8qKCVuKj4YYxclPz5EIBb2
JsglrgVKtOdjLPOMFlN+XPsRGgjBRmKfIrjxwo1p3Po6WAbfAgMBAAGjgbwwgbkw
DwYDVR0TAQH/BAUwAwEB/zAdBgNVHQ4EFgQUGu3+QTmQtCRZvgHyUtVF9lo53BEw
ZAYDVR0jBF0wW4AUGu3+QTmQtCRZvgHyUtVF9lo53BGhOKQ2MDQxCzAJBgNVBAYT
AkZSMRIwEAYDVQQKDAlEaGlteW90aXMxETAPBgNVBAMMCENlcnRpZ25hggkA/tzj
AQ/JSP8wDgYDVR0PAQH/BAQDAgEGMBEGCWCGSAGG+EIBAQQEAwIABzANBgkqhkiG
9w0BAQUFAAOCAQEAhQMeknH2Qq/ho2Ge6/PAD/Kl1NqV5ta+aDY9fm4fTIrv0Q8h
bV6lUmPOEvjvKtpv6zf+EwLHyzs+ImvaYS5/1HI93TDhHkxAGYwP15zRgzB7mFnc
fca5DClMoTOi62c6ZYTTluLtdkVwj7Ur3vkj1kluPBS1xp81HlDQwY9qcEQCYsuu
HWhBp6pX6FOqB9IG9tUUBguRA3UsbHK1YZWaDYu5Def131TN3ubY1gkIl2PlwS6w
t0QmwCbAr1UwnjvVNioZBPRcHv/PLLf/0P2HQBHVESO7SMAhqaQoLf0V+LBOK/Qw
WyH8EZE0vkHve52Xdf+XlcCWWC/qu0bXu+TZLg==
-----END CERTIFICATE-----
Certificate:
    Data:
        Version: 3 (0x2)
        Serial Number: 18364802974209362175 (0xfedce3010fc948ff)
    Signature Algorithm: sha1WithRSAEncryption
        Issuer: C=FR, O=Dhimyotis, CN=Certigna
        Validity
            Not Before: Jun 29 15:13:05 2007 GMT
            Not After : Jun 29 15:13:05 2027 GMT
        Subject: C=FR, O=Dhimyotis, CN=Certigna
        Subject Public Key Info:
            Public Key Algorithm: rsaEncryption
                Public-Key: (2048 bit)
                Modulus:
                    00:c8:68:f1:c9:d6:d6:b3:34:75:26:82:1e:ec:b4:
                    be:ea:5c:e1:26:ed:11:47:61:e1:a2:7c:16:78:40:
                    21:e4:60:9e:5a:c8:63:e1:c4:b1:96:92:ff:18:6d:
                    69:23:e1:2b:62:f7:dd:e2:36:2f:91:07:b9:48:cf:
                    0e:ec:79:b6:2c:e7:34:4b:70:08:25:a3:3c:87:1b:
                    19:f2:81:07:0f:38:90:19:d3:11:fe:86:b4:f2:d1:
                    5e:1e:1e:96:cd:80:6c:ce:3b:31:93:b6:f2:a0:d0:
                    a9:95:12:7d:a5:9a:cc:6b:c8:84:56:8a:33:a9:e7:
                    22:15:53:16:f0:cc:17:ec:57:5f:e9:a2:0a:98:09:
                    de:e3:5f:9c:6f:dc:48:e3:85:0b:15:5a:a6:ba:9f:
                    ac:48:e3:09:b2:f7:f4:32:de:5e:34:be:1c:78:5d:
                    42:5b:ce:0e:22:8f:4d:90:d7:7d:32:18:b3:0b:2c:
                    6a:bf:8e:3f:14:11:89:20:0e:77:14:b5:3d:94:08:
                    87:f7:25:1e:d5:b2:60:00:ec:6f:2a:28:25:6e:2a:
                    3e:18:63:17:25:3f:3e:44:20:16:f6:26:c8:25:ae:
                    05:4a:b4:e7:63:2c:f3:8c:16:53:7e:5c:fb:11:1a:
                    08:c1:46:62:9f:22:b8:f1:c2:8d:69:dc:fa:3a:58:
                    06:df
                Exponent: 65537 (0x10001)
        X509v3 extensions:
            X509v3 Basic Constraints: critical
                CA:TRUE
            X509v3 Subject Key Identifier: 
                1A:ED:FE:41:39:90:B4:24:59:BE:01:F2:52:D5:45:F6:5A:39:DC:11
            X509v3 Authority Key Identifier: 
                keyid:1A:ED:FE:41:39:90:B4:24:59:BE:01:F2:52:D5:45:F6:5A:39:DC:11
                DirName:/C=FR/O=Dhimyotis/CN=Certigna
                serial:FE:DC:E3:01:0F:C9:48:FF

            X509v3 Key Usage: critical
                Certificate Sign, CRL Sign
            Netscape Cert Type: 
                SSL CA, S/MIME CA, Object Signing CA
    Signature Algorithm: sha1WithRSAEncryption
         85:03:1e:92:71:f6:42:af:e1:a3:61:9e:eb:f3:c0:0f:f2:a5:
         d4:da:95:e6:d6:be:68:36:3d:7e:6e:1f:4c:8a:ef:d1:0f:21:
         6d:5e:a5:52:63:ce:12:f8:ef:2a:da:6f:eb:37:fe:13:02:c7:
         cb:3b:3e:22:6b:da:61:2e:7f:d4:72:3d:dd:30:e1:1e:4c:40:
         19:8c:0f:d7:9c:d1:83:30:7b:98:59:dc:7d:c6:b9:0c:29:4c:
         a1:33:a2:eb:67:3a:65:84:d3:96:e2:ed:76:45:70:8f:b5:2b:
         de:f9:23:d6:49:6e:3c:14:b5:c6:9f:35:1e:50:d0:c1:8f:6a:
         70:44:02:62:cb:ae:1d:68:41:a7:aa:57:e8:53:aa:07:d2:06:
         f6:d5:14:06:0b:91:03:75:2c:6c:72:b5:61:95:9a:0d:8b:b9:
         0d:e7:f5:df:54:cd:de:e6:d8:d6:09:08:97:63:e5:c1:2e:b0:
         b7:44:26:c0:26:c0:af:55:30:9e:3b:d5:36:2a:19:04:f4:5c:
         1e:ff:cf:2c:b7:ff:d0:fd:87:40:11:d5:11:23:bb:48:c0:21:
         a9:a4:28:2d:fd:15:f8:b0:4e:2b:f4:30:5b:21:fc:11:91:34:
         be:41:ef:7b:9d:97:75:ff:97:95:c0:96:58:2f:ea:bb:46:d7:
         bb:e4:d9:2e
SHA1 Fingerprint=B1:2E:13:63:45:86:A4:6F:1A:B2:60:68:37:58:2D:C4:AC:FD:94:97
