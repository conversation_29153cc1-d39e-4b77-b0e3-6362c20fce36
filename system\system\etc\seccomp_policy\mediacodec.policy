# Organized by frequency of systemcall - in descending order for
# best performance.
futex: 1
ioctl: 1
write: 1
prctl: 1
clock_gettime: 1
getpriority: 1
read: 1
close: 1
writev: 1
dup: 1
ppoll: 1
mmap2: 1
getrandom: 1

# mremap: Ensure |flags| are (MREMAP_MAYMOVE | MREMAP_FIXED) TODO: Once minijail
# parser support for '<' is in this needs to be modified to also prevent
# |old_address| and |new_address| from touching the exception vector page, which
# on ARM is statically loaded at 0xffff 0000. See
# http://infocenter.arm.com/help/index.jsp?topic=/com.arm.doc.ddi0211h/Babfeega.html
# for more details.
mremap: arg3 == 3
munmap: 1
mprotect: 1
madvise: 1
openat: 1
sigaltstack: 1
clone: 1
setpriority: 1
getuid32: 1
fstat64: 1
fstatfs64: 1
pread64: 1
faccessat: 1
readlinkat: 1
exit: 1
rt_sigprocmask: 1
set_tid_address: 1
restart_syscall: 1
exit_group: 1
rt_sigreturn: 1
pipe2: 1
gettimeofday: 1
sched_yield: 1
nanosleep: 1
lseek: 1
_llseek: 1
sched_get_priority_max: 1
sched_get_priority_min: 1
statfs64: 1
sched_setscheduler: 1
fstatat64: 1
ugetrlimit: 1
getdents64: 1
getrandom: 1

@include /system/etc/seccomp_policy/crash_dump.arm.policy
