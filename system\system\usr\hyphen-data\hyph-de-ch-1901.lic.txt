% title: >
%     TeX-Trennmuster für die traditionelle deutsch-schweizerische
%     Rechtschreibung
% copyright: >
%     Copyright (c) 2013-2017
%     <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>,
%     <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>
% notice: This file is part of the hyph-utf8 package.
%     See http://www.hyphenation.org for more information.
% source: dehyphts-x-2017-03-31.pat
% language:
%     name: German, Swiss spelling
%     tag: de-CH-1901
% licence:
%     - This file is made available under the following licence:
%         name: MIT
%         url: https://opensourceo.org/licenses/MIT
%         text: >
%             Permission is hereby granted, free of charge, to any person
%             obtaining a copy of this software and associated documentation
%             files (the "Software"), to deal in the Software without
%             restriction, including without limitation the rights to use,
%             copy, modify, merge, publish, distribute, sublicense, and/or sell
%             copies of the Software, and to permit persons to whom the
%             Software is furnished to do so, subject to the following
%             conditions:
%
%             The above copyright notice and this permission notice shall be
%             included in all copies or substantial portions of the Software.
%
%             THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
%             EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
%             OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
%             NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
%             HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
%             WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
%             FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
%             OTHER DEALINGS IN THE SOFTWARE.
% ==========================================
% The word list is available from
%
%   http://repo.or.cz/w/wortliste.git?a=commit;h=5fd786fcb1ed48448e058672f1f58d185653d8c6
%
% The used patgen parameters are
%
%   1 1 | 2 5 | 1 1 1
%   2 2 | 2 5 | 1 2 1
%   3 3 | 2 6 | 1 1 1
%   4 4 | 2 6 | 1 4 1
%   5 5 | 2 7 | 1 1 1
%   6 6 | 2 7 | 1 6 1
%   7 7 | 2 13 | 1 4 1
%   8 8 | 2 13 | 1 8 1

\message{Swiss-German Hyphenation Patterns (Traditional Orthography) `dehyphts-x' 2017-03-31 (WL)}
