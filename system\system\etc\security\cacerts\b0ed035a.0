-----<PERSON><PERSON><PERSON> CERTIFICATE-----
MIIFQTCCAymgAwIBAgICDL4wDQYJKoZIhvcNAQELBQAwUTELMAkGA1UEBhMCVFcx
EjAQBgNVBAoTCVRBSVdBTi1DQTEQMA4GA1UECxMHUm9vdCBDQTEcMBoGA1UEAxMT
VFdDQSBHbG9iYWwgUm9vdCBDQTAeFw0xMjA2MjcwNjI4MzNaFw0zMDEyMzExNTU5
NTlaMFExCzAJBgNVBAYTAlRXMRIwEAYDVQQKEwlUQUlXQU4tQ0ExEDAOBgNVBAsT
B1Jvb3QgQ0ExHDAaBgNVBAMTE1RXQ0EgR2xvYmFsIFJvb3QgQ0EwggIiMA0GCSqG
SIb3DQEBAQUAA4ICDwAwggIKAoICAQCwBdvI64zEbooh745NnHEKH1Jw7W2CnJfF
10xORUnLQEK1EjRsGcJ0pDFfhQKX7EMzClPSnIyOt7h52yvVavKOZsTuKwEHktSz
0ALfUPZVr2YOy+BHYC8rMjk1Ujoog/h7FsYYuGLWRyWRzvAZEk2tY/XTP3VfKfCh
MBwqoJimFb3u/Rk28OKRQ4/6ytYQJ0lM793B8YVwm8rqqFpD/G2Gb3PpN0Wp8DbH
zIh1HrtsBv+baz4X7GGqcXzGHaL3SekVtTzWoWH1EfcFbx39Eb7QMAfCKbAJTibc
46KokWofwpFFiFzlmLhxpRUZyXx1EcxwdE8tmx2RRP1WKKD+u4ZqyPpcC1jcxkt2
yKsi2XMPpfRaAok/T54igu6idFMqPVMnaR1sjjIsZAAmY2E2TqNGtz99sy2sbZCi
laLOz9qC5wc0GZbpuCGqKX6mOL6OKUohZnkfs8O1CWfe1tQHRvMq2uYiN2DLgbYP
oA/pyJV/v1WRBXrPPRXAb94JlAGD1zQbzECl8LibZ9WYkTunhHiVJqRaCPgrdLQA
BDzfuBSO6N+pjWxnkjMdwLfS7JLIvgm/LCkFbwJrnu+8vyq8W8BQj0FwcYeyTbcE
qYSjMq+u7msXi7Kx/mzhkIyIqJdIzshNy/MGz19qCkKxHh53L46g5pIOBvwFItIm
4TFRfTLcDwIDAQABoyMwITAOBgNVHQ8BAf8EBAMCAQYwDwYDVR0TAQH/BAUwAwEB
/zANBgkqhkiG9w0BAQsFAAOCAgEAXzSBdu+WHdXltdkCY4QWwa6gcFGn90xHNcgL
1yg9iXHZqjNB6hQbbCEAwGxCGX6faVsgQt+i0trEfJdLjbDorMjupWkEmQqSpqsn
LhpNgb+E1HAerUf+/UqdM+DyucRFCCEK2mlpc3INvjT+lIutwx4116KD7+U4x6WF
H6vPNOw/KP4M8VeGTslV9xzU2KV9Bnpv1d8Q34FOIWWxtuEXeZVFBs5fzNxGiWNo
RI2T9GRwoD2dKAXDOXC4Ynsg/eTb6QihuJ49CcdP+yz4k3ZB3lLg4VfSnQO8d57+
nile98FRYB/e2guyLXW3Q0iT5/Z5xoRdgFlglPx4mI88k1HtQJAH32RjJMtOcQWh
15QaiDLxInQirqWm2BJpTGCjAu4r7NRjkgtevi92a6O2JryPA9gK8kxkRr05YuWW
6zRjESjMlfGt7+/cgFhI6Uu46mWs6fyAtbXIRfmswZ/ZuepiiI7E8UuDEq3mi4TW
nsLrgxifarsbJGAzcMzs9zLzXNl5fe+epP7JI8Mk7hWSsT2RTyaGvWZzJBPqpK5j
wa19hAM8EHiGG3njxPPyBJUgriOCxLM6AGK/5jYk4Ve6xx6QddVfP5VhK8E7zeWz
aGHQRiapIVJpLesux+t3zqY6tQMzT3bR51xUAV3LePTJDL/PEo4XLSNolOer/qmy
KwbQBM0=
-----END CERTIFICATE-----
Certificate:
    Data:
        Version: 3 (0x2)
        Serial Number: 3262 (0xcbe)
    Signature Algorithm: sha256WithRSAEncryption
        Issuer: C=TW, O=TAIWAN-CA, OU=Root CA, CN=TWCA Global Root CA
        Validity
            Not Before: Jun 27 06:28:33 2012 GMT
            Not After : Dec 31 15:59:59 2030 GMT
        Subject: C=TW, O=TAIWAN-CA, OU=Root CA, CN=TWCA Global Root CA
        Subject Public Key Info:
            Public Key Algorithm: rsaEncryption
                Public-Key: (4096 bit)
                Modulus:
                    00:b0:05:db:c8:eb:8c:c4:6e:8a:21:ef:8e:4d:9c:
                    71:0a:1f:52:70:ed:6d:82:9c:97:c5:d7:4c:4e:45:
                    49:cb:40:42:b5:12:34:6c:19:c2:74:a4:31:5f:85:
                    02:97:ec:43:33:0a:53:d2:9c:8c:8e:b7:b8:79:db:
                    2b:d5:6a:f2:8e:66:c4:ee:2b:01:07:92:d4:b3:d0:
                    02:df:50:f6:55:af:66:0e:cb:e0:47:60:2f:2b:32:
                    39:35:52:3a:28:83:f8:7b:16:c6:18:b8:62:d6:47:
                    25:91:ce:f0:19:12:4d:ad:63:f5:d3:3f:75:5f:29:
                    f0:a1:30:1c:2a:a0:98:a6:15:bd:ee:fd:19:36:f0:
                    e2:91:43:8f:fa:ca:d6:10:27:49:4c:ef:dd:c1:f1:
                    85:70:9b:ca:ea:a8:5a:43:fc:6d:86:6f:73:e9:37:
                    45:a9:f0:36:c7:cc:88:75:1e:bb:6c:06:ff:9b:6b:
                    3e:17:ec:61:aa:71:7c:c6:1d:a2:f7:49:e9:15:b5:
                    3c:d6:a1:61:f5:11:f7:05:6f:1d:fd:11:be:d0:30:
                    07:c2:29:b0:09:4e:26:dc:e3:a2:a8:91:6a:1f:c2:
                    91:45:88:5c:e5:98:b8:71:a5:15:19:c9:7c:75:11:
                    cc:70:74:4f:2d:9b:1d:91:44:fd:56:28:a0:fe:bb:
                    86:6a:c8:fa:5c:0b:58:dc:c6:4b:76:c8:ab:22:d9:
                    73:0f:a5:f4:5a:02:89:3f:4f:9e:22:82:ee:a2:74:
                    53:2a:3d:53:27:69:1d:6c:8e:32:2c:64:00:26:63:
                    61:36:4e:a3:46:b7:3f:7d:b3:2d:ac:6d:90:a2:95:
                    a2:ce:cf:da:82:e7:07:34:19:96:e9:b8:21:aa:29:
                    7e:a6:38:be:8e:29:4a:21:66:79:1f:b3:c3:b5:09:
                    67:de:d6:d4:07:46:f3:2a:da:e6:22:37:60:cb:81:
                    b6:0f:a0:0f:e9:c8:95:7f:bf:55:91:05:7a:cf:3d:
                    15:c0:6f:de:09:94:01:83:d7:34:1b:cc:40:a5:f0:
                    b8:9b:67:d5:98:91:3b:a7:84:78:95:26:a4:5a:08:
                    f8:2b:74:b4:00:04:3c:df:b8:14:8e:e8:df:a9:8d:
                    6c:67:92:33:1d:c0:b7:d2:ec:92:c8:be:09:bf:2c:
                    29:05:6f:02:6b:9e:ef:bc:bf:2a:bc:5b:c0:50:8f:
                    41:70:71:87:b2:4d:b7:04:a9:84:a3:32:af:ae:ee:
                    6b:17:8b:b2:b1:fe:6c:e1:90:8c:88:a8:97:48:ce:
                    c8:4d:cb:f3:06:cf:5f:6a:0a:42:b1:1e:1e:77:2f:
                    8e:a0:e6:92:0e:06:fc:05:22:d2:26:e1:31:51:7d:
                    32:dc:0f
                Exponent: 65537 (0x10001)
        X509v3 extensions:
            X509v3 Key Usage: critical
                Certificate Sign, CRL Sign
            X509v3 Basic Constraints: critical
                CA:TRUE
    Signature Algorithm: sha256WithRSAEncryption
         5f:34:81:76:ef:96:1d:d5:e5:b5:d9:02:63:84:16:c1:ae:a0:
         70:51:a7:f7:4c:47:35:c8:0b:d7:28:3d:89:71:d9:aa:33:41:
         ea:14:1b:6c:21:00:c0:6c:42:19:7e:9f:69:5b:20:42:df:a2:
         d2:da:c4:7c:97:4b:8d:b0:e8:ac:c8:ee:a5:69:04:99:0a:92:
         a6:ab:27:2e:1a:4d:81:bf:84:d4:70:1e:ad:47:fe:fd:4a:9d:
         33:e0:f2:b9:c4:45:08:21:0a:da:69:69:73:72:0d:be:34:fe:
         94:8b:ad:c3:1e:35:d7:a2:83:ef:e5:38:c7:a5:85:1f:ab:cf:
         34:ec:3f:28:fe:0c:f1:57:86:4e:c9:55:f7:1c:d4:d8:a5:7d:
         06:7a:6f:d5:df:10:df:81:4e:21:65:b1:b6:e1:17:79:95:45:
         06:ce:5f:cc:dc:46:89:63:68:44:8d:93:f4:64:70:a0:3d:9d:
         28:05:c3:39:70:b8:62:7b:20:fd:e4:db:e9:08:a1:b8:9e:3d:
         09:c7:4f:fb:2c:f8:93:76:41:de:52:e0:e1:57:d2:9d:03:bc:
         77:9e:fe:9e:29:5e:f7:c1:51:60:1f:de:da:0b:b2:2d:75:b7:
         43:48:93:e7:f6:79:c6:84:5d:80:59:60:94:fc:78:98:8f:3c:
         93:51:ed:40:90:07:df:64:63:24:cb:4e:71:05:a1:d7:94:1a:
         88:32:f1:22:74:22:ae:a5:a6:d8:12:69:4c:60:a3:02:ee:2b:
         ec:d4:63:92:0b:5e:be:2f:76:6b:a3:b6:26:bc:8f:03:d8:0a:
         f2:4c:64:46:bd:39:62:e5:96:eb:34:63:11:28:cc:95:f1:ad:
         ef:ef:dc:80:58:48:e9:4b:b8:ea:65:ac:e9:fc:80:b5:b5:c8:
         45:f9:ac:c1:9f:d9:b9:ea:62:88:8e:c4:f1:4b:83:12:ad:e6:
         8b:84:d6:9e:c2:eb:83:18:9f:6a:bb:1b:24:60:33:70:cc:ec:
         f7:32:f3:5c:d9:79:7d:ef:9e:a4:fe:c9:23:c3:24:ee:15:92:
         b1:3d:91:4f:26:86:bd:66:73:24:13:ea:a4:ae:63:c1:ad:7d:
         84:03:3c:10:78:86:1b:79:e3:c4:f3:f2:04:95:20:ae:23:82:
         c4:b3:3a:00:62:bf:e6:36:24:e1:57:ba:c7:1e:90:75:d5:5f:
         3f:95:61:2b:c1:3b:cd:e5:b3:68:61:d0:46:26:a9:21:52:69:
         2d:eb:2e:c7:eb:77:ce:a6:3a:b5:03:33:4f:76:d1:e7:5c:54:
         01:5d:cb:78:f4:c9:0c:bf:cf:12:8e:17:2d:23:68:94:e7:ab:
         fe:a9:b2:2b:06:d0:04:cd
SHA1 Fingerprint=9C:BB:48:53:F6:A4:F6:D3:52:A4:E8:32:52:55:60:13:F5:AD:AF:65
