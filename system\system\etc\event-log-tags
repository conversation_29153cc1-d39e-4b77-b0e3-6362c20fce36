42 answer (to life the universe etc|3)
314 pi
1003 auditd (avc|3)
1004 chatty (dropped|3)
1005 tag_def (tag|1),(name|3),(format|3)
1006 liblog (dropped|1)
2718 e
2719 configuration_changed (config mask|1|5)
2720 sync (id|3),(event|1|5),(source|1|5),(account|1|5)
2721 cpu (total|1|6),(user|1|6),(system|1|6),(iowait|1|6),(irq|1|6),(softirq|1|6)
2722 battery_level (level|1|6),(voltage|1|1),(temperature|1|1)
2723 battery_status (status|1|5),(health|1|5),(present|1|5),(plugged|1|5),(technology|3)
2724 power_sleep_requested (wakeLocksCleared|1|1)
2725 power_screen_broadcast_send (wakelockCount|1|1)
2726 power_screen_broadcast_done (on|1|5),(broadcastDuration|2|3),(wakelockCount|1|1)
2727 power_screen_broadcast_stop (which|1|5),(wakelockCount|1|1)
2728 power_screen_state (offOrOn|1|5),(becauseOfUser|1|5),(totalTouchDownTime|2|3),(touchCycles|1|1),(latency|1|3)
2729 power_partial_wake_state (releasedorAcquired|1|5),(tag|3)
2730 battery_discharge (duration|2|3),(minLevel|1|6),(maxLevel|1|6)
2731 power_soft_sleep_requested (savedwaketimems|2)
2732 storaged_disk_stats (type|3),(start_time|2|3),(end_time|2|3),(read_ios|2|1),(read_merges|2|1),(read_sectors|2|1),(read_ticks|2|3),(write_ios|2|1),(write_merges|2|1),(write_sectors|2|1),(write_ticks|2|3),(o_in_flight|2|1),(io_ticks|2|3),(io_in_queue|2|1)
2733 storaged_emmc_info (mmc_ver|3),(eol|1),(lifetime_a|1),(lifetime_b|1)
2739 battery_saver_mode (prevOffOrOn|1|5),(nowOffOrOn|1|5),(interactive|1|5),(features|3|5),(reason|1|5)
2740 location_controller
2741 force_gc (reason|3)
2742 tickle (authority|3)
2747 contacts_aggregation (aggregation time|2|3), (count|1|1)
2748 cache_file_deleted (path|3)
2749 storage_state (uuid|3),(old_state|1),(new_state|1),(usable|2),(total|2)
2750 notification_enqueue (uid|1|5),(pid|1|5),(pkg|3),(id|1|5),(tag|3),(userid|1|5),(notification|3),(status|1)
2751 notification_cancel (uid|1|5),(pid|1|5),(pkg|3),(id|1|5),(tag|3),(userid|1|5),(required_flags|1),(forbidden_flags|1),(reason|1|5),(listener|3)
2752 notification_cancel_all (uid|1|5),(pid|1|5),(pkg|3),(userid|1|5),(required_flags|1),(forbidden_flags|1),(reason|1|5),(listener|3)
2755 fstrim_start (time|2|3)
2756 fstrim_finish (time|2|3)
2802 watchdog (Service|3)
2803 watchdog_proc_pss (Process|3),(Pid|1|5),(Pss|1|2)
2804 watchdog_soft_reset (Process|3),(Pid|1|5),(MaxPss|1|2),(Pss|1|2),(Skip|3)
2805 watchdog_hard_reset (Process|3),(Pid|1|5),(MaxPss|1|2),(Pss|1|2)
2806 watchdog_pss_stats (EmptyPss|1|2),(EmptyCount|1|1),(BackgroundPss|1|2),(BackgroundCount|1|1),(ServicePss|1|2),(ServiceCount|1|1),(VisiblePss|1|2),(VisibleCount|1|1),(ForegroundPss|1|2),(ForegroundCount|1|1),(NoPssCount|1|1)
2807 watchdog_proc_stats (DeathsInOne|1|1),(DeathsInTwo|1|1),(DeathsInThree|1|1),(DeathsInFour|1|1),(DeathsInFive|1|1)
2808 watchdog_scheduled_reboot (Now|2|1),(Interval|1|3),(StartTime|1|3),(Window|1|3),(Skip|3)
2809 watchdog_meminfo (MemFree|1|2),(Buffers|1|2),(Cached|1|2),(Active|1|2),(Inactive|1|2),(AnonPages|1|2),(Mapped|1|2),(Slab|1|2),(SReclaimable|1|2),(SUnreclaim|1|2),(PageTables|1|2)
2810 watchdog_vmstat (runtime|2|3),(pgfree|1|1),(pgactivate|1|1),(pgdeactivate|1|1),(pgfault|1|1),(pgmajfault|1|1)
2811 watchdog_requested_reboot (NoWait|1|1),(ScheduleInterval|1|3),(RecheckInterval|1|3),(StartTime|1|3),(Window|1|3),(MinScreenOff|1|3),(MinNextAlarm|1|3)
2820 backup_data_changed (Package|3)
2821 backup_start (Transport|3)
2822 backup_transport_failure (Package|3)
2823 backup_agent_failure (Package|3),(Message|3)
2824 backup_package (Package|3),(Size|1|2)
2825 backup_success (Packages|1|1),(Time|1|3)
2826 backup_reset (Transport|3)
2827 backup_initialize
2828 backup_requested (Total|1|1),(Key-Value|1|1),(Full|1|1)
2829 backup_quota_exceeded (Package|3)
2830 restore_start (Transport|3),(Source|2|5)
2831 restore_transport_failure
2832 restore_agent_failure (Package|3),(Message|3)
2833 restore_package (Package|3),(Size|1|2)
2834 restore_success (Packages|1|1),(Time|1|3)
2840 full_backup_package (Package|3)
2841 full_backup_agent_failure (Package|3),(Message|3)
2842 full_backup_transport_failure
2843 full_backup_success (Package|3)
2844 full_restore_package (Package|3)
2845 full_backup_quota_exceeded (Package|3)
2846 full_backup_cancelled (Package|3),(Message|3)
2850 backup_transport_lifecycle (Transport|3),(Bound|1|1)
2851 backup_transport_connection (Transport|3),(Connected|1|1)
2900 rescue_note (uid|1),(count|1),(window|2)
2901 rescue_level (level|1),(trigger_uid|1)
2902 rescue_success (level|1)
2903 rescue_failure (level|1),(msg|3)
3000 boot_progress_start (time|2|3)
3010 boot_progress_system_run (time|2|3)
3020 boot_progress_preload_start (time|2|3)
3030 boot_progress_preload_end (time|2|3)
3040 boot_progress_ams_ready (time|2|3)
3050 boot_progress_enable_screen (time|2|3)
3060 boot_progress_pms_start (time|2|3)
3070 boot_progress_pms_system_scan_start (time|2|3)
3080 boot_progress_pms_data_scan_start (time|2|3)
3090 boot_progress_pms_scan_end (time|2|3)
3100 boot_progress_pms_ready (time|2|3)
3110 unknown_sources_enabled (value|1)
3120 pm_critical_info (msg|3)
3121 pm_package_stats (manual_time|2|3),(quota_time|2|3),(manual_data|2|2),(quota_data|2|2),(manual_cache|2|2),(quota_cache|2|2)
4000 calendar_upgrade_receiver (time|2|3)
4100 contacts_upgrade_receiver (time|2|3)
8000 job_deferred_execution (time|2|3)
20003 dvm_lock_sample (process|3),(main|1|5),(thread|3),(time|1|3),(file|3),(line|1|5),(ownerfile|3),(ownerline|1|5),(sample_percent|1|6)
20004 art_hidden_api_access (access_method|1),(flags|1),(class|3),(member|3),(type_signature|3)
27390 battery_saving_stats (batterySaver|1|5),(interactive|1|5),(doze|1|5),(delta_duration|2|3),(delta_battery_drain|1|1),(delta_battery_drain_percent|1|6),(total_duration|2|3),(total_battery_drain|1|1),(total_battery_drain_percent|1|6)
27391 user_activity_timeout_override (override|2|3)
27392 battery_saver_setting (threshold|1)
27500 notification_panel_revealed (items|1)
27501 notification_panel_hidden
27510 notification_visibility_changed (newlyVisibleKeys|3),(noLongerVisibleKeys|3)
27511 notification_expansion (key|3),(user_action|1),(expanded|1),(lifespan|1),(freshness|1),(exposure|1)
27520 notification_clicked (key|3),(lifespan|1),(freshness|1),(exposure|1),(rank|1),(count|1)
27521 notification_action_clicked (key|3),(action_index|1),(lifespan|1),(freshness|1),(exposure|1),(rank|1),(count|1)
27530 notification_canceled (key|3),(reason|1),(lifespan|1),(freshness|1),(exposure|1),(rank|1),(count|1),(listener|3)
27531 notification_visibility (key|3),(visibile|1),(lifespan|1),(freshness|1),(exposure|1),(rank|1)
27532 notification_alert (key|3),(buzz|1),(beep|1),(blink|1)
27533 notification_autogrouped (key|3)
30001 am_finish_activity (User|1|5),(Token|1|5),(Task ID|1|5),(Component Name|3),(Reason|3)
30002 am_task_to_front (User|1|5),(Task|1|5)
30003 am_new_intent (User|1|5),(Token|1|5),(Task ID|1|5),(Component Name|3),(Action|3),(MIME Type|3),(URI|3),(Flags|1|5)
30004 am_create_task (User|1|5),(Task ID|1|5)
30005 am_create_activity (User|1|5),(Token|1|5),(Task ID|1|5),(Component Name|3),(Action|3),(MIME Type|3),(URI|3),(Flags|1|5)
30006 am_restart_activity (User|1|5),(Token|1|5),(Task ID|1|5),(Component Name|3)
30007 am_resume_activity (User|1|5),(Token|1|5),(Task ID|1|5),(Component Name|3)
30008 am_anr (User|1|5),(pid|1|5),(Package Name|3),(Flags|1|5),(reason|3)
30009 am_activity_launch_time (User|1|5),(Token|1|5),(Component Name|3),(time|2|3)
30010 am_proc_bound (User|1|5),(PID|1|5),(Process Name|3)
30011 am_proc_died (User|1|5),(PID|1|5),(Process Name|3),(OomAdj|1|5),(ProcState|1|5)
30012 am_failed_to_pause (User|1|5),(Token|1|5),(Wanting to pause|3),(Currently pausing|3)
30013 am_pause_activity (User|1|5),(Token|1|5),(Component Name|3),(User Leaving|3)
30014 am_proc_start (User|1|5),(PID|1|5),(UID|1|5),(Process Name|3),(Type|3),(Component|3)
30015 am_proc_bad (User|1|5),(UID|1|5),(Process Name|3)
30016 am_proc_good (User|1|5),(UID|1|5),(Process Name|3)
30017 am_low_memory (Num Processes|1|1)
30018 am_destroy_activity (User|1|5),(Token|1|5),(Task ID|1|5),(Component Name|3),(Reason|3)
30019 am_relaunch_resume_activity (User|1|5),(Token|1|5),(Task ID|1|5),(Component Name|3)
30020 am_relaunch_activity (User|1|5),(Token|1|5),(Task ID|1|5),(Component Name|3)
30021 am_on_paused_called (User|1|5),(Component Name|3),(Reason|3)
30022 am_on_resume_called (User|1|5),(Component Name|3),(Reason|3)
30023 am_kill (User|1|5),(PID|1|5),(Process Name|3),(OomAdj|1|5),(Reason|3)
30024 am_broadcast_discard_filter (User|1|5),(Broadcast|1|5),(Action|3),(Receiver Number|1|1),(BroadcastFilter|1|5)
30025 am_broadcast_discard_app (User|1|5),(Broadcast|1|5),(Action|3),(Receiver Number|1|1),(App|3)
30030 am_create_service (User|1|5),(Service Record|1|5),(Name|3),(UID|1|5),(PID|1|5)
30031 am_destroy_service (User|1|5),(Service Record|1|5),(PID|1|5)
30032 am_process_crashed_too_much (User|1|5),(Name|3),(PID|1|5)
30033 am_drop_process (PID|1|5)
30034 am_service_crashed_too_much (User|1|5),(Crash Count|1|1),(Component Name|3),(PID|1|5)
30035 am_schedule_service_restart (User|1|5),(Component Name|3),(Time|2|3)
30036 am_provider_lost_process (User|1|5),(Package Name|3),(UID|1|5),(Name|3)
30037 am_process_start_timeout (User|1|5),(PID|1|5),(UID|1|5),(Process Name|3)
30039 am_crash (User|1|5),(PID|1|5),(Process Name|3),(Flags|1|5),(Exception|3),(Message|3),(File|3),(Line|1|5)
30040 am_wtf (User|1|5),(PID|1|5),(Process Name|3),(Flags|1|5),(Tag|3),(Message|3)
30041 am_switch_user (id|1|5)
30043 am_set_resumed_activity (User|1|5),(Component Name|3),(Reason|3)
30044 am_focused_stack (User|1|5),(Focused Stack Id|1|5),(Last Focused Stack Id|1|5),(Reason|3)
30045 am_pre_boot (User|1|5),(Package|3)
30046 am_meminfo (Cached|2|2),(Free|2|2),(Zram|2|2),(Kernel|2|2),(Native|2|2)
30047 am_pss (Pid|1|5),(UID|1|5),(Process Name|3),(Pss|2|2),(Uss|2|2),(SwapPss|2|2),(Rss|2|2),(StatType|1|5),(ProcState|1|5),(TimeToCollect|2|2)
30048 am_stop_activity (User|1|5),(Token|1|5),(Component Name|3)
30049 am_on_stop_called (User|1|5),(Component Name|3),(Reason|3)
30050 am_mem_factor (Current|1|5),(Previous|1|5)
30051 am_user_state_changed (id|1|5),(state|1|5)
30052 am_uid_running (UID|1|5)
30053 am_uid_stopped (UID|1|5)
30054 am_uid_active (UID|1|5)
30055 am_uid_idle (UID|1|5)
30056 am_stop_idle_service (UID|1|5),(Component Name|3)
30057 am_on_create_called (User|1|5),(Component Name|3),(Reason|3)
30058 am_on_restart_called (User|1|5),(Component Name|3),(Reason|3)
30059 am_on_start_called (User|1|5),(Component Name|3),(Reason|3)
30060 am_on_destroy_called (User|1|5),(Component Name|3),(Reason|3)
30061 am_remove_task (Task ID|1|5), (Stack ID|1|5)
30062 am_on_activity_result_called (User|1|5),(Component Name|3),(Reason|3)
31000 wm_no_surface_memory (Window|3),(PID|1|5),(Operation|3)
31001 wm_task_created (TaskId|1|5),(StackId|1|5)
31002 wm_task_moved (TaskId|1|5),(ToTop|1),(Index|1)
31003 wm_task_removed (TaskId|1|5),(Reason|3)
31004 wm_stack_created (StackId|1|5)
31005 wm_home_stack_moved (ToTop|1)
31006 wm_stack_removed (StackId|1|5)
31007 wm_boot_animation_done (time|2|3)
32000 imf_force_reconnect_ime (IME|4),(Time Since Connect|2|3),(Showing|1|1)
33000 wp_wallpaper_crashed (component|3)
34000 device_idle (state|1|5), (reason|3)
34001 device_idle_step
34002 device_idle_wake_from_idle (is_idle|1|5), (reason|3)
34003 device_idle_on_start
34004 device_idle_on_phase (what|3)
34005 device_idle_on_complete
34006 device_idle_off_start (reason|3)
34007 device_idle_off_phase (what|3)
34008 device_idle_off_complete
34009 device_idle_light (state|1|5), (reason|3)
34010 device_idle_light_step
35000 auto_brightness_adj (old_lux|5),(old_brightness|5),(new_lux|5),(new_brightness|5)
36000 sysui_statusbar_touch (type|1),(x|1),(y|1),(disable1|1),(disable2|1)
36001 sysui_heads_up_status (key|3),(visible|1)
36002 sysui_fullscreen_notification (key|3)
36003 sysui_heads_up_escalation (key|3)
36004 sysui_status_bar_state (state|1),(keyguardShowing|1),(keyguardOccluded|1),(bouncerShowing|1),(secure|1),(currentlyInsecure|1)
36010 sysui_panelbar_touch (type|1),(x|1),(y|1),(enabled|1)
36020 sysui_notificationpanel_touch (type|1),(x|1),(y|1)
36021 sysui_lockscreen_gesture (type|1),(lengthDp|1),(velocityDp|1)
36030 sysui_quickpanel_touch (type|1),(x|1),(y|1)
36040 sysui_panelholder_touch (type|1),(x|1),(y|1)
36050 sysui_searchpanel_touch (type|1),(x|1),(y|1)
36060 sysui_recents_connection (type|1),(user|1)
36070 sysui_latency (action|1|6),(latency|1|3)
40000 volume_changed (stream|1), (prev_level|1), (level|1), (max_level|1), (caller|3)
40001 stream_devices_changed (stream|1), (prev_devices|1), (devices|1)
40100 camera_gesture_triggered (gesture_on_time|2|3), (sensor1_on_time|2|3), (sensor2_on_time|2|3), (event_extra|1|1)
50000 menu_item_selected (Menu type where 0 is options and 1 is context|1|5),(Menu item title|3)
50001 menu_opened (Menu type where 0 is options and 1 is context|1|5)
50020 connectivity_state_changed (type|1),(subtype|1),(state|1)
50021 wifi_state_changed (wifi_state|3)
50022 wifi_event_handled (wifi_event|1|5)
50023 wifi_supplicant_state_changed (supplicant_state|1|5)
50080 ntp_success (server|3),(rtt|2),(offset|2)
50081 ntp_failure (server|3),(msg|3)
50100 pdp_bad_dns_address (dns_address|3)
50101 pdp_radio_reset_countdown_triggered (out_packet_count|1|1)
50102 pdp_radio_reset (out_packet_count|1|1)
50103 pdp_context_reset (out_packet_count|1|1)
50104 pdp_reregister_network (out_packet_count|1|1)
50105 pdp_setup_fail (cause|1|5), (cid|1|5), (network_type|1|5)
50106 call_drop (cause|1|5), (cid|1|5), (network_type|1|5)
50107 data_network_registration_fail (op_numeric|1|5), (cid|1|5)
50108 data_network_status_on_radio_off (dc_state|3), (enable|1|5)
50109 pdp_network_drop (cid|1|5), (network_type|1|5)
50110 cdma_data_setup_failed (cause|1|5), (cid|1|5), (network_type|1|5)
50111 cdma_data_drop (cid|1|5), (network_type|1|5)
50112 gsm_rat_switched (cid|1|5), (network_from|1|5), (network_to|1|5)
50113 gsm_data_state_change (oldState|3), (newState|3)
50114 gsm_service_state_change (oldState|1|5), (oldGprsState|1|5), (newState|1|5), (newGprsState|1|5)
50115 cdma_data_state_change (oldState|3), (newState|3)
50116 cdma_service_state_change (oldState|1|5), (oldDataState|1|5), (newState|1|5), (newDataState|1|5)
50117 bad_ip_address (ip_address|3)
50118 data_stall_recovery_get_data_call_list (out_packet_count|1|1)
50119 data_stall_recovery_cleanup (out_packet_count|1|1)
50120 data_stall_recovery_reregister (out_packet_count|1|1)
50121 data_stall_recovery_radio_restart (out_packet_count|1|1)
50122 data_stall_recovery_radio_restart_with_prop (out_packet_count|1|1)
50123 gsm_rat_switched_new (cid|1|5), (network_from|1|5), (network_to|1|5)
50125 exp_det_sms_denied_by_user (app_signature|3)
50128 exp_det_sms_sent_by_user (app_signature|3)
51100 netstats_mobile_sample (dev_rx_bytes|2|2),(dev_tx_bytes|2|2),(dev_rx_pkts|2|1),(dev_tx_pkts|2|1),(xt_rx_bytes|2|2),(xt_tx_bytes|2|2),(xt_rx_pkts|2|1),(xt_tx_pkts|2|1),(uid_rx_bytes|2|2),(uid_tx_bytes|2|2),(uid_rx_pkts|2|1),(uid_tx_pkts|2|1),(trusted_time|2|3)
51101 netstats_wifi_sample (dev_rx_bytes|2|2),(dev_tx_bytes|2|2),(dev_rx_pkts|2|1),(dev_tx_pkts|2|1),(xt_rx_bytes|2|2),(xt_tx_bytes|2|2),(xt_rx_pkts|2|1),(xt_tx_pkts|2|1),(uid_rx_bytes|2|2),(uid_tx_bytes|2|2),(uid_rx_pkts|2|1),(uid_tx_pkts|2|1),(trusted_time|2|3)
51200 lockdown_vpn_connecting (egress_net|1)
51201 lockdown_vpn_connected (egress_net|1)
51202 lockdown_vpn_error (egress_net|1)
51300 config_install_failed (dir|3)
51400 ifw_intent_matched (Intent Type|1|5),(Component Name|3),(Caller Uid|1|5),(Caller Pkg Count|1|1),(Caller Pkgs|3),(Action|3),(MIME Type|3),(URI|3),(Flags|1|5)
51500 idle_maintenance_window_start (time|2|3), (lastUserActivity|2|3), (batteryLevel|1|6), (batteryCharging|1|5)
51501 idle_maintenance_window_finish (time|2|3), (lastUserActivity|2|3), (batteryLevel|1|6), (batteryCharging|1|5)
51600 timezone_trigger_check (token|3)
51610 timezone_request_install (token|3)
51611 timezone_install_started (token|3)
51612 timezone_install_complete (token|3), (result|1)
51620 timezone_request_uninstall (token|3)
51621 timezone_uninstall_started (token|3)
51622 timezone_uninstall_complete (token|3), (result|1)
51630 timezone_request_nothing (token|3)
51631 timezone_nothing_complete (token|3)
51690 timezone_check_trigger_received (token_bytes|3)
51691 timezone_check_read_from_data_app (token_bytes|3)
51692 timezone_check_request_uninstall (token_bytes|3)
51693 timezone_check_request_install (token_bytes|3)
51694 timezone_check_request_nothing (token_bytes|3), (success|1)
52000 db_sample (db|3),(sql|3),(time|1|3),(blocking_package|3),(sample_percent|1|6)
52001 http_stats (useragent|3),(response|2|3),(processing|2|3),(tx|1|2),(rx|1|2)
52002 content_query_sample (uri|3),(projection|3),(selection|3),(sortorder|3),(time|1|3),(blocking_package|3),(sample_percent|1|6)
52003 content_update_sample (uri|3),(operation|3),(selection|3),(time|1|3),(blocking_package|3),(sample_percent|1|6)
52004 binder_sample (descriptor|3),(method_num|1|5),(time|1|3),(blocking_package|3),(sample_percent|1|6)
52100 unsupported_settings_query (uri|3),(selection|3),(whereArgs|3)
52101 persist_setting_error (message|3)
53000 harmful_app_warning_uninstall (package_name|3)
53001 harmful_app_warning_launch_anyway (package_name|3)
60000 viewroot_draw (Draw time|1|3)
60001 viewroot_layout (Layout time|1|3)
60002 view_build_drawing_cache (View created drawing cache|1|5)
60003 view_use_drawing_cache (View drawn using bitmap cache|1|5)
60100 sf_frame_dur (window|3),(dur0|1),(dur1|1),(dur2|1),(dur3|1),(dur4|1),(dur5|1),(dur6|1)
60110 sf_stop_bootanim (time|2|3)
65537 exp_det_netlink_failure (uid|1)
70000 screen_toggled (screen_state|1|5)
70101 browser_zoom_level_change (start level|1|5),(end level|1|5),(time|2|3)
70102 browser_double_tap_duration (duration|1|3),(time|2|3)
70150 browser_snap_center
70151 exp_det_attempt_to_call_object_getclass (app_signature|3)
70200 aggregation (aggregation time|2|3)
70201 aggregation_test (field1|1|2),(field2|1|2),(field3|1|2),(field4|1|2),(field5|1|2)
70220 gms_unknown
70301 phone_ui_enter
70302 phone_ui_exit
70303 phone_ui_button_click (text|3)
70304 phone_ui_ringer_query_elapsed
70305 phone_ui_multiple_query
71001 qsb_start (name|3),(version|1),(start_method|3),(latency|1|3),(search_source|3),(enabled_sources|3),(on_create_latency|1|3)
71002 qsb_click (id|2),(suggestions|3),(queried_sources|3),(num_chars|1),(click_type|1)
71003 qsb_search (search_source|3),(method|1),(num_chars|1)
71004 qsb_voice_search (search_source|3)
71005 qsb_exit (suggestions|3),(num_chars|1)
71006 qsb_latency (corpus|3),(latency|1|3),(num_chars|1)
75000 sqlite_mem_alarm_current (current|1|2)
75001 sqlite_mem_alarm_max (max|1|2)
75002 sqlite_mem_alarm_alloc_attempt (attempts|1|4)
75003 sqlite_mem_released (Memory released|1|2)
75004 sqlite_db_corrupt (Database file corrupt|3)
76001 tts_speak_success (engine|3),(caller_uid|1),(caller_pid|1),(length|1),(locale|3),(rate|1),(pitch|1),(engine_latency|2|3),(engine_total|2|3),(audio_latency|2|3)
76002 tts_speak_failure (engine|3),(caller_uid|1),(caller_pid|1),(length|1),(locale|3),(rate|1),(pitch|1)
76003 tts_v2_speak_success (engine|3),(caller_uid|1),(caller_pid|1),(length|1),(request_config|3),(engine_latency|2|3),(engine_total|2|3),(audio_latency|2|3)
76004 tts_v2_speak_failure (engine|3),(caller_uid|1),(caller_pid|1),(length|1),(request_config|3), (statusCode|1)
78001 exp_det_dispatchCommand_overflow
80100 bionic_event_memcpy_buffer_overflow (uid|1)
80105 bionic_event_strcat_buffer_overflow (uid|1)
80110 bionic_event_memmov_buffer_overflow (uid|1)
80115 bionic_event_strncat_buffer_overflow (uid|1)
80120 bionic_event_strncpy_buffer_overflow (uid|1)
80125 bionic_event_memset_buffer_overflow (uid|1)
80130 bionic_event_strcpy_buffer_overflow (uid|1)
80200 bionic_event_strcat_integer_overflow (uid|1)
80205 bionic_event_strncat_integer_overflow (uid|1)
80300 bionic_event_resolver_old_response (uid|1)
80305 bionic_event_resolver_wrong_server (uid|1)
80310 bionic_event_resolver_wrong_query (uid|1)
81002 dropbox_file_copy (FileName|3),(Size|1),(Tag|3)
90100 exp_det_cert_pin_failure (certs|4)
90200 lock_screen_type (type|3)
90201 exp_det_device_admin_activated_by_user (app_signature|3)
90202 exp_det_device_admin_declined_by_user (app_signature|3)
90203 exp_det_device_admin_uninstalled_by_user (app_signature|3)
90204 settings_latency (action|1|6),(latency|1|3)
201001 system_update (status|1|5),(download_result|1|5),(bytes|2|2),(url|3)
201002 system_update_user (action|3)
202001 vending_reconstruct (changes|1)
202901 transaction_event (data|3)
203001 sync_details (authority|3),(send|1|2),(recv|1|2),(details|3)
203002 google_http_request (elapsed|2|3),(status|1),(appname|3),(reused|1)
204001 gtalkservice (eventType|1)
204002 gtalk_connection (status|1)
204003 gtalk_conn_close (status|1),(duration|1)
204004 gtalk_heartbeat_reset (interval_and_nt|1),(ip|3)
204005 c2dm (packet_type|1),(persistent_id|3),(stream_id|1),(last_stream_id|1)
205001 setup_server_timeout
205002 setup_required_captcha (action|3)
205003 setup_io_error (status|3)
205004 setup_server_error
205005 setup_retries_exhausted
205006 setup_no_data_network
205007 setup_completed
205008 gls_account_tried (status|1)
205009 gls_account_saved (status|1)
205010 gls_authenticate (status|1),(service|3)
205011 google_mail_switch (direction|1)
206001 snet (payload|3)
206003 exp_det_snet (payload|3)
208000 metrics_heartbeat
210001 security_adb_shell_interactive
210002 security_adb_shell_command (command|3)
210003 security_adb_sync_recv (path|3)
210004 security_adb_sync_send (path|3)
210005 security_app_process_start (process|3),(start_time|2|3),(uid|1),(pid|1),(seinfo|3),(sha256|3)
210006 security_keyguard_dismissed
210007 security_keyguard_dismiss_auth_attempt (success|1),(method_strength|1)
210008 security_keyguard_secured
210009 security_os_startup (boot_state|3),(verity_mode|3)
210010 security_os_shutdown
210011 security_logging_started
210012 security_logging_stopped
210013 security_media_mounted (path|3),(label|3)
210014 security_media_unmounted (path|3),(label|3)
210015 security_log_buffer_size_critical
210016 security_password_expiration_set (package|3),(admin_user|1),(target_user|1),(timeout|2|3)
210017 security_password_complexity_set (package|3),(admin_user|1),(target_user|1),(length|1),(quality|1),(num_letters|1),(num_non_letters|1),(num_numeric|1),(num_uppercase|1),(num_lowercase|1),(num_symbols|1)
210018 security_password_history_length_set (package|3),(admin_user|1),(target_user|1),(length|1)
210019 security_max_screen_lock_timeout_set (package|3),(admin_user|1),(target_user|1),(timeout|2|3)
210020 security_max_password_attempts_set (package|3),(admin_user|1),(target_user|1),(num_failures|1)
210021 security_keyguard_disabled_features_set (package|3),(admin_user|1),(target_user|1),(features|1)
210022 security_remote_lock (package|3),(admin_user|1),(target_user|1)
210023 security_wipe_failed (package|3),(admin_user|1)
210024 security_key_generated (success|1),(key_id|3),(uid|1)
210025 security_key_imported (success|1),(key_id|3),(uid|1)
210026 security_key_destroyed (success|1),(key_id|3),(uid|1)
210027 security_user_restriction_added (package|3),(admin_user|1),(restriction|3)
210028 security_user_restriction_removed (package|3),(admin_user|1),(restriction|3)
210029 security_cert_authority_installed (success|1),(subject|3)
210030 security_cert_authority_removed (success|1),(subject|3)
210031 security_crypto_self_test_completed (success|1)
210032 security_key_integrity_violation (key_id|3),(uid|1)
210033 security_cert_validation_failure (reason|3)
230000 service_manager_stats (call_count|1),(total_time|1|3),(duration|1|3)
230001 service_manager_slow (time|1|3),(service|3)
275534 notification_unautogrouped (key|3)
300000 arc_system_event (event|3)
524287 sysui_view_visibility (category|1|5),(visible|1|6)
524288 sysui_action (category|1|5),(pkg|3)
524290 sysui_count (name|3),(increment|1)
524291 sysui_histogram (name|3),(bucket|1)
524292 sysui_multi_action (content|4)
525000 commit_sys_config_file (name|3),(time|2|3)
1010000 bt_hci_timeout (opcode|1)
1010001 bt_config_source (opcode|1)
1010002 bt_hci_unknown_type (hci_type|1)
1397638484 snet_event_log (subtag|3) (uid|1) (message|3)
1937006964 stats_log (atom_id|1|5),(data|4)
