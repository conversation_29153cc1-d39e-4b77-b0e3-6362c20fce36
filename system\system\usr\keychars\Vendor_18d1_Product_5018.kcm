# Copyright (C) 2015 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

#
# Key character map for Google Pixel C Keyboard
#

type FULL

### Basic QWERTY keys ###

key A {
    label:                              'A'
    base:                               'a'
    shift, capslock:                    'A'
}

key B {
    label:                              'B'
    base:                               'b'
    shift, capslock:                    'B'
}

key C {
    label:                              'C'
    base:                               'c'
    shift, capslock:                    'C'
    alt:                                '\u00e7'
    shift+alt:                          '\u00c7'
}

key D {
    label:                              'D'
    base:                               'd'
    shift, capslock:                    'D'
}

key E {
    label:                              'E'
    base:                               'e'
    shift, capslock:                    'E'
    alt:                                '\u0301'
}

key F {
    label:                              'F'
    base:                               'f'
    shift, capslock:                    'F'
}

key G {
    label:                              'G'
    base:                               'g'
    shift, capslock:                    'G'
}

key H {
    label:                              'H'
    base:                               'h'
    shift, capslock:                    'H'
}

key I {
    label:                              'I'
    base:                               'i'
    shift, capslock:                    'I'
    alt:                                '\u0302'
}

key J {
    label:                              'J'
    base:                               'j'
    shift, capslock:                    'J'
}

key K {
    label:                              'K'
    base:                               'k'
    shift, capslock:                    'K'
}

key L {
    label:                              'L'
    base:                               'l'
    shift, capslock:                    'L'
}

key M {
    label:                              'M'
    base:                               'm'
    shift, capslock:                    'M'
}

key N {
    label:                              'N'
    base:                               'n'
    shift, capslock:                    'N'
    alt:                                '\u0303'
}

key O {
    label:                              'O'
    base:                               'o'
    shift, capslock:                    'O'
    ralt:                               '['
    ralt+shift:                         '{'
}

key P {
    label:                              'P'
    base:                               'p'
    shift, capslock:                    'P'
    ralt:                               ']'
    ralt+shift:                         '}'
}

key Q {
    label:                              'Q'
    base:                               'q'
    shift, capslock:                    'Q'
}

key R {
    label:                              'R'
    base:                               'r'
    shift, capslock:                    'R'
}

key S {
    label:                              'S'
    base:                               's'
    shift, capslock:                    'S'
    alt:                                '\u00df'
}

key T {
    label:                              'T'
    base:                               't'
    shift, capslock:                    'T'
}

key U {
    label:                              'U'
    base:                               'u'
    shift, capslock:                    'U'
    alt:                                '\u0308'
}

key V {
    label:                              'V'
    base:                               'v'
    shift, capslock:                    'V'
}

key W {
    label:                              'W'
    base:                               'w'
    shift, capslock:                    'W'
}

key X {
    label:                              'X'
    base:                               'x'
    shift, capslock:                    'X'
}

key Y {
    label:                              'Y'
    base:                               'y'
    shift, capslock:                    'Y'
}

key Z {
    label:                              'Z'
    base:                               'z'
    shift, capslock:                    'Z'
}

key 0 {
    label:                              '0'
    base:                               '0'
    shift:                              ')'
}

key 1 {
    label:                              '1'
    base:                               '1'
    shift:                              '!'
    ralt:                               replace ESCAPE
}

key 2 {
    label:                              '2'
    base:                               '2'
    shift:                              '@'
    ralt:                               '`'
    ralt+shift:                         '~'
}

key 3 {
    label:                              '3'
    base:                               '3'
    shift:                              '#'
}

key 4 {
    label:                              '4'
    base:                               '4'
    shift:                              '$'
}

key 5 {
    label:                              '5'
    base:                               '5'
    shift:                              '%'
}

key 6 {
    label:                              '6'
    base:                               '6'
    shift:                              '^'
    alt+shift:                          '\u0302'
}

key 7 {
    label:                              '7'
    base:                               '7'
    shift:                              '&'
}

key 8 {
    label:                              '8'
    base:                               '8'
    shift:                              '*'
}

key 9 {
    label:                              '9'
    base:                               '9'
    shift:                              '('
}

key SPACE {
    label:                              ' '
    base:                               ' '
    alt, meta:                          fallback SEARCH
    ctrl:                               fallback LANGUAGE_SWITCH
}

key ENTER {
    label:                              '\n'
    base:                               '\n'
}

key TAB {
    label:                              '\t'
    base:                               '\t'
}

key COMMA {
    label:                              ','
    base:                               ','
    shift:                              '<'
}

key PERIOD {
    label:                              '.'
    base:                               '.'
    shift:                              '>'
}

key SLASH {
    label:                              '/'
    base:                               '/'
    shift:                              '?'
}

key MINUS {
    label:                              '-'
    base:                               '-'
    shift:                              '_'
}

key EQUALS {
    label:                              '='
    base:                               '='
    shift:                              '+'
    ralt:                               '\\'
    ralt+shift:                         '|'
}

key SEMICOLON {
    label:                              ';'
    base:                               ';'
    shift:                              ':'
}

key APOSTROPHE {
    label:                              '\''
    base:                               '\''
    shift:                              '"'
}

### Non-printing keys ###

key ESCAPE {
    base:                               fallback BACK
    alt, meta:                          fallback HOME
    ctrl:                               fallback MENU
}
