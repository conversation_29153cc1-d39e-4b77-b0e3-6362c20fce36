% copyright: Copyright (c) 1994-2008, <PERSON><PERSON>
% title: Bulgarian hyphenation patterns
% version: 1.7, July 2008
% language:
%     name: Bulgarian
%     code: bg
% notice: >
%     This file is part of the hyph-utf8 package.
%     See http://www.hyphenation.org for more information.
% authors:
%   -
%     name:    <PERSON><PERSON>
%     contact: manchester.ac.uk:georgi.boshnakov
% licence:
%     name: MIT
%     url: https://opensource.org/licenses/MIT
%     text: >
%             Permission is hereby granted, free of charge, to any person
%             obtaining a copy of this software and associated documentation
%             files (the "Software"), to deal in the Software without
%             restriction, including without limitation the rights to use,
%             copy, modify, merge, publish, distribute, sublicense, and/or sell
%             copies of the Software, and to permit persons to whom the
%             Software is furnished to do so, subject to the following
%             conditions:
%
%             The above copyright notice and this permission notice shall be
%             included in all copies or substantial portions of the Software.
%
%             THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
%             EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
%             OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
%             NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
%             HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
%             WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
%             FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
%             OTHER DEALINGS IN THE SOFTWARE.
% hyphenmins:
%     for_typesetting:
%         left: 2
%         right: 2
% changes:
%   -
%     date:        2008-06
%     description: Changed encoding to UTF-8
%   -
%     date:        2006-05
%     description: Added copyright notice
%   -
%     date:        2000-06
%     description: Minor changes
%   -
%     date:        1994
%     description: First version
% ==========================================
% Note: The original name of this file was 'bghyphsi.tex' which is
%   part of the package 'bghyphen'. The package 'bghyphen' is now
%   obsolete but it is still available on CTAN and currently (June 2008)
%   gives the same hyphenation results.
%
%
%
% To make TeX use these patterns:
%
%    (1) Make sure that the hyph-utf8 package is present in your TeX
%        system.
%
%    (2) generate the necessary formats (TeX, LaTeX, pdfLaTeX, etc),
%        instructing TeX to load 'loadhyph-bg.tex' for Bulgarian
%        hyphenation.
%
% The LaTeX babel package sets \lefthyphenmin and \righthyphenmin to 2
%   when the language is switched to Bulgarian.  Developers who write
%   support for Bulgarian outside LaTeX and/or babel need to take care
%   of this.
%
