<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2014 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<permissions>

    <!-- These are the hardware components that all television devices must
         include.  Devices with optional hardware must also include extra hardware
         files.
    -->
    <feature name="android.hardware.audio.output" />
    <feature name="android.hardware.location" />
    <feature name="android.hardware.location.network" />
    <feature name="android.hardware.screen.landscape" />
    <feature name="android.hardware.screen.portrait" />
    
    <feature name="nrdp.modelgroup" />
    <feature name="android.software.backup" />
    
    <feature name="android.hardware.ethernet"/>
    <feature name="android.hardware.sensor.accelerometer"/>
    <feature name="android.hardware.touchscreen"/>
    <feature name="android.software.device_admin"/>
    <feature name="android.software.home_screen"/>
    <feature name="android.software.live_wallpaper"/>
    <feature name="android.software.managed_users"/>
    <feature name="android.hardware.microphone"/>
    <feature name="android.software.voice_recognizers"/>
    <feature name="android.software.midi"/>
    <feature name="android.software.pppoe" />

    <feature name="android.hardware.nfc"/>
    <feature name="com.google.android.feature.PIXEL_EXPERIENCE"/>
    <feature name="android.software.autofill"/>
<!--
    <feature name="android.software.leanback" />
    <feature name="android.hardware.television" />
    <feature name="android.software.leanback_only" />
    -->
    <feature name="android.software.live_tv" />
    <feature name="android.software.picture_in_picture" />
    <feature name="android.software.voice_recognizers" />
    <feature name="android.software.pppoe" />
    <feature name="android.software.app_widgets" />
    <!-- add some gms google features for netflix -->
    <feature name="com.google.android.apps.dialer.SUPPORTED" />
    <feature name="com.google.android.feature.EXCHANGE_6_2" />
    <feature name="com.google.android.feature.GOOGLE_BUILD" />
    <feature name="com.google.android.feature.GOOGLE_EXPERIENCE" />
    <feature name="com.google.android.feature.PIXEL_2017_EXPERIENCE" />
    <feature name="com.google.android.feature.PIXEL_2018_EXPERIENCE" />
    <feature name="com.google.android.feature.TURBO_PRELOAD" />
    <feature name="com.google.android.feature.WELLBEING" />
    <feature name="android.software.verified_boot" />

    <feature name="android.hardware.opengles.aep" />
    <feature name="android.hardware.television" />
    <!-- add some gms google features for phone play store
    <feature name="android.hardware.telephony" />
    <feature name="android.hardware.telephony.cdma" />
    <feature name="android.hardware.telephony.gsm" /> -->

</permissions>
