#!/system/bin/sh
if ! applypatch -c EMMC:/dev/block/by-name/recovery:41891840:cee24208a801ea158703c71d5750a99f805d7b9a; then
  applypatch  EMMC:/dev/block/by-name/boot:33464320:6bc1448e8cbb7b66a3c1882474dd881073885eea EMMC:/dev/block/by-name/recovery cee24208a801ea158703c71d5750a99f805d7b9a 41891840 6bc1448e8cbb7b66a3c1882474dd881073885eea:/system/recovery-from-boot.p && log -t recovery "Installing new recovery image: succeeded" || log -t recovery "Installing new recovery image: failed"
else
  log -t recovery "Recovery image already installed"
fi
