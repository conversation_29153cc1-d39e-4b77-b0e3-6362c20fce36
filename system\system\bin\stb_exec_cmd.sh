#!/system/bin/sh

LOG="/system/bin/log -t stb_exec_cmd"
ECHO()
{
    echo $*
    #$LOG "$*"
}

traceroute_enable=`getprop vendor.stb.exec_traceroute`
if [ "$traceroute_enable" == "1" ]; then
    ECHO "start traceroute"
    traceroute_str=`getprop sys.stb.traceroute_str`
    rm -rf /sdcard/traceroute_result.txt
    busybox traceroute $traceroute_str > /sdcard/traceroute_result.txt
    setprop vendor.stb.exec_traceroute 0
    exit 0
fi

capture_enable=`getprop persist.sys.stb.capture.boot`
capture_filepath=`getprop persist.sys.stb.capture.filepath`
capture_skip_ip=`getprop persist.sys.stb.capture.ip`
capture_skip_port=`getprop persist.sys.stb.capture.port`
capture_duration=`getprop persist.sys.stb.capture.duration`
capture_maxsize=`getprop persist.sys.stb.capture.maxsize`

logcat_enable=`getprop persist.sys.stb.logcat.boot`
logcat_filepath=`getprop persist.sys.stb.logcat.filepath`
logcat_duration=`getprop persist.sys.stb.logcat.duration`
logcat_maxsize=`getprop persist.sys.stb.logcat.maxsize`
logcat_level=`getprop persist.sys.stb.logcat.level`

netdev=proc/net/dev
last_rx_pkts=0
last_tx_pkts=0
pkts_threshold=10000
need_recovery=0

if [ $capture_enable -eq 1 ] ; then
	ECHO "start tcpdump pcap  to $capture_filepath, capture_skip_ip: $capture_skip_ip, capture_skip_port: $capture_skip_port, time: $capture_duration, maxsize: $capture_maxsize"
	capture_time=0
	tcpdump -i eth0 -s 0 -w $capture_filepath &
fi

if [ $logcat_enable -eq 1 ] ; then
	ECHO "start catch logcat log to $logcat_filepath, time: $logcat_duration, maxsize: $logcat_maxsize"
	logcat_time=0
	logcat -v time > $logcat_filepath &
fi

while [ true ] ; do
	sleep 1
    capture_enable=`getprop persist.sys.stb.capture.boot`
	logcat_enable=`getprop persist.sys.stb.logcat.boot`
	if [ $capture_enable -ne 1 ] && [ $logcat_enable -ne 1 ]; then
	   ECHO "stop catch tcpdump && logcat cmd +++!!"
	   break
	fi

	if [ $capture_enable -eq 1 ] ; then
		if [ $capture_duration -gt 0 ] ; then
			capture_time=$(($capture_time + 1))
			ECHO "++++ capture tcpdump log to $capture_filepath,capture_time:$capture_time ++++"
			if [ $capture_time -gt $capture_duration ] ; then
			   killall tcpdump
			   ECHO "stop catch tcpdump log to $capture_filepath!!"
			   setprop persist.sys.stb.capture.boot 0
			fi
		fi

		if [ $capture_maxsize -gt 0 ] ; then
			log_size=`du -h $capture_filepath | awk '{print $1}'`
			last_char=${log_size: -1}
			if [ "$last_char" != "K" ] ; then
                if [ "$last_char" != "G" ] ; then
				    log_size=${log_size%?}
				else
				    log_size=${log_size%?}
					log_size=$(($log_size *1024))
				fi
				ECHO "++++ capture tcpdump log to $capture_filepath,log_size:$log_size ++++"
				if [ `expr $log_size \> $capture_maxsize` -eq 0 ] ; then
				   killall tcpdump
				   ECHO "stop capture tcpdump log to $capture_filepath!!"
				   setprop persist.sys.stb.capture.boot 0
				fi
			fi
		fi
	fi 
	if [ $logcat_enable -eq 1 ] ; then
	ECHO "=====  logcat_enable:$logcat_enable ==== "
		if [ $logcat_duration -gt 0 ] ; then
			logcat_time=$(($logcat_time + 1))
			ECHO "===== catch logcat log to $logcat_filepath,logcat_time:$logcat_time ===="
			if [ $logcat_time -gt $logcat_duration ] ; then
			   killall logcat
			   ECHO "stop catch logcat log to $logcat_filepath!!"
			   setprop persist.sys.stb.logcat.boot 0
			fi
		fi
		if [ $logcat_maxsize -gt 0 ] ; then
			log_size=`du -h $logcat_filepath | awk '{print $1}'`
			last_char=${log_size: -1}
			ECHO "=====  logcat_enable:$logcat_enable ====logcat_maxsize:$logcat_maxsize ,log_size:$log_size"
			if [ "$last_char" != "K" ] ; then
				if [ "$last_char" != "G" ] ; then
					log_size=${log_size%?}
				else
					log_size=${log_size%?}
					log_size=$(($log_size *1024))
				fi
				ECHO "===== catch logcat log to $logcat_filepath,log_size:$log_size ===="
				if [ `expr $log_size \> $logcat_maxsize` -eq 0 ] ; then
				   killall logcat
				   ECHO "stop catch logcat log to $logcat_filepath!!"
				   setprop persist.sys.stb.logcat.boot 0
				fi
			fi
		fi
	fi
done
#if [ $capture_enable -eq 1 ] ; then
	killall tcpdump
	ECHO "stop tcpdump log to $capture_filepath!!"
#fi
#if [ $logcat_enable -eq 1 ] ; then
	killall logcat
	ECHO "stop catch logcat log to $logcat_filepath!!"
#fi
