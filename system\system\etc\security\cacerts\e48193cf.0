-----<PERSON><PERSON><PERSON> CERTIFICATE-----
MIIDTDCCAjSgAwIBAgIId3cGJyapsXwwDQYJKoZIhvcNAQELBQAwRDELMAkGA1UE
BhMCVVMxFDASBgNVBAoMC0FmZmlybVRydXN0MR8wHQYDVQQDDBZBZmZpcm1UcnVz
dCBDb21tZXJjaWFsMB4XDTEwMDEyOTE0MDYwNloXDTMwMTIzMTE0MDYwNlowRDEL
MAkGA1UEBhMCVVMxFDASBgNVBAoMC0FmZmlybVRydXN0MR8wHQYDVQQDDBZBZmZp
cm1UcnVzdCBDb21tZXJjaWFsMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKC
AQEA9htPZwcroRX1BiLLHwGy43NFBkRJLLtJJRTWzsO3qyxPxkEylFf6EqdbDuKP
Hx6GGaeqtS25Xw2Kwq+FNXkyLbscYjfysVtKPcrNcV/pQr6U6Mje+SJIZMblq8Yr
ba0F8PrVC8+a5fBQpIs7R6UjW3p6+DM/uO+Zl+MgwdYoic+U+7lF7eNAFxHUdPAL
MeIrJmqbTFeurCA+ukV6BfO9m2kVrn1OIGPENXY6BwLJN/3HR+7o8XYdcxXyl6S1
yHp52UKqK39c/s4mT6NmgTWvRLpUHhwwMmWd5jyTXlBOeuM61G7MGvv50jeuJCqr
VwMiKA1JdX+3KNp1v47j3A55MQIDAQABo0IwQDAdBgNVHQ4EFgQUnZPGU4teyq8/
nx4P5ZmVvCT2lI8wDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYwDQYJ
KoZIhvcNAQELBQADggEBAFis9AQOzcAN/wr91LoWXym9e2iZWEnStB03TX8nfUYG
XUPGhi4+c7ImfU+TqbbEKpqrIZcUsd6M06uJFdhrJNTxFq7YpFzUf1GO7RgBsZNj
vbz4YYCanrHOQnDiqX0GJX0nof5v7LMeJNrjS1UaADs1tDvZ110w/YETifLCBivt
Z8SOyUOyXGsViQK8YvxO8rUzqrJv0wqiUOP2O+guRMLbZjipM1ZI8W0bM40NjD9g
N53Tym1+NH4Nn3J2ixufcv1SNUFFApYvHLKac0khsUlHRUe072o0EclNmsxZt9YC
nlpOZbWUrhvfKbAW8b8Angc6F2S1BLUjIZkKlTuXfO8=
-----END CERTIFICATE-----
Certificate:
    Data:
        Version: 3 (0x2)
        Serial Number: 8608355977964138876 (0x7777062726a9b17c)
    Signature Algorithm: sha256WithRSAEncryption
        Issuer: C=US, O=AffirmTrust, CN=AffirmTrust Commercial
        Validity
            Not Before: Jan 29 14:06:06 2010 GMT
            Not After : Dec 31 14:06:06 2030 GMT
        Subject: C=US, O=AffirmTrust, CN=AffirmTrust Commercial
        Subject Public Key Info:
            Public Key Algorithm: rsaEncryption
                Public-Key: (2048 bit)
                Modulus:
                    00:f6:1b:4f:67:07:2b:a1:15:f5:06:22:cb:1f:01:
                    b2:e3:73:45:06:44:49:2c:bb:49:25:14:d6:ce:c3:
                    b7:ab:2c:4f:c6:41:32:94:57:fa:12:a7:5b:0e:e2:
                    8f:1f:1e:86:19:a7:aa:b5:2d:b9:5f:0d:8a:c2:af:
                    85:35:79:32:2d:bb:1c:62:37:f2:b1:5b:4a:3d:ca:
                    cd:71:5f:e9:42:be:94:e8:c8:de:f9:22:48:64:c6:
                    e5:ab:c6:2b:6d:ad:05:f0:fa:d5:0b:cf:9a:e5:f0:
                    50:a4:8b:3b:47:a5:23:5b:7a:7a:f8:33:3f:b8:ef:
                    99:97:e3:20:c1:d6:28:89:cf:94:fb:b9:45:ed:e3:
                    40:17:11:d4:74:f0:0b:31:e2:2b:26:6a:9b:4c:57:
                    ae:ac:20:3e:ba:45:7a:05:f3:bd:9b:69:15:ae:7d:
                    4e:20:63:c4:35:76:3a:07:02:c9:37:fd:c7:47:ee:
                    e8:f1:76:1d:73:15:f2:97:a4:b5:c8:7a:79:d9:42:
                    aa:2b:7f:5c:fe:ce:26:4f:a3:66:81:35:af:44:ba:
                    54:1e:1c:30:32:65:9d:e6:3c:93:5e:50:4e:7a:e3:
                    3a:d4:6e:cc:1a:fb:f9:d2:37:ae:24:2a:ab:57:03:
                    22:28:0d:49:75:7f:b7:28:da:75:bf:8e:e3:dc:0e:
                    79:31
                Exponent: 65537 (0x10001)
        X509v3 extensions:
            X509v3 Subject Key Identifier: 
                9D:93:C6:53:8B:5E:CA:AF:3F:9F:1E:0F:E5:99:95:BC:24:F6:94:8F
            X509v3 Basic Constraints: critical
                CA:TRUE
            X509v3 Key Usage: critical
                Certificate Sign, CRL Sign
    Signature Algorithm: sha256WithRSAEncryption
         58:ac:f4:04:0e:cd:c0:0d:ff:0a:fd:d4:ba:16:5f:29:bd:7b:
         68:99:58:49:d2:b4:1d:37:4d:7f:27:7d:46:06:5d:43:c6:86:
         2e:3e:73:b2:26:7d:4f:93:a9:b6:c4:2a:9a:ab:21:97:14:b1:
         de:8c:d3:ab:89:15:d8:6b:24:d4:f1:16:ae:d8:a4:5c:d4:7f:
         51:8e:ed:18:01:b1:93:63:bd:bc:f8:61:80:9a:9e:b1:ce:42:
         70:e2:a9:7d:06:25:7d:27:a1:fe:6f:ec:b3:1e:24:da:e3:4b:
         55:1a:00:3b:35:b4:3b:d9:d7:5d:30:fd:81:13:89:f2:c2:06:
         2b:ed:67:c4:8e:c9:43:b2:5c:6b:15:89:02:bc:62:fc:4e:f2:
         b5:33:aa:b2:6f:d3:0a:a2:50:e3:f6:3b:e8:2e:44:c2:db:66:
         38:a9:33:56:48:f1:6d:1b:33:8d:0d:8c:3f:60:37:9d:d3:ca:
         6d:7e:34:7e:0d:9f:72:76:8b:1b:9f:72:fd:52:35:41:45:02:
         96:2f:1c:b2:9a:73:49:21:b1:49:47:45:47:b4:ef:6a:34:11:
         c9:4d:9a:cc:59:b7:d6:02:9e:5a:4e:65:b5:94:ae:1b:df:29:
         b0:16:f1:bf:00:9e:07:3a:17:64:b5:04:b5:23:21:99:0a:95:
         3b:97:7c:ef
SHA1 Fingerprint=F9:B5:B6:32:45:5F:9C:BE:EC:57:5F:80:DC:E9:6E:2C:C7:B2:78:B7
