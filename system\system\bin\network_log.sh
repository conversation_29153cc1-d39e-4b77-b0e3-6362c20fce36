#!/system/bin/sh

print_space()
{
	echo "" >> $log_file
	echo "============================================ $1 =============================================" >> $log_file
	echo "" >> $log_file
}

log_file=/data/network_log.txt

echo ""
echo "=========== start dump network debug info to /data/network_log.txt ==========="
echo ""

rm $log_file

print_space "ifconfig"
ifconfig >> $log_file

print_space "ip rule"
ip rule >> $log_file

print_space "ip route show table wlan0"
ip route show table wlan0 >> $log_file

print_space "ip route show table eth0"
ip route show table eth0 >> $log_file

print_space "ip route show table ppp0"
ip route show table ppp0 >> $log_file

print_space "ip route show table main"
ip route show table main >> $log_file

print_space "ip -6 route show table wlan0"
ip -6 route show table wlan0 >> $log_file
print_space "ip -6 route show table eth0"
ip -6 route show table eth0 >> $log_file
print_space "ip -6 route show table ppp0"
ip -6 route show table ppp0 >> $log_file
print_space "ip -6 route show table main"
ip -6 route show table main >> $log_file

print_space "dump /data/misc/ethernet/ipconfig.txt"
busybox hd /data/misc/ethernet/ipconfig.txt >> $log_file

print_space "getprop"
getprop >> $log_file

print_space "dumpsys connectivity"
dumpsys connectivity >> $log_file

print_space "dumpsys wifi"
dumpsys wifi >> $log_file

print_space "dumpsys bluetooth_manager"
dumpsys bluetooth_manager >> $log_file

print_space "dmesg"
dmesg >> $log_file

print_space "logcat"
logcat -d >> $log_file

echo ""
echo "=========== finish dump ==========="
echo ""

