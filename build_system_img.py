#!/usr/bin/env python3
"""
Android System Image Builder
Builds system.img from unpacked system directory
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_tools():
    """Check if required tools are available"""
    tools = ['make_ext4fs', 'mke2fs']
    available_tool = None
    
    for tool in tools:
        if shutil.which(tool):
            available_tool = tool
            break
    
    return available_tool

def get_system_info():
    """Parse system_info.txt to get filesystem parameters"""
    info_file = Path('system_info.txt')
    if not info_file.exists():
        print("Error: system_info.txt not found!")
        return None
    
    info = {}
    with open(info_file, 'r') as f:
        for line in f:
            if 'Block count:' in line:
                info['block_count'] = int(line.split(':')[1].strip())
            elif 'Block size:' in line:
                info['block_size'] = int(line.split(':')[1].strip())
            elif 'Inode count:' in line:
                info['inode_count'] = int(line.split(':')[1].strip())
    
    return info

def build_with_make_ext4fs(info):
    """Build system.img using make_ext4fs"""
    size_bytes = info['block_count'] * info['block_size']
    
    cmd = [
        'make_ext4fs',
        '-s',  # sparse image
        '-l', str(size_bytes),  # size in bytes
        '-a', 'system',  # android filesystem
        '-S', 'system_file_contexts.txt',  # SELinux contexts
        '-C', 'system_fs_config.txt',  # filesystem config
        'system.img',  # output file
        'system/'  # source directory
    ]
    
    print(f"Running: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        print("Successfully created system.img using make_ext4fs")
        return True
    else:
        print(f"Error: {result.stderr}")
        return False

def build_with_mke2fs(info):
    """Build system.img using mke2fs (Linux only)"""
    size_bytes = info['block_count'] * info['block_size']
    
    # Create empty image file
    print(f"Creating {size_bytes} byte image file...")
    with open('system.img', 'wb') as f:
        f.write(b'\x00' * size_bytes)
    
    # Format as ext4
    cmd = [
        'mke2fs',
        '-t', 'ext4',
        '-F',  # force
        '-b', str(info['block_size']),
        '-N', str(info['inode_count']),
        'system.img'
    ]
    
    print(f"Formatting: {' '.join(cmd)}")
    result = subprocess.run(cmd)
    
    if result.returncode != 0:
        print("Failed to format image")
        return False
    
    # Mount and copy files (requires root)
    print("Note: Copying files requires root privileges")
    print("You may need to run this script with sudo")
    
    return True

def main():
    print("Android System Image Builder")
    print("=" * 40)
    
    # Check if system directory exists
    if not Path('system').exists():
        print("Error: system directory not found!")
        sys.exit(1)
    
    # Check required config files
    required_files = ['system_fs_config.txt', 'system_file_contexts.txt']
    for file in required_files:
        if not Path(file).exists():
            print(f"Error: {file} not found!")
            sys.exit(1)
    
    # Get system info
    info = get_system_info()
    if not info:
        sys.exit(1)
    
    print(f"System parameters:")
    print(f"  Block count: {info['block_count']}")
    print(f"  Block size: {info['block_size']}")
    print(f"  Inode count: {info['inode_count']}")
    print(f"  Total size: {info['block_count'] * info['block_size'] // 1024 // 1024} MB")
    
    # Check available tools
    tool = check_tools()
    if not tool:
        print("\nError: No suitable tools found!")
        print("Please install one of the following:")
        print("  - make_ext4fs (Android build tools)")
        print("  - mke2fs (e2fsprogs)")
        print("\nFor Windows, you can download make_ext4fs from:")
        print("  https://github.com/osm0sis/make_ext4fs-Windows")
        sys.exit(1)
    
    print(f"\nUsing tool: {tool}")
    
    # Build image
    if tool == 'make_ext4fs':
        success = build_with_make_ext4fs(info)
    elif tool == 'mke2fs':
        success = build_with_mke2fs(info)
    
    if success:
        if Path('system.img').exists():
            size = Path('system.img').stat().st_size
            print(f"\nSuccess! Created system.img ({size // 1024 // 1024} MB)")
        else:
            print("\nImage creation completed, but file not found")
    else:
        print("\nFailed to create system.img")
        sys.exit(1)

if __name__ == '__main__':
    main()
