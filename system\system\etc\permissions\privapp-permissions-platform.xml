<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2016 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License
  -->

<!--
This XML file declares which signature|privileged permissions should be granted to privileged
applications that come with the platform
-->
<permissions>
    <privapp-permissions package="android.ext.services">
        <permission name="android.permission.PROVIDE_RESOLVER_RANKER_SERVICE" />
    </privapp-permissions>

    <privapp-permissions package="com.android.apps.tag">
        <permission name="android.permission.WRITE_SECURE_SETTINGS"/>
    </privapp-permissions>

    <privapp-permissions package="com.android.backupconfirm">
        <permission name="android.permission.BACKUP"/>
        <permission name="android.permission.CRYPT_KEEPER"/>
    </privapp-permissions>

    <privapp-permissions package="com.android.cellbroadcastreceiver">
        <permission name="android.permission.INTERACT_ACROSS_USERS"/>
        <permission name="android.permission.MANAGE_USERS"/>
        <permission name="android.permission.MODIFY_PHONE_STATE"/>
        <permission name="android.permission.READ_PRIVILEGED_PHONE_STATE"/>
        <permission name="android.permission.RECEIVE_EMERGENCY_BROADCAST"/>
    </privapp-permissions>

    <privapp-permissions package="com.android.contacts">
        <permission name="android.permission.GET_ACCOUNTS_PRIVILEGED"/>
        <permission name="com.android.voicemail.permission.READ_VOICEMAIL"/>
    </privapp-permissions>

    <privapp-permissions package="com.android.defcontainer">
        <permission name="android.permission.ACCESS_CACHE_FILESYSTEM"/>
        <permission name="android.permission.ALLOCATE_AGGRESSIVE"/>
        <permission name="android.permission.INTERACT_ACROSS_USERS"/>
        <permission name="android.permission.WRITE_MEDIA_STORAGE"/>
    </privapp-permissions>

    <privapp-permissions package="com.android.dialer">
        <permission name="android.permission.ALLOW_ANY_CODEC_FOR_PLAYBACK"/>
        <permission name="android.permission.CONTROL_INCALL_EXPERIENCE"/>
        <permission name="android.permission.GET_ACCOUNTS_PRIVILEGED"/>
        <permission name="android.permission.MODIFY_PHONE_STATE"/>
        <permission name="android.permission.STATUS_BAR"/>
        <permission name="android.permission.STOP_APP_SWITCHES"/>
        <permission name="com.android.voicemail.permission.READ_VOICEMAIL"/>
        <permission name="com.android.voicemail.permission.WRITE_VOICEMAIL"/>
    </privapp-permissions>

    <privapp-permissions package="com.android.emergency">
        <!-- Required to place emergency calls from emergency info screen. -->
        <permission name="android.permission.CALL_PRIVILEGED"/>
        <permission name="android.permission.MANAGE_USERS"/>
    </privapp-permissions>

    <privapp-permissions package="com.android.externalstorage">
        <permission name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"/>
        <permission name="android.permission.WRITE_MEDIA_STORAGE"/>
    </privapp-permissions>

    <privapp-permissions package="com.android.launcher3">
        <permission name="android.permission.BIND_APPWIDGET"/>
        <permission name="android.permission.CONTROL_REMOTE_APP_TRANSITION_ANIMATIONS"/>
        <permission name="android.permission.GET_ACCOUNTS_PRIVILEGED"/>
    </privapp-permissions>

    <privapp-permissions package="com.android.location.fused">
        <permission name="android.permission.INSTALL_LOCATION_PROVIDER"/>
    </privapp-permissions>

    <privapp-permissions package="com.android.managedprovisioning">
        <permission name="android.permission.CHANGE_COMPONENT_ENABLED_STATE"/>
        <permission name="android.permission.CHANGE_CONFIGURATION"/>
        <permission name="android.permission.CONNECTIVITY_INTERNAL"/>
        <permission name="android.permission.CRYPT_KEEPER"/>
        <permission name="android.permission.DELETE_PACKAGES"/>
        <permission name="android.permission.INSTALL_PACKAGES"/>
        <permission name="android.permission.INTERACT_ACROSS_USERS"/>
        <permission name="android.permission.MANAGE_DEVICE_ADMINS"/>
        <permission name="android.permission.MANAGE_USERS"/>
        <permission name="android.permission.MASTER_CLEAR"/>
        <permission name="android.permission.PERFORM_CDMA_PROVISIONING"/>
        <permission name="android.permission.SET_TIME"/>
        <permission name="android.permission.SET_TIME_ZONE"/>
        <permission name="android.permission.SHUTDOWN"/>
        <permission name="android.permission.WRITE_SECURE_SETTINGS"/>
    </privapp-permissions>

    <privapp-permissions package="com.android.mms.service">
        <permission name="android.permission.BIND_CARRIER_MESSAGING_SERVICE"/>
        <permission name="android.permission.BIND_CARRIER_SERVICES"/>
        <permission name="android.permission.INTERACT_ACROSS_USERS"/>
    </privapp-permissions>

    <privapp-permissions package="com.android.mtp">
        <permission name="android.permission.MANAGE_USB"/>
    </privapp-permissions>

    <privapp-permissions package="com.android.musicfx">
        <permission name="android.permission.CHANGE_COMPONENT_ENABLED_STATE"/>
    </privapp-permissions>

    <privapp-permissions package="com.android.networkrecommendation">
        <permission name="android.permission.SCORE_NETWORKS"/>
        <permission name="android.permission.SUBSTITUTE_NOTIFICATION_APP_NAME"/>
        <permission name="android.permission.WRITE_SECURE_SETTINGS"/>
    </privapp-permissions>

    <privapp-permissions package="com.android.omadm.service">
        <permission name="android.permission.CHANGE_CONFIGURATION"/>
        <permission name="android.permission.CONNECTIVITY_INTERNAL"/>
        <permission name="android.permission.MODIFY_PHONE_STATE"/>
        <permission name="android.permission.READ_PRIVILEGED_PHONE_STATE"/>
        <permission name="android.permission.WRITE_APN_SETTINGS"/>
        <permission name="android.permission.WRITE_SECURE_SETTINGS"/>
    </privapp-permissions>

    <privapp-permissions package="com.android.packageinstaller">
        <permission name="android.permission.CLEAR_APP_CACHE"/>
        <permission name="android.permission.DELETE_PACKAGES"/>
        <permission name="android.permission.INSTALL_PACKAGES"/>
        <permission name="android.permission.MANAGE_USERS"/>
        <permission name="android.permission.OBSERVE_GRANT_REVOKE_PERMISSIONS"/>
        <permission name="android.permission.UPDATE_APP_OPS_STATS"/>
        <permission name="android.permission.USE_RESERVED_DISK"/>
    </privapp-permissions>

    <privapp-permissions package="com.android.phone">
        <permission name="android.permission.ACCESS_IMS_CALL_SERVICE"/>
        <permission name="android.permission.BIND_CARRIER_MESSAGING_SERVICE"/>
        <permission name="android.permission.BIND_CARRIER_SERVICES"/>
        <permission name="android.permission.BIND_IMS_SERVICE"/>
        <permission name="android.permission.BIND_TELEPHONY_DATA_SERVICE"/>
        <permission name="android.permission.BIND_VISUAL_VOICEMAIL_SERVICE"/>
        <permission name="android.permission.CALL_PRIVILEGED"/>
        <permission name="android.permission.CHANGE_COMPONENT_ENABLED_STATE"/>
        <permission name="android.permission.CHANGE_CONFIGURATION"/>
        <permission name="android.permission.CHANGE_DEVICE_IDLE_TEMP_WHITELIST"/>
        <permission name="android.permission.CONNECTIVITY_INTERNAL"/>
        <permission name="android.permission.CONTROL_INCALL_EXPERIENCE"/>
        <permission name="android.permission.DUMP"/>
        <permission name="android.permission.INTERACT_ACROSS_USERS"/>
        <permission name="android.permission.LOCAL_MAC_ADDRESS"/>
        <permission name="android.permission.MANAGE_USERS"/>
        <permission name="android.permission.MODIFY_PHONE_STATE"/>
        <permission name="android.permission.PACKAGE_USAGE_STATS"/>
        <permission name="android.permission.PERFORM_CDMA_PROVISIONING"/>
        <permission name="android.permission.READ_NETWORK_USAGE_HISTORY"/>
        <permission name="android.permission.READ_PRIVILEGED_PHONE_STATE"/>
        <permission name="android.permission.READ_SEARCH_INDEXABLES"/>
        <permission name="android.permission.REBOOT"/>
        <permission name="android.permission.REGISTER_CALL_PROVIDER"/>
        <permission name="android.permission.REGISTER_SIM_SUBSCRIPTION"/>
        <permission name="android.permission.SEND_RESPOND_VIA_MESSAGE"/>
        <permission name="android.permission.SET_TIME"/>
        <permission name="android.permission.SET_TIME_ZONE"/>
        <permission name="android.permission.SHUTDOWN"/>
        <permission name="android.permission.STATUS_BAR"/>
        <permission name="android.permission.STOP_APP_SWITCHES"/>
        <permission name="android.permission.UPDATE_APP_OPS_STATS"/>
        <permission name="android.permission.UPDATE_DEVICE_STATS"/>
        <permission name="android.permission.UPDATE_LOCK"/>
        <permission name="android.permission.WRITE_APN_SETTINGS"/>
        <permission name="android.permission.WRITE_SECURE_SETTINGS"/>
        <permission name="android.permission.WRITE_EMBEDDED_SUBSCRIPTIONS"/>
        <permission name="com.android.voicemail.permission.READ_VOICEMAIL"/>
        <permission name="com.android.voicemail.permission.WRITE_VOICEMAIL"/>
    </privapp-permissions>

    <privapp-permissions package="com.android.providers.calendar">
        <permission name="android.permission.GET_ACCOUNTS_PRIVILEGED"/>
        <permission name="android.permission.UPDATE_APP_OPS_STATS"/>
        <permission name="android.permission.USE_RESERVED_DISK"/>
    </privapp-permissions>

    <privapp-permissions package="com.android.providers.contacts">
        <permission name="android.permission.BIND_DIRECTORY_SEARCH"/>
        <permission name="android.permission.GET_ACCOUNTS_PRIVILEGED"/>
        <permission name="android.permission.INTERACT_ACROSS_USERS"/>
        <permission name="android.permission.MANAGE_USERS"/>
        <permission name="android.permission.UPDATE_APP_OPS_STATS"/>
        <permission name="android.permission.USE_RESERVED_DISK"/>
    </privapp-permissions>

    <privapp-permissions package="com.android.providers.downloads">
        <permission name="android.permission.ACCESS_CACHE_FILESYSTEM"/>
        <permission name="android.permission.CLEAR_APP_CACHE"/>
        <permission name="android.permission.CONNECTIVITY_INTERNAL"/>
        <permission name="android.permission.UPDATE_APP_OPS_STATS"/>
        <permission name="android.permission.UPDATE_DEVICE_STATS"/>
    </privapp-permissions>

    <privapp-permissions package="com.android.providers.media">
        <permission name="android.permission.ACCESS_MTP"/>
        <permission name="android.permission.INTERACT_ACROSS_USERS"/>
        <permission name="android.permission.MANAGE_USERS"/>
        <permission name="android.permission.USE_RESERVED_DISK"/>
        <permission name="android.permission.WRITE_MEDIA_STORAGE"/>
    </privapp-permissions>

    <privapp-permissions package="com.android.providers.telephony">
        <permission name="android.permission.INTERACT_ACROSS_USERS"/>
        <permission name="android.permission.MODIFY_PHONE_STATE"/>
        <permission name="android.permission.USE_RESERVED_DISK"/>
    </privapp-permissions>

    <privapp-permissions package="com.android.provision">
        <permission name="android.permission.WRITE_SECURE_SETTINGS"/>
    </privapp-permissions>

    <privapp-permissions package="com.android.server.telecom">
        <permission name="android.permission.BIND_CONNECTION_SERVICE"/>
        <permission name="android.permission.BIND_INCALL_SERVICE"/>
        <permission name="android.permission.CALL_PRIVILEGED"/>
        <permission name="android.permission.INTERACT_ACROSS_USERS"/>
        <permission name="android.permission.MANAGE_USERS"/>
        <permission name="android.permission.MODIFY_AUDIO_ROUTING" />
        <permission name="android.permission.MODIFY_PHONE_STATE"/>
        <permission name="android.permission.STOP_APP_SWITCHES"/>
        <permission name="android.permission.SUBSTITUTE_NOTIFICATION_APP_NAME"/>
    </privapp-permissions>

    <privapp-permissions package="com.android.settings">
        <permission name="android.permission.ACCESS_CHECKIN_PROPERTIES"/>
        <permission name="android.permission.ACCESS_NOTIFICATIONS"/>
        <permission name="android.permission.BACKUP"/>
        <permission name="android.permission.BATTERY_STATS"/>
        <permission name="android.permission.BLUETOOTH_PRIVILEGED"/>
        <permission name="android.permission.CHANGE_APP_IDLE_STATE"/>
        <permission name="android.permission.CHANGE_CONFIGURATION"/>
        <permission name="android.permission.DELETE_PACKAGES"/>
        <permission name="android.permission.FORCE_STOP_PACKAGES"/>
        <permission name="android.permission.LOCAL_MAC_ADDRESS"/>
        <permission name="android.permission.MANAGE_DEVICE_ADMINS"/>
        <permission name="android.permission.MANAGE_FINGERPRINT"/>
        <permission name="android.permission.MANAGE_USB"/>
        <permission name="android.permission.MANAGE_USERS"/>
        <permission name="android.permission.MANAGE_USER_OEM_UNLOCK_STATE" />
        <permission name="android.permission.MASTER_CLEAR"/>
        <permission name="android.permission.MODIFY_PHONE_STATE"/>
        <permission name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"/>
        <permission name="android.permission.MOVE_PACKAGE"/>
        <permission name="android.permission.OVERRIDE_WIFI_CONFIG"/>
        <permission name="android.permission.PACKAGE_USAGE_STATS"/>
        <permission name="android.permission.READ_SEARCH_INDEXABLES"/>
        <permission name="android.permission.REBOOT"/>
        <permission name="android.permission.SET_TIME"/>
        <permission name="android.permission.STATUS_BAR"/>
        <permission name="android.permission.TETHER_PRIVILEGED"/>
        <permission name="android.permission.USE_RESERVED_DISK"/>
        <permission name="android.permission.USER_ACTIVITY"/>
        <permission name="android.permission.WRITE_APN_SETTINGS"/>
        <permission name="android.permission.WRITE_MEDIA_STORAGE"/>
        <permission name="android.permission.WRITE_SECURE_SETTINGS"/>
    </privapp-permissions>

    <privapp-permissions package="com.android.settings.intelligence">
        <permission name="android.permission.MANAGE_FINGERPRINT"/>
        <permission name="android.permission.MODIFY_PHONE_STATE"/>
        <permission name="android.permission.READ_SEARCH_INDEXABLES"/>
    </privapp-permissions>

    <privapp-permissions package="com.android.sharedstoragebackup">
        <permission name="android.permission.WRITE_MEDIA_STORAGE"/>
    </privapp-permissions>

    <privapp-permissions package="com.android.shell">
        <permission name="android.permission.ACCESS_LOWPAN_STATE"/>
        <permission name="android.permission.BACKUP"/>
        <permission name="android.permission.BATTERY_STATS"/>
        <permission name="android.permission.BIND_APPWIDGET"/>
        <permission name="android.permission.CHANGE_APP_IDLE_STATE"/>
        <permission name="android.permission.CHANGE_COMPONENT_ENABLED_STATE"/>
        <permission name="android.permission.CHANGE_CONFIGURATION"/>
        <permission name="android.permission.CHANGE_DEVICE_IDLE_TEMP_WHITELIST" />
        <permission name="android.permission.CHANGE_LOWPAN_STATE"/>
        <permission name="android.permission.CHANGE_OVERLAY_PACKAGES"/>
        <permission name="android.permission.CLEAR_APP_CACHE"/>
        <permission name="android.permission.CONNECTIVITY_INTERNAL"/>
        <permission name="android.permission.DELETE_CACHE_FILES"/>
        <permission name="android.permission.DELETE_PACKAGES"/>
        <permission name="android.permission.DUMP"/>
        <permission name="android.permission.ACTIVITY_EMBEDDING"/>
        <permission name="android.permission.FORCE_STOP_PACKAGES"/>
        <permission name="android.permission.GET_APP_OPS_STATS"/>
        <permission name="android.permission.INSTALL_LOCATION_PROVIDER"/>
        <permission name="android.permission.INSTALL_PACKAGES"/>
        <permission name="android.permission.INTERACT_ACROSS_USERS"/>
        <permission name="android.permission.LOCAL_MAC_ADDRESS"/>
        <permission name="android.permission.MANAGE_ACTIVITY_STACKS"/>
        <permission name="android.permission.MANAGE_DEVICE_ADMINS"/>
        <permission name="android.permission.MANAGE_USB"/>
        <permission name="android.permission.MODIFY_APPWIDGET_BIND_PERMISSIONS"/>
        <permission name="android.permission.MODIFY_PHONE_STATE"/>
        <permission name="android.permission.MOUNT_FORMAT_FILESYSTEMS"/>
        <permission name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"/>
        <permission name="android.permission.MOVE_PACKAGE"/>
        <permission name="android.permission.PACKAGE_USAGE_STATS" />
        <permission name="android.permission.READ_FRAME_BUFFER"/>
        <permission name="android.permission.READ_LOWPAN_CREDENTIAL"/>
        <permission name="android.permission.REAL_GET_TASKS"/>
        <permission name="android.permission.REGISTER_CALL_PROVIDER"/>
        <permission name="android.permission.REGISTER_CONNECTION_MANAGER"/>
        <permission name="android.permission.REGISTER_SIM_SUBSCRIPTION"/>
        <permission name="android.permission.RETRIEVE_WINDOW_CONTENT"/>
        <permission name="android.permission.SET_ALWAYS_FINISH"/>
        <permission name="android.permission.SET_ANIMATION_SCALE"/>
        <permission name="android.permission.SET_DEBUG_APP"/>
        <permission name="android.permission.SET_PROCESS_LIMIT"/>
        <permission name="android.permission.SET_TIME"/>
        <permission name="android.permission.SET_TIME_ZONE"/>
        <permission name="android.permission.SIGNAL_PERSISTENT_PROCESSES"/>
        <permission name="android.permission.START_TASKS_FROM_RECENTS" />
        <permission name="android.permission.STOP_APP_SWITCHES"/>
        <permission name="android.permission.SUBSTITUTE_NOTIFICATION_APP_NAME"/>
        <permission name="android.permission.UPDATE_APP_OPS_STATS"/>
        <permission name="android.permission.USE_RESERVED_DISK"/>
        <permission name="android.permission.WRITE_MEDIA_STORAGE"/>
        <permission name="android.permission.WRITE_SECURE_SETTINGS"/>
    </privapp-permissions>

    <privapp-permissions package="com.android.statementservice">
        <permission name="android.permission.INTENT_FILTER_VERIFICATION_AGENT"/>
    </privapp-permissions>

    <privapp-permissions package="com.android.storagemanager">
        <permission name="android.permission.DELETE_PACKAGES"/>
        <permission name="android.permission.INTERACT_ACROSS_USERS"/>
        <permission name="android.permission.MANAGE_USERS"/>
        <permission name="android.permission.PACKAGE_USAGE_STATS"/>
        <permission name="android.permission.USE_RESERVED_DISK"/>
        <permission name="android.permission.WRITE_SECURE_SETTINGS"/>
    </privapp-permissions>

    <privapp-permissions package="com.android.systemui">
        <permission name="android.permission.BATTERY_STATS"/>
        <permission name="android.permission.BIND_APPWIDGET"/>
        <permission name="android.permission.BLUETOOTH_PRIVILEGED"/>
        <permission name="android.permission.CHANGE_COMPONENT_ENABLED_STATE"/>
        <permission name="android.permission.CHANGE_DEVICE_IDLE_TEMP_WHITELIST"/>
        <permission name="android.permission.CHANGE_OVERLAY_PACKAGES"/>
        <permission name="android.permission.CONNECTIVITY_INTERNAL"/>
        <permission name="android.permission.CONTROL_REMOTE_APP_TRANSITION_ANIMATIONS"/>
        <permission name="android.permission.CONTROL_VPN"/>
        <permission name="android.permission.DUMP"/>
        <permission name="android.permission.GET_APP_OPS_STATS"/>
        <permission name="android.permission.INTERACT_ACROSS_USERS"/>
        <permission name="android.permission.MANAGE_ACTIVITY_STACKS"/>
        <permission name="android.permission.MANAGE_USB"/>
        <permission name="android.permission.MANAGE_USERS"/>
        <permission name="android.permission.MASTER_CLEAR"/>
        <permission name="android.permission.MEDIA_CONTENT_CONTROL"/>
        <permission name="android.permission.MODIFY_PHONE_STATE"/>
        <permission name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"/>
        <permission name="android.permission.OVERRIDE_WIFI_CONFIG"/>
        <permission name="android.permission.READ_DREAM_STATE"/>
        <permission name="android.permission.READ_FRAME_BUFFER"/>
        <permission name="android.permission.READ_NETWORK_USAGE_HISTORY"/>
        <permission name="android.permission.READ_PRIVILEGED_PHONE_STATE"/>
        <permission name="android.permission.REAL_GET_TASKS"/>
        <permission name="android.permission.RECEIVE_MEDIA_RESOURCE_USAGE"/>
        <permission name="android.permission.START_TASKS_FROM_RECENTS"/>
        <permission name="android.permission.STATUS_BAR"/>
        <permission name="android.permission.STOP_APP_SWITCHES"/>
        <permission name="android.permission.SUBSTITUTE_NOTIFICATION_APP_NAME"/>
        <permission name="android.permission.TETHER_PRIVILEGED"/>
        <permission name="android.permission.UPDATE_APP_OPS_STATS"/>
        <permission name="android.permission.USE_RESERVED_DISK"/>
        <permission name="android.permission.WRITE_DREAM_STATE"/>
        <permission name="android.permission.WRITE_MEDIA_STORAGE"/>
        <permission name="android.permission.WRITE_SECURE_SETTINGS"/>
        <permission name="android.permission.WRITE_EMBEDDED_SUBSCRIPTIONS"/>
    </privapp-permissions>

    <privapp-permissions package="com.android.tv">
        <permission name="android.permission.CHANGE_HDMI_CEC_ACTIVE_SOURCE"/>
        <permission name="android.permission.DVB_DEVICE"/>
        <permission name="android.permission.GLOBAL_SEARCH"/>
        <permission name="android.permission.HDMI_CEC"/>
        <permission name="android.permission.MODIFY_PARENTAL_CONTROLS"/>
        <permission name="android.permission.READ_CONTENT_RATING_SYSTEMS"/>
        <permission name="com.android.providers.tv.permission.ACCESS_ALL_EPG_DATA"/>
        <permission name="com.android.providers.tv.permission.ACCESS_WATCHED_PROGRAMS"/>
    </privapp-permissions>

    <privapp-permissions package="com.android.vpndialogs">
        <permission name="android.permission.CONNECTIVITY_INTERNAL"/>
        <permission name="android.permission.CONTROL_VPN"/>
    </privapp-permissions>

    <privapp-permissions package="com.kozyax.korgutv">
        <permission name="android.permission.BIND_APPWIDGET"/>
        <permission name="android.permission.CONTROL_REMOTE_APP_TRANSITION_ANIMATIONS"/>
        <permission name="android.permission.GET_ACCOUNTS_PRIVILEGED"/>
    </privapp-permissions>

</permissions>
