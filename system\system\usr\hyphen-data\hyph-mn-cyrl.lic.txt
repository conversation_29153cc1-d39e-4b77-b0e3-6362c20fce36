% copyright: Copyright 2007-2015 by <PERSON><PERSON><PERSON><PERSON><PERSON>, National University of Mongolia
% title: Hyphenation patterns for Mongolian language
% version: v1.2 2008/03/23
% notice: >
%     This file is part of the hyph-utf8 package.
%     See http://www.hyphenation.org for more information.
% license:
%     - This file is available under the following license.
%        name: MIT
%        url: https://opensource.org/licenses/MIT
%        text: >
%             Permission is hereby granted, free of charge, to any person obtaining a copy
%             of this software and associated documentation files (the "Software"), to deal
%             in the Software without restriction, including without limitation the rights
%             to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
%             copies of the Software, and to permit persons to whom the Software is
%             furnished to do so, subject to the following conditions:
%             
%             The above copyright notice and this permission notice shall be included in
%             all copies or substantial portions of the Software.
%             
%             THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
%             IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
%             FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL THE
%             AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
%             LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
%             OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
%             THE SOFTWARE.
% changes:
%     - 2008/03/23 v1.2
%     - 2008/06/08 Patterns added to hyph-utf8
%     - 2015/12/05 Patterns released under the MIT license
% ==========================================
% Special thanks to: Jim Hefferon and Robin Fairbairns
%
% There are few basic rules in mongolian
%
% 1. If there is a consonant then it can use hyphen before it.
%    And if there're two consonants then it can use hyphen before second consonant.
%    Are there three consonants then it can put hyphen before third consonant,
%    also are there four consonants then it can use hyphen before fourth consonant.
% 2. In the case of hardsign or softsign, it's possible to hyphen after these signs.
%    For instance, байгуулъ-я, үзүүль-е
% 3. However one vowel can be belong to a syllable, it's not possible to use hyphen.
%
