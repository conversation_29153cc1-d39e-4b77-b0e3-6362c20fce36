-----<PERSON><PERSON><PERSON> CERTIFICATE-----
MIIFbzCCA1egAwIBAgISESChaRu/vbm9UpaPI+hIvyYRMA0GCSqGSIb3DQEBDQUA
MEAxCzAJBgNVBAYTAkZSMRIwEAYDVQQKDAlPcGVuVHJ1c3QxHTAbBgNVBAMMFE9w
ZW5UcnVzdCBSb290IENBIEcyMB4XDTE0MDUyNjAwMDAwMFoXDTM4MDExNTAwMDAw
MFowQDELMAkGA1UEBhMCRlIxEjAQBgNVBAoMCU9wZW5UcnVzdDEdMBsGA1UEAwwU
T3BlblRydXN0IFJvb3QgQ0EgRzIwggIiMA0GCSqGSIb3DQEBAQUAA4ICDwAwggIK
AoICAQDMtlelM5QQgTJT32F+D3Y5z1zCU3UdSXqWON2ic2rxb95eolq5cSG+Ntmh
/LzubKh8NBpxGuga2F8ORAbtp+Dz0mEL4DKiltE48MLaARf85KxP6O6JHnSrT78e
CbY2albz4e6WiWYkBuTNQjpK3eCasMSCRbP+yatcfD7J6xcvDH1urqWPyKwlCm/6
1UWY0jUJ9gNDlP7ZvyCVeYCYitmJNbtRG6Q3ffyZO6v/v6wNj0OxmXsWEH4db0fE
FY8ElggGQgT4hNYdvJGmQr5J1WqIP7wtUdGejeBSzFfdNTVY27SPJIjki9/ca1TS
gSuyzpJLHB9G+h3Ykst2Z7UJmQnlrBcUVXDGPKBWCgOz3GIZ38i1MH/1PCZ1Eb3X
G7OHngevZXHloM8apwkQHZOJZlvoPGIytbU6bumFAYueQ4xncyhZW+vj3CzMpSZy
YhK05pyDRPZRpOLAeiRXyg6lPzq1O4vldu5w5pLeFlwoW5cZJ5L+epJUzpM5ChaH
vGOz9bGTXOBut9Dq+WIyiET7vycotjCVXRIouZW+j1MY5aIYFuJWpLIsEPUdN6b4
t/bQWVyJ98LVtZR00dX+G7bw5tYee9I8y6jj9RjzIR9u701oBnstXW5DiabA+aC/
gh7PU3+06yzbXfZqfUAkBXKJOAGTy3HCOV0GEfZvePg3DTmEJwIDAQABo2MwYTAO
BgNVHQ8BAf8EBAMCAQYwDwYDVR0TAQH/BAUwAwEB/zAdBgNVHQ4EFgQUajn6QiL3
5okATV59M4PLuG53hq8wHwYDVR0jBBgwFoAUajn6QiL35okATV59M4PLuG53hq8w
DQYJKoZIhvcNAQENBQADggIBAJjLq0A85TMCl38th6aP1F5Kr7ge57tx+4BkJamz
Gj5oXScmp7oq4fBXgwpkTx4idBvpkF/wrM//T2h6OKQQbA2xx6R3gBi2oihEdqc0
nXGEL8pZ0keImUEiyTCYYW49qKgFbdEfwFFEVn8nNQLdXpgKQuswv42hm1GqO+qT
RmTFAHneIWv2V6CG1wZy7HBGS4tz3aAhdT7cHcCP009zHIXZ/n9iyJVvttN7jLpT
wm+bREx50B1ws9efAvSyB7DH5fitIw6mVskpEndI2S9G/Tvw/HRwkqWOOAgfZDC2
t0v7NqwQjqBSM2OdAzVWxWm9xiNaJ5T2pBL4LTM8oValX9YZ6e18CL13zSdkzJTa
TkZQh+D5wVOAHrut+0dSixv9ovneDiK3PTNZbNTe9ZUGMg1RGUFcPk8G97krgCf2
o6p6fAbhQ8MTOWIaNr3gKC6UAuQpLmBVrkA9sHSSXvAgZJY/X0VdiLWK2gKgW0VU
3jg9CcCoSmVGFvyqv1ROTVu+OEO3KMqLM6oaJbolXCkvW0pujOotnCr2BXbgd5eA
iN1nE28daCSLT7d0geX0YJ96Vdc+N9oWaz53rK4YcJUIeSkDiv7BO7M/Gg+kO14f
WKGVyasvc0rQLW6aWQ9VGHgtPFGml4vmu7JwqkwR3v98KzfUetF3NI/n+UL3PIEM
S1IK
-----END CERTIFICATE-----
Certificate:
    Data:
        Version: 3 (0x2)
        Serial Number:
            11:20:a1:69:1b:bf:bd:b9:bd:52:96:8f:23:e8:48:bf:26:11
    Signature Algorithm: sha512WithRSAEncryption
        Issuer: C=FR, O=OpenTrust, CN=OpenTrust Root CA G2
        Validity
            Not Before: May 26 00:00:00 2014 GMT
            Not After : Jan 15 00:00:00 2038 GMT
        Subject: C=FR, O=OpenTrust, CN=OpenTrust Root CA G2
        Subject Public Key Info:
            Public Key Algorithm: rsaEncryption
                Public-Key: (4096 bit)
                Modulus:
                    00:cc:b6:57:a5:33:94:10:81:32:53:df:61:7e:0f:
                    76:39:cf:5c:c2:53:75:1d:49:7a:96:38:dd:a2:73:
                    6a:f1:6f:de:5e:a2:5a:b9:71:21:be:36:d9:a1:fc:
                    bc:ee:6c:a8:7c:34:1a:71:1a:e8:1a:d8:5f:0e:44:
                    06:ed:a7:e0:f3:d2:61:0b:e0:32:a2:96:d1:38:f0:
                    c2:da:01:17:fc:e4:ac:4f:e8:ee:89:1e:74:ab:4f:
                    bf:1e:09:b6:36:6a:56:f3:e1:ee:96:89:66:24:06:
                    e4:cd:42:3a:4a:dd:e0:9a:b0:c4:82:45:b3:fe:c9:
                    ab:5c:7c:3e:c9:eb:17:2f:0c:7d:6e:ae:a5:8f:c8:
                    ac:25:0a:6f:fa:d5:45:98:d2:35:09:f6:03:43:94:
                    fe:d9:bf:20:95:79:80:98:8a:d9:89:35:bb:51:1b:
                    a4:37:7d:fc:99:3b:ab:ff:bf:ac:0d:8f:43:b1:99:
                    7b:16:10:7e:1d:6f:47:c4:15:8f:04:96:08:06:42:
                    04:f8:84:d6:1d:bc:91:a6:42:be:49:d5:6a:88:3f:
                    bc:2d:51:d1:9e:8d:e0:52:cc:57:dd:35:35:58:db:
                    b4:8f:24:88:e4:8b:df:dc:6b:54:d2:81:2b:b2:ce:
                    92:4b:1c:1f:46:fa:1d:d8:92:cb:76:67:b5:09:99:
                    09:e5:ac:17:14:55:70:c6:3c:a0:56:0a:03:b3:dc:
                    62:19:df:c8:b5:30:7f:f5:3c:26:75:11:bd:d7:1b:
                    b3:87:9e:07:af:65:71:e5:a0:cf:1a:a7:09:10:1d:
                    93:89:66:5b:e8:3c:62:32:b5:b5:3a:6e:e9:85:01:
                    8b:9e:43:8c:67:73:28:59:5b:eb:e3:dc:2c:cc:a5:
                    26:72:62:12:b4:e6:9c:83:44:f6:51:a4:e2:c0:7a:
                    24:57:ca:0e:a5:3f:3a:b5:3b:8b:e5:76:ee:70:e6:
                    92:de:16:5c:28:5b:97:19:27:92:fe:7a:92:54:ce:
                    93:39:0a:16:87:bc:63:b3:f5:b1:93:5c:e0:6e:b7:
                    d0:ea:f9:62:32:88:44:fb:bf:27:28:b6:30:95:5d:
                    12:28:b9:95:be:8f:53:18:e5:a2:18:16:e2:56:a4:
                    b2:2c:10:f5:1d:37:a6:f8:b7:f6:d0:59:5c:89:f7:
                    c2:d5:b5:94:74:d1:d5:fe:1b:b6:f0:e6:d6:1e:7b:
                    d2:3c:cb:a8:e3:f5:18:f3:21:1f:6e:ef:4d:68:06:
                    7b:2d:5d:6e:43:89:a6:c0:f9:a0:bf:82:1e:cf:53:
                    7f:b4:eb:2c:db:5d:f6:6a:7d:40:24:05:72:89:38:
                    01:93:cb:71:c2:39:5d:06:11:f6:6f:78:f8:37:0d:
                    39:84:27
                Exponent: 65537 (0x10001)
        X509v3 extensions:
            X509v3 Key Usage: critical
                Certificate Sign, CRL Sign
            X509v3 Basic Constraints: critical
                CA:TRUE
            X509v3 Subject Key Identifier: 
                6A:39:FA:42:22:F7:E6:89:00:4D:5E:7D:33:83:CB:B8:6E:77:86:AF
            X509v3 Authority Key Identifier: 
                keyid:6A:39:FA:42:22:F7:E6:89:00:4D:5E:7D:33:83:CB:B8:6E:77:86:AF

    Signature Algorithm: sha512WithRSAEncryption
         98:cb:ab:40:3c:e5:33:02:97:7f:2d:87:a6:8f:d4:5e:4a:af:
         b8:1e:e7:bb:71:fb:80:64:25:a9:b3:1a:3e:68:5d:27:26:a7:
         ba:2a:e1:f0:57:83:0a:64:4f:1e:22:74:1b:e9:90:5f:f0:ac:
         cf:ff:4f:68:7a:38:a4:10:6c:0d:b1:c7:a4:77:80:18:b6:a2:
         28:44:76:a7:34:9d:71:84:2f:ca:59:d2:47:88:99:41:22:c9:
         30:98:61:6e:3d:a8:a8:05:6d:d1:1f:c0:51:44:56:7f:27:35:
         02:dd:5e:98:0a:42:eb:30:bf:8d:a1:9b:51:aa:3b:ea:93:46:
         64:c5:00:79:de:21:6b:f6:57:a0:86:d7:06:72:ec:70:46:4b:
         8b:73:dd:a0:21:75:3e:dc:1d:c0:8f:d3:4f:73:1c:85:d9:fe:
         7f:62:c8:95:6f:b6:d3:7b:8c:ba:53:c2:6f:9b:44:4c:79:d0:
         1d:70:b3:d7:9f:02:f4:b2:07:b0:c7:e5:f8:ad:23:0e:a6:56:
         c9:29:12:77:48:d9:2f:46:fd:3b:f0:fc:74:70:92:a5:8e:38:
         08:1f:64:30:b6:b7:4b:fb:36:ac:10:8e:a0:52:33:63:9d:03:
         35:56:c5:69:bd:c6:23:5a:27:94:f6:a4:12:f8:2d:33:3c:a1:
         56:a5:5f:d6:19:e9:ed:7c:08:bd:77:cd:27:64:cc:94:da:4e:
         46:50:87:e0:f9:c1:53:80:1e:bb:ad:fb:47:52:8b:1b:fd:a2:
         f9:de:0e:22:b7:3d:33:59:6c:d4:de:f5:95:06:32:0d:51:19:
         41:5c:3e:4f:06:f7:b9:2b:80:27:f6:a3:aa:7a:7c:06:e1:43:
         c3:13:39:62:1a:36:bd:e0:28:2e:94:02:e4:29:2e:60:55:ae:
         40:3d:b0:74:92:5e:f0:20:64:96:3f:5f:45:5d:88:b5:8a:da:
         02:a0:5b:45:54:de:38:3d:09:c0:a8:4a:65:46:16:fc:aa:bf:
         54:4e:4d:5b:be:38:43:b7:28:ca:8b:33:aa:1a:25:ba:25:5c:
         29:2f:5b:4a:6e:8c:ea:2d:9c:2a:f6:05:76:e0:77:97:80:88:
         dd:67:13:6f:1d:68:24:8b:4f:b7:74:81:e5:f4:60:9f:7a:55:
         d7:3e:37:da:16:6b:3e:77:ac:ae:18:70:95:08:79:29:03:8a:
         fe:c1:3b:b3:3f:1a:0f:a4:3b:5e:1f:58:a1:95:c9:ab:2f:73:
         4a:d0:2d:6e:9a:59:0f:55:18:78:2d:3c:51:a6:97:8b:e6:bb:
         b2:70:aa:4c:11:de:ff:7c:2b:37:d4:7a:d1:77:34:8f:e7:f9:
         42:f7:3c:81:0c:4b:52:0a
SHA1 Fingerprint=79:5F:88:60:C5:AB:7C:3D:92:E6:CB:F4:8D:E1:45:CD:11:EF:60:0B
