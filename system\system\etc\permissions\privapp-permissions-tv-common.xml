<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2017 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License
  -->

<!--
This XML file declares which signature|privileged permissions should be granted to privileged
applications on GMS or Google-branded devices.
It allows additional grants on top of privapp-permissions-platform.xml

CHANGHONG modify for cmjt hangyan default-jtydn-permissions.xml T230524
-->

<permissions>
    <privapp-permissions package="com.rockchips.mediacenter">
        <permission name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
        <permission name="android.permission.WRITE_MEDIA_STORAGE" />
        <permission name="android.permission.WRITE_SECURE_SETTINGS"/>
    </privapp-permissions>
    <privapp-permissions package="com.google.android.katniss">
	<permission name="android.permission.MODIFY_AUDIO_ROUTING" />
    </privapp-permissions>
    <privapp-permissions package="com.cghs.stresstest">
        <permission name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
        <permission name="android.permission.WRITE_MEDIA_STORAGE" />
        <permission name="android.permission.WRITE_SECURE_SETTINGS"/>
    </privapp-permissions>
    <privapp-permissions package="cm.komect.aqb.android.cloudcomputer">
        <permission name="android.permission.RECORD_AUDIO" />
    </privapp-permissions>
</permissions>
