-----<PERSON><PERSON><PERSON> CERTIFICATE-----
MIIDfTCCAmWgAwIBAgIBADANBgkqhkiG9w0BAQUFADBgMQswCQYDVQQGEwJKUDEl
MCMGA1UEChMcU0VDT00gVHJ1c3QgU3lzdGVtcyBDTy4sTFRELjEqMCgGA1UECxMh
U2VjdXJpdHkgQ29tbXVuaWNhdGlvbiBFViBSb290Q0ExMB4XDTA3MDYwNjAyMTIz
MloXDTM3MDYwNjAyMTIzMlowYDELMAkGA1UEBhMCSlAxJTAjBgNVBAoTHFNFQ09N
IFRydXN0IFN5c3RlbXMgQ08uLExURC4xKjAoBgNVBAsTIVNlY3VyaXR5IENvbW11
bmljYXRpb24gRVYgUm9vdENBMTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoC
ggEBALx/7FebJOD+nLpCeamIivqA4PUHKUPqjgo0No0c+qe1OXj/l3X3L+SqawSE
RMqm4miO/VVQYg+kcQ7OBzgtQoVQrTyWb4vVog7P3kmJPdZkLjjlHmy1V4qe70gO
zXppFodEtZDkBp2uoQSXWHnvIEqCa4wiv+wfD+mEce3xDuS4GBPMVjZd0ZoeUWs5
bmB2iDQL87PRsJ3KYeJkHcFGB7hj3R4zZbOOCVVSPbW9/wfrrWFVGCypaZhKqkDF
MxRldAD5kd6vA0jFQFTcD4SQaCDFkpbcLuUCRarAX1T4bepJz11sS6/vmsJWXMY1
VkJqMF/Cq/biPT+zyRGPMUzXn0kCAwEAAaNCMEAwHQYDVR0OBBYEFDVK9U2vP9eC
OKyrcWUXdYydVZPmMA4GA1UdDwEB/wQEAwIBBjAPBgNVHRMBAf8EBTADAQH/MA0G
CSqGSIb3DQEBBQUAA4IBAQCoh+ns+EBnXcPBZsdAS5f8hxOQWsTvoMpfi7ent/HW
tWS3irO4G8za+6xmiEHO6Pzk2x6Ipu0nUBsCMCRGef4Eh3CXQHPRwMFXGZpppSeZ
q51ihPZRwSzJIxXYKLerJRO1RuGGAv8mjMSIkh1W/hln8lXkgKNrnKt34VFxDSDb
EJrbvXZ5B3eZKK2aXtqxT0QsNY6llsf9g/BYxnnWmHyojf6GPgcWkuF75x3sM3Z+
Qi5KhfmRiWiEA4Glm5q+4zfFVKtWOxgtQaQM+ELbmaDgcm+7XeEWT1MKZPlO9L9O
VL14bIjqv5wTJMJwaaJ/D8g8rQjJsJhAoyrniIPtd490
-----END CERTIFICATE-----
Certificate:
    Data:
        Version: 3 (0x2)
        Serial Number: 0 (0x0)
    Signature Algorithm: sha1WithRSAEncryption
        Issuer: C=JP, O=SECOM Trust Systems CO.,LTD., OU=Security Communication EV RootCA1
        Validity
            Not Before: Jun  6 02:12:32 2007 GMT
            Not After : Jun  6 02:12:32 2037 GMT
        Subject: C=JP, O=SECOM Trust Systems CO.,LTD., OU=Security Communication EV RootCA1
        Subject Public Key Info:
            Public Key Algorithm: rsaEncryption
                Public-Key: (2048 bit)
                Modulus:
                    00:bc:7f:ec:57:9b:24:e0:fe:9c:ba:42:79:a9:88:
                    8a:fa:80:e0:f5:07:29:43:ea:8e:0a:34:36:8d:1c:
                    fa:a7:b5:39:78:ff:97:75:f7:2f:e4:aa:6b:04:84:
                    44:ca:a6:e2:68:8e:fd:55:50:62:0f:a4:71:0e:ce:
                    07:38:2d:42:85:50:ad:3c:96:6f:8b:d5:a2:0e:cf:
                    de:49:89:3d:d6:64:2e:38:e5:1e:6c:b5:57:8a:9e:
                    ef:48:0e:cd:7a:69:16:87:44:b5:90:e4:06:9d:ae:
                    a1:04:97:58:79:ef:20:4a:82:6b:8c:22:bf:ec:1f:
                    0f:e9:84:71:ed:f1:0e:e4:b8:18:13:cc:56:36:5d:
                    d1:9a:1e:51:6b:39:6e:60:76:88:34:0b:f3:b3:d1:
                    b0:9d:ca:61:e2:64:1d:c1:46:07:b8:63:dd:1e:33:
                    65:b3:8e:09:55:52:3d:b5:bd:ff:07:eb:ad:61:55:
                    18:2c:a9:69:98:4a:aa:40:c5:33:14:65:74:00:f9:
                    91:de:af:03:48:c5:40:54:dc:0f:84:90:68:20:c5:
                    92:96:dc:2e:e5:02:45:aa:c0:5f:54:f8:6d:ea:49:
                    cf:5d:6c:4b:af:ef:9a:c2:56:5c:c6:35:56:42:6a:
                    30:5f:c2:ab:f6:e2:3d:3f:b3:c9:11:8f:31:4c:d7:
                    9f:49
                Exponent: 65537 (0x10001)
        X509v3 extensions:
            X509v3 Subject Key Identifier: 
                35:4A:F5:4D:AF:3F:D7:82:38:AC:AB:71:65:17:75:8C:9D:55:93:E6
            X509v3 Key Usage: critical
                Certificate Sign, CRL Sign
            X509v3 Basic Constraints: critical
                CA:TRUE
    Signature Algorithm: sha1WithRSAEncryption
         a8:87:e9:ec:f8:40:67:5d:c3:c1:66:c7:40:4b:97:fc:87:13:
         90:5a:c4:ef:a0:ca:5f:8b:b7:a7:b7:f1:d6:b5:64:b7:8a:b3:
         b8:1b:cc:da:fb:ac:66:88:41:ce:e8:fc:e4:db:1e:88:a6:ed:
         27:50:1b:02:30:24:46:79:fe:04:87:70:97:40:73:d1:c0:c1:
         57:19:9a:69:a5:27:99:ab:9d:62:84:f6:51:c1:2c:c9:23:15:
         d8:28:b7:ab:25:13:b5:46:e1:86:02:ff:26:8c:c4:88:92:1d:
         56:fe:19:67:f2:55:e4:80:a3:6b:9c:ab:77:e1:51:71:0d:20:
         db:10:9a:db:bd:76:79:07:77:99:28:ad:9a:5e:da:b1:4f:44:
         2c:35:8e:a5:96:c7:fd:83:f0:58:c6:79:d6:98:7c:a8:8d:fe:
         86:3e:07:16:92:e1:7b:e7:1d:ec:33:76:7e:42:2e:4a:85:f9:
         91:89:68:84:03:81:a5:9b:9a:be:e3:37:c5:54:ab:56:3b:18:
         2d:41:a4:0c:f8:42:db:99:a0:e0:72:6f:bb:5d:e1:16:4f:53:
         0a:64:f9:4e:f4:bf:4e:54:bd:78:6c:88:ea:bf:9c:13:24:c2:
         70:69:a2:7f:0f:c8:3c:ad:08:c9:b0:98:40:a3:2a:e7:88:83:
         ed:77:8f:74
SHA1 Fingerprint=FE:B8:C4:32:DC:F9:76:9A:CE:AE:3D:D8:90:8F:FD:28:86:65:64:7D
