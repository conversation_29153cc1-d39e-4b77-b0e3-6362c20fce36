# Copyright (C) 2017 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

#
# Sony Playstation(R) DualShock 4 Controller
#


# Mapping according to https://developer.android.com/training/game-controllers/controller-input.html

# Square
key 0x130    BUTTON_X
# Cross
key 0x131    BUTTON_A
# Circle
key 0x132    BUTTON_B
# Triangle
key 0x133    BUTTON_Y

key 0x134    BUTTON_L1
key 0x135    BUTTON_R1
key 0x136    BUTTON_L2
key 0x137    BUTTON_R2

# L2 axis
axis 0x03   LTRIGGER
# R2 axis
axis 0x04   RTRIGGER


# Left Analog Stick
axis 0x00    X
axis 0x01    Y
# Right Analog Stick
axis 0x02    Z
axis 0x05    RZ

# Left stick click
key 0x13a    BUTTON_THUMBL
# Right stick click
key 0x13b    BUTTON_THUMBR

# Hat
axis 0x10 HAT_X
axis 0x11 HAT_Y

# Mapping according to https://www.kernel.org/doc/Documentation/input/gamepad.txt
# Share
key 0x138    BUTTON_SELECT
# Options
key 0x139    BUTTON_START

# PS key
key 0x13c    BUTTON_MODE

# Touchpad press
# The touchpad for this joystick will become a separate input device in future releases
# and this button will be equivalent to left mouse button
# Therefore, map it to KEYCODE_BUTTON_1 here to allow apps to still handle this on earlier versions
key 0x13d   BUTTON_1
