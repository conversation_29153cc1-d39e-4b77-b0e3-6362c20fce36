-----<PERSON><PERSON><PERSON> CERTIFICATE-----
MIIDMDCCAhigAwIBAgICA+gwDQYJKoZIhvcNAQEFBQAwRzELMAkGA1UEBhMCSEsx
FjAUBgNVBAoTDUhvbmdrb25nIFBvc3QxIDAeBgNVBAMTF0hvbmdrb25nIFBvc3Qg
Um9vdCBDQSAxMB4XDTAzMDUxNTA1MTMxNFoXDTIzMDUxNTA0NTIyOVowRzELMAkG
A1UEBhMCSEsxFjAUBgNVBAoTDUhvbmdrb25nIFBvc3QxIDAeBgNVBAMTF0hvbmdr
b25nIFBvc3QgUm9vdCBDQSAxMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKC
AQEArP84tulmAknjorThkPlAj3n54r15/gK97iSSHSL22oVyaf7XPwnU3ZG1ApzQ
jVrhVcNQhrkpJsLj2aDxaQMoIIBFIi1WpztUlVYiWR8o3x8gPW2iNr4joLFutbEn
PzlTCeqrauh0ssJlXI6/fMN4hM2eFvz1Lk8gKgifd/PFHsSaUmYeSF7jEAaPIpjh
ZY4bXSNmO7ilMlHIhqqhqZ5/dpTCpmy3QfDVyAY45tQM4vM7TG1QjMSDJ8EThFk9
nnV0ttgCXjqQesBCNnLsak3c78QA3xMYV18meMjWCnl3v/evt3a5pQuEF10Q6m/h
q5URX208o1xNg1vysxmKgIsLhwIDAQABoyYwJDASBgNVHRMBAf8ECDAGAQH/AgED
MA4GA1UdDwEB/wQEAwIBxjANBgkqhkiG9w0BAQUFAAOCAQEADkbVPK7ih9legYsC
mEEIjEy82tvuJxuC52pF7BaLT4Wg87JwvVqWuspube5Gi27nKi6Wsxkz67SfqLI3
7piol7Yutmcn1KZJ/RyTZXaeQi/cImyaT/JaFTmxcdcrUehtHJjA2Sr0oYJ71clB
oiMBdDhViw+5LmeiIAQ32pwL0xch4I+XeTRvhEgCIDMb5jREn5Fw9IBehEPCKdJs
EhTkYY2sEJCehFC78JZvRZ+K88psT/oROhUVRsPNH4NbLUES7VBnQRM9IauUiqpO
fMGx+6fWtScvl6tu4B3i0RwsH0Ti/L6RoZz71ilTc4afU9hDDl3WY4JxHYB0yvbi
AmvZWg==
-----END CERTIFICATE-----
Certificate:
    Data:
        Version: 3 (0x2)
        Serial Number: 1000 (0x3e8)
    Signature Algorithm: sha1WithRSAEncryption
        Issuer: C=HK, O=Hongkong Post, CN=Hongkong Post Root CA 1
        Validity
            Not Before: May 15 05:13:14 2003 GMT
            Not After : May 15 04:52:29 2023 GMT
        Subject: C=HK, O=Hongkong Post, CN=Hongkong Post Root CA 1
        Subject Public Key Info:
            Public Key Algorithm: rsaEncryption
                Public-Key: (2048 bit)
                Modulus:
                    00:ac:ff:38:b6:e9:66:02:49:e3:a2:b4:e1:90:f9:
                    40:8f:79:f9:e2:bd:79:fe:02:bd:ee:24:92:1d:22:
                    f6:da:85:72:69:fe:d7:3f:09:d4:dd:91:b5:02:9c:
                    d0:8d:5a:e1:55:c3:50:86:b9:29:26:c2:e3:d9:a0:
                    f1:69:03:28:20:80:45:22:2d:56:a7:3b:54:95:56:
                    22:59:1f:28:df:1f:20:3d:6d:a2:36:be:23:a0:b1:
                    6e:b5:b1:27:3f:39:53:09:ea:ab:6a:e8:74:b2:c2:
                    65:5c:8e:bf:7c:c3:78:84:cd:9e:16:fc:f5:2e:4f:
                    20:2a:08:9f:77:f3:c5:1e:c4:9a:52:66:1e:48:5e:
                    e3:10:06:8f:22:98:e1:65:8e:1b:5d:23:66:3b:b8:
                    a5:32:51:c8:86:aa:a1:a9:9e:7f:76:94:c2:a6:6c:
                    b7:41:f0:d5:c8:06:38:e6:d4:0c:e2:f3:3b:4c:6d:
                    50:8c:c4:83:27:c1:13:84:59:3d:9e:75:74:b6:d8:
                    02:5e:3a:90:7a:c0:42:36:72:ec:6a:4d:dc:ef:c4:
                    00:df:13:18:57:5f:26:78:c8:d6:0a:79:77:bf:f7:
                    af:b7:76:b9:a5:0b:84:17:5d:10:ea:6f:e1:ab:95:
                    11:5f:6d:3c:a3:5c:4d:83:5b:f2:b3:19:8a:80:8b:
                    0b:87
                Exponent: 65537 (0x10001)
        X509v3 extensions:
            X509v3 Basic Constraints: critical
                CA:TRUE, pathlen:3
            X509v3 Key Usage: critical
                Digital Signature, Non Repudiation, Certificate Sign, CRL Sign
    Signature Algorithm: sha1WithRSAEncryption
         0e:46:d5:3c:ae:e2:87:d9:5e:81:8b:02:98:41:08:8c:4c:bc:
         da:db:ee:27:1b:82:e7:6a:45:ec:16:8b:4f:85:a0:f3:b2:70:
         bd:5a:96:ba:ca:6e:6d:ee:46:8b:6e:e7:2a:2e:96:b3:19:33:
         eb:b4:9f:a8:b2:37:ee:98:a8:97:b6:2e:b6:67:27:d4:a6:49:
         fd:1c:93:65:76:9e:42:2f:dc:22:6c:9a:4f:f2:5a:15:39:b1:
         71:d7:2b:51:e8:6d:1c:98:c0:d9:2a:f4:a1:82:7b:d5:c9:41:
         a2:23:01:74:38:55:8b:0f:b9:2e:67:a2:20:04:37:da:9c:0b:
         d3:17:21:e0:8f:97:79:34:6f:84:48:02:20:33:1b:e6:34:44:
         9f:91:70:f4:80:5e:84:43:c2:29:d2:6c:12:14:e4:61:8d:ac:
         10:90:9e:84:50:bb:f0:96:6f:45:9f:8a:f3:ca:6c:4f:fa:11:
         3a:15:15:46:c3:cd:1f:83:5b:2d:41:12:ed:50:67:41:13:3d:
         21:ab:94:8a:aa:4e:7c:c1:b1:fb:a7:d6:b5:27:2f:97:ab:6e:
         e0:1d:e2:d1:1c:2c:1f:44:e2:fc:be:91:a1:9c:fb:d6:29:53:
         73:86:9f:53:d8:43:0e:5d:d6:63:82:71:1d:80:74:ca:f6:e2:
         02:6b:d9:5a
SHA1 Fingerprint=D6:DA:A8:20:8D:09:D2:15:4D:24:B5:2F:CB:34:6E:B2:58:B2:8A:58
