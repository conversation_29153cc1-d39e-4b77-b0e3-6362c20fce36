# Host IID to use as the source of CLAT traffic.
# This is a /128 taken out of the /64 on the parent interface.
# A host IID of :: means to generate a checksum-neutral, random IID.
ipv6_host_id ::

# IPv4 address configuration to use when selecting a host address. The first
# clat daemon started will use the address specified by ipv4_local_subnet. If
# more than one daemon is run at the same time, subsequent daemons will use
# other addresses in the prefix of length ipv4_local prefixlen that contains
# ipv4_local_subnet. The default is to use the IANA-assigned range *********/29,
# which allows up to 8 clat daemons (.4, .5, .6, .7, .0, .1, .2, .3).
ipv4_local_subnet *********
ipv4_local_prefixlen 29

# get the plat_subnet from dns lookups (requires DNS64)
plat_from_dns64 yes
# hostname to use to lookup plat subnet. must contain only A records
plat_from_dns64_hostname ipv4only.arpa

# plat subnet to send ipv4 traffic to. This is a /96 subnet.
# This setting only makes sense with: plat_from_dns64 no
#plat_subnet 2001:db8:1:2:3:4::
