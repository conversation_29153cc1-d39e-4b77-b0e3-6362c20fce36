#!/system/bin/sh

LOG="/system/bin/log -t tp_policy"
ECHO()
{
    #echo $*
    #$LOG "$*"
}

function is_proc_exist()
{
#	str1=`ps -A | grep -i $1 | grep -v grep | awk '{print $NF}'`
	str1=`ps -ef | grep -i $1 | grep -v grep`

	if [ "$str1" != "" ] ; then
		ECHO "proc $1 exist"
		return 1
	else
		ECHO "proc $1 not exist"
		return 0
	fi
}

function is_package_exist()
{
	str1=`pm list package -f | grep $1 | grep -v grep`

	if [ "$str1" != "" ] ; then
		ECHO "package $1 exist"
		return 1
	else
		ECHO "package $1 not exist"
		return 0
	fi
}

cpugov_path=/sys/devices/system/cpu/cpu0/cpufreq/scaling_governor
netdev=proc/net/dev
last_rx_pkts=0
last_tx_pkts=0
pkts_threshold=10000
need_recovery=0

while [ true ] ; do
	ifconfig wlan0 | grep "inet addr" > /dev/null 2>&1
	if [ $? -ne 0 ] ; then
		sleep 2
		continue
	fi

	is_proc_exist iperf
	if [ $? -eq 1 ] ; then
		iperf_exist=1
	else
		iperf_exist=0
	fi

	rx_pkts=`cat $netdev | grep wlan0 | awk '{print $3}'`
	tx_pkts=`cat $netdev | grep wlan0 | awk '{print $11}'`
	ECHO "rx_pkts:$rx_pkts tx_pkts:$tx_pkts iperf:$iperf_exist"
	
	sleep_int=1
	rx_inc=0
	if [ $last_rx_pkts -gt 0 ] ; then
		rx_inc=$((rx_pkts-last_rx_pkts))
		sleep_int=2
	fi
	tx_inc=0
	if [ $last_tx_pkts -gt 0 ] ; then
		tx_inc=$((tx_pkts-last_tx_pkts))
		sleep_int=2
	fi
	
	threshold=$((pkts_threshold*sleep_int))
	ECHO "rx_inc: $rx_inc tx_inc: $tx_inc sleep_int: $sleep_int threshold: $threshold"
	if [ $rx_inc -gt $threshold ] || [ $tx_inc -gt $threshold ] || [ $iperf_exist -eq 1 ]; then
		cur_cpugov=`cat $cpugov_path`
		if [ $cur_cpugov != "performance" ] ; then
			ECHO "cpu performance"
			echo "performance" > $cpugov_path
			need_recovery=1
		fi

	else
		if [ $need_recovery -eq 1 ] ; then
			ECHO "cpu interactive"
			echo "interactive" > $cpugov_path
			need_recovery=0
		fi

	fi

	last_rx_pkts=$rx_pkts
	last_tx_pkts=$tx_pkts
	sleep $sleep_int
done

