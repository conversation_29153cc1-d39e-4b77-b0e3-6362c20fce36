EXT4 superblock info:

Filesystem volume name:    
Last mounted on:           
Filesystem UUID:           37DFCF82-C3BD-11F0-91C0-C111C441E86E
Filesystem magic number:   0xEF53
Filesystem revision:       1 (v2 format/dynamic inode sizes)
FS compatible features:    0x0000003C (HAS_JOURNAL EXT_ATTR RESIZE_INODE DIR_INDEX)
FS incompatible features:  0x000002C2 (FILETYPE EXTENTS 64BIT FLEX_BG)
FS read-only features:     0x0000046B (SPARSE_SUPER LARGE_FILE HUGE_FILE DIR_NLINK EXTRA_ISIZE)
Filesystem flags:          Signed directory hash in use
Default mount options:     0x0000000C (XATTR_USER ACL)
Filesystem state:          Cleanly umounted
Errors behavior:           Continue
Filesystem OS type:        Linux
Inode count:               98304
Block count:               393216
Reserved block count:      19660
Free blocks:               377692
Free inodes:               98293
First data block:          0
Block size:                4096
Fragment size:             4096
Reserved GDT blocks:       191
Blocks per group:          32768
Fragments per group:       32768
Inodes per group:          8192
Inode blocks per group:    512
Last mount time:           none
Last write time:           17.11.2025 21:56:30
Mount count:               0
Maximum mount count:       -1
Last checked:              17.11.2025 21:56:30
Check interval:            0
Reserved blocks uid:       0 (user unknown)
Reserved blocks gid:       0 (group unknown)
First inode:               11
Inode size:                256
Journal superblock UUID:   00000000-0000-0000-0000-000000000000
Journal inode:             8
Journal device number:     0x00000000
Default directory hash:    Half MD4.
Directory Hash Seed:       37DFCF83-C3BD-11F0-91C0-C111C441E86E
Journal inode backup type: 0x01 (EXT3_JNL_BACKUP_BLOCKS)

Output information about the superblock of system.img finish success
