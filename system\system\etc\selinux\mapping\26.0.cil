;; attributes removed from current policy
(typeattribute hal_wifi_keystore)
(typeattribute hal_wifi_keystore_client)
(typeattribute hal_wifi_keystore_server)

;; types removed from current policy
(type asan_reboot_prop)
(type log_device)
(type mediacasserver_service)
(type reboot_data_file)
(type tracing_shell_writable)
(type tracing_shell_writable_debug)
(type vold_socket)
(type webview_zygote_socket)
(type rild)

(typeattributeset accessibility_service_26_0 (accessibility_service))
(typeattributeset account_service_26_0 (account_service))
(typeattributeset activity_service_26_0 (activity_service))
(typeattributeset adbd_26_0 (adbd))
(typeattributeset adb_data_file_26_0 (adb_data_file))
(typeattributeset adbd_socket_26_0 (adbd_socket))
(typeattributeset adb_keys_file_26_0 (adb_keys_file))
(typeattributeset alarm_device_26_0 (alarm_device))
(typeattributeset alarm_service_26_0 (alarm_service))
(typeattributeset anr_data_file_26_0 (anr_data_file))
(typeattributeset apk_data_file_26_0 (apk_data_file))
(typeattributeset apk_private_data_file_26_0 (apk_private_data_file))
(typeattributeset apk_private_tmp_file_26_0 (apk_private_tmp_file))
(typeattributeset apk_tmp_file_26_0 (apk_tmp_file))
(typeattributeset app_data_file_26_0 (app_data_file))
(typeattributeset app_fuse_file_26_0 (app_fuse_file))
(typeattributeset app_fusefs_26_0 (app_fusefs))
(typeattributeset appops_service_26_0 (appops_service))
(typeattributeset appwidget_service_26_0 (appwidget_service))
(typeattributeset asan_reboot_prop_26_0 (asan_reboot_prop))
(typeattributeset asec_apk_file_26_0 (asec_apk_file))
(typeattributeset asec_image_file_26_0 (asec_image_file))
(typeattributeset asec_public_file_26_0 (asec_public_file))
(typeattributeset ashmem_device_26_0 (ashmem_device))
(typeattributeset assetatlas_service_26_0 (assetatlas_service))
(typeattributeset audio_data_file_26_0 (audio_data_file))
(typeattributeset audio_device_26_0 (audio_device))
(typeattributeset audiohal_data_file_26_0 (audiohal_data_file))
(typeattributeset audio_prop_26_0 (audio_prop))
(typeattributeset audio_seq_device_26_0 (audio_seq_device))
(typeattributeset audioserver_26_0 (audioserver))
(typeattributeset audioserver_data_file_26_0 (audioserver_data_file))
(typeattributeset audioserver_service_26_0 (audioserver_service))
(typeattributeset audio_service_26_0 (audio_service))
(typeattributeset audio_timer_device_26_0 (audio_timer_device))
(typeattributeset autofill_service_26_0 (autofill_service))
(typeattributeset backup_data_file_26_0 (backup_data_file))
(typeattributeset backup_service_26_0 (backup_service))
(typeattributeset batteryproperties_service_26_0 (batteryproperties_service))
(typeattributeset battery_service_26_0 (battery_service))
(typeattributeset batterystats_service_26_0 (batterystats_service))
(typeattributeset binder_device_26_0 (binder_device))
(typeattributeset binfmt_miscfs_26_0 (binfmt_miscfs))
(typeattributeset blkid_26_0 (blkid))
(typeattributeset blkid_untrusted_26_0 (blkid_untrusted))
(typeattributeset block_device_26_0 (block_device))
(typeattributeset bluetooth_26_0 (bluetooth))
(typeattributeset bluetooth_data_file_26_0 (bluetooth_data_file))
(typeattributeset bluetooth_efs_file_26_0 (bluetooth_efs_file))
(typeattributeset bluetooth_logs_data_file_26_0 (bluetooth_logs_data_file))
(typeattributeset bluetooth_manager_service_26_0 (bluetooth_manager_service))
(typeattributeset bluetooth_prop_26_0 (bluetooth_prop))
(typeattributeset bluetooth_service_26_0 (bluetooth_service))
(typeattributeset bluetooth_socket_26_0 (bluetooth_socket))
(typeattributeset bootanim_26_0 (bootanim))
(typeattributeset bootanim_exec_26_0 (bootanim_exec))
(typeattributeset boot_block_device_26_0 (boot_block_device))
(typeattributeset bootchart_data_file_26_0 (bootchart_data_file))
(typeattributeset bootstat_26_0 (bootstat))
(typeattributeset bootstat_data_file_26_0 (bootstat_data_file))
(typeattributeset bootstat_exec_26_0 (bootstat_exec))
(typeattributeset boottime_prop_26_0 (boottime_prop))
(typeattributeset boottrace_data_file_26_0 (boottrace_data_file))
(typeattributeset bufferhubd_26_0 (bufferhubd))
(typeattributeset bufferhubd_exec_26_0 (bufferhubd_exec))
(typeattributeset cache_backup_file_26_0 (cache_backup_file))
(typeattributeset cache_block_device_26_0 (cache_block_device))
(typeattributeset cache_file_26_0 (cache_file))
(typeattributeset cache_private_backup_file_26_0 (cache_private_backup_file))
(typeattributeset cache_recovery_file_26_0 (cache_recovery_file))
(typeattributeset camera_data_file_26_0 (camera_data_file))
(typeattributeset camera_device_26_0 (camera_device))
(typeattributeset cameraproxy_service_26_0 (cameraproxy_service))
(typeattributeset cameraserver_26_0 (cameraserver))
(typeattributeset cameraserver_exec_26_0 (cameraserver_exec))
(typeattributeset cameraserver_service_26_0 (cameraserver_service))
(typeattributeset cgroup_26_0 (cgroup))
(typeattributeset charger_26_0 (charger))
(typeattributeset clatd_26_0 (clatd))
(typeattributeset clatd_exec_26_0 (clatd_exec))
(typeattributeset clipboard_service_26_0 (clipboard_service))
(typeattributeset commontime_management_service_26_0 (commontime_management_service))
(typeattributeset companion_device_service_26_0 (companion_device_service))
(typeattributeset configfs_26_0 (configfs))
(typeattributeset config_prop_26_0 (config_prop))
(typeattributeset connectivity_service_26_0 (connectivity_service))
(typeattributeset connmetrics_service_26_0 (connmetrics_service))
(typeattributeset console_device_26_0 (console_device))
(typeattributeset consumer_ir_service_26_0 (consumer_ir_service))
(typeattributeset content_service_26_0 (content_service))
(typeattributeset contexthub_service_26_0 (contexthub_service))
(typeattributeset coredump_file_26_0 (coredump_file))
(typeattributeset country_detector_service_26_0 (country_detector_service))
(typeattributeset coverage_service_26_0 (coverage_service))
(typeattributeset cppreopt_prop_26_0 (cppreopt_prop))
(typeattributeset cppreopts_26_0 (cppreopts))
(typeattributeset cppreopts_exec_26_0 (cppreopts_exec))
(typeattributeset cpuctl_device_26_0 (cpuctl_device))
(typeattributeset cpuinfo_service_26_0 (cpuinfo_service))
(typeattributeset crash_dump_26_0 (crash_dump))
(typeattributeset crash_dump_exec_26_0 (crash_dump_exec))
(typeattributeset ctl_bootanim_prop_26_0 (ctl_bootanim_prop))
(typeattributeset ctl_bugreport_prop_26_0 (ctl_bugreport_prop))
(typeattributeset ctl_console_prop_26_0 (ctl_console_prop))
(typeattributeset ctl_default_prop_26_0 (ctl_default_prop ctl_restart_prop ctl_start_prop ctl_stop_prop))
(typeattributeset ctl_dumpstate_prop_26_0 (ctl_dumpstate_prop))
(typeattributeset ctl_fuse_prop_26_0 (ctl_fuse_prop))
(typeattributeset ctl_mdnsd_prop_26_0 (ctl_mdnsd_prop))
(typeattributeset ctl_rildaemon_prop_26_0 (ctl_rildaemon_prop))
(typeattributeset dalvikcache_data_file_26_0 (dalvikcache_data_file))
(typeattributeset dalvik_prop_26_0 (dalvik_prop))
(typeattributeset dbinfo_service_26_0 (dbinfo_service))
(typeattributeset debugfs_26_0
  ( debugfs
    debugfs_wakeup_sources
  ))
(typeattributeset debugfs_mmc_26_0 (debugfs_mmc))
(typeattributeset debugfs_trace_marker_26_0 (debugfs_trace_marker))
(typeattributeset debugfs_tracing_26_0 (debugfs_tracing))
(typeattributeset debugfs_tracing_instances_26_0 (debugfs_tracing_instances))
(typeattributeset debugfs_wifi_tracing_26_0 (debugfs_wifi_tracing))
(typeattributeset debuggerd_prop_26_0 (debuggerd_prop))
(typeattributeset debug_prop_26_0 (debug_prop))
(typeattributeset default_android_hwservice_26_0 (default_android_hwservice))
(typeattributeset default_android_service_26_0 (default_android_service))
(typeattributeset default_android_vndservice_26_0 (default_android_vndservice))
(typeattributeset default_prop_26_0
  ( default_prop pm_prop))
(typeattributeset device_26_0 (device))
(typeattributeset device_identifiers_service_26_0 (device_identifiers_service))
(typeattributeset deviceidle_service_26_0 (deviceidle_service))
(typeattributeset device_logging_prop_26_0 (device_logging_prop))
(typeattributeset device_policy_service_26_0 (device_policy_service))
(typeattributeset devicestoragemonitor_service_26_0 (devicestoragemonitor_service))
(typeattributeset devpts_26_0 (devpts))
(typeattributeset dex2oat_26_0 (dex2oat))
(typeattributeset dex2oat_exec_26_0 (dex2oat_exec))
(typeattributeset dhcp_26_0 (dhcp))
(typeattributeset dhcp_data_file_26_0 (dhcp_data_file))
(typeattributeset dhcp_exec_26_0 (dhcp_exec))
(typeattributeset dhcp_prop_26_0 (dhcp_prop))
(typeattributeset diskstats_service_26_0 (diskstats_service))
(typeattributeset display_service_26_0 (display_service))
(typeattributeset dm_device_26_0 (dm_device))
(typeattributeset dnsmasq_26_0 (dnsmasq))
(typeattributeset dnsmasq_exec_26_0 (dnsmasq_exec))
(typeattributeset dnsproxyd_socket_26_0 (dnsproxyd_socket))
(typeattributeset DockObserver_service_26_0 (DockObserver_service))
(typeattributeset dreams_service_26_0 (dreams_service))
(typeattributeset drm_data_file_26_0 (drm_data_file))
(typeattributeset drmserver_26_0 (drmserver))
(typeattributeset drmserver_exec_26_0 (drmserver_exec))
(typeattributeset drmserver_service_26_0 (drmserver_service))
(typeattributeset drmserver_socket_26_0 (drmserver_socket))
(typeattributeset dropbox_service_26_0 (dropbox_service))
(typeattributeset dumpstate_26_0 (dumpstate))
(typeattributeset dumpstate_exec_26_0 (dumpstate_exec))
(typeattributeset dumpstate_options_prop_26_0 (dumpstate_options_prop))
(typeattributeset dumpstate_prop_26_0 (dumpstate_prop))
(typeattributeset dumpstate_service_26_0 (dumpstate_service))
(typeattributeset dumpstate_socket_26_0 (dumpstate_socket))
(typeattributeset efs_file_26_0 (efs_file))
(typeattributeset ephemeral_app_26_0 (ephemeral_app))
(typeattributeset ethernet_service_26_0 (ethernet_service))
(typeattributeset ffs_prop_26_0 (ffs_prop))
(typeattributeset file_contexts_file_26_0 (file_contexts_file))
(typeattributeset fingerprintd_26_0 (fingerprintd))
(typeattributeset fingerprintd_data_file_26_0 (fingerprintd_data_file))
(typeattributeset fingerprintd_exec_26_0 (fingerprintd_exec))
(typeattributeset fingerprintd_service_26_0 (fingerprintd_service))
(typeattributeset fingerprint_prop_26_0 (fingerprint_prop))
(typeattributeset fingerprint_service_26_0 (fingerprint_service))
(typeattributeset firstboot_prop_26_0 (firstboot_prop))
(typeattributeset font_service_26_0 (font_service))
(typeattributeset frp_block_device_26_0 (frp_block_device))
(typeattributeset fsck_26_0 (fsck))
(typeattributeset fsck_exec_26_0 (fsck_exec))
(typeattributeset fscklogs_26_0 (fscklogs))
(typeattributeset fsck_untrusted_26_0 (fsck_untrusted))
(typeattributeset full_device_26_0 (full_device))
(typeattributeset functionfs_26_0 (functionfs))
(typeattributeset fuse_26_0 (fuse))
(typeattributeset fuse_device_26_0 (fuse_device))
(typeattributeset fwk_display_hwservice_26_0 (fwk_display_hwservice))
(typeattributeset fwk_scheduler_hwservice_26_0 (fwk_scheduler_hwservice))
(typeattributeset fwk_sensor_hwservice_26_0 (fwk_sensor_hwservice))
(typeattributeset fwmarkd_socket_26_0 (fwmarkd_socket))
(typeattributeset gatekeeperd_26_0 (gatekeeperd))
(typeattributeset gatekeeper_data_file_26_0 (gatekeeper_data_file))
(typeattributeset gatekeeperd_exec_26_0 (gatekeeperd_exec))
(typeattributeset gatekeeper_service_26_0 (gatekeeper_service))
(typeattributeset gfxinfo_service_26_0 (gfxinfo_service))
(typeattributeset gps_control_26_0 (gps_control))
(typeattributeset gpu_device_26_0 (gpu_device))
(typeattributeset gpu_service_26_0 (gpu_service))
(typeattributeset graphics_device_26_0 (graphics_device))
(typeattributeset graphicsstats_service_26_0 (graphicsstats_service))
(typeattributeset hal_audio_hwservice_26_0 (hal_audio_hwservice))
(typeattributeset hal_bluetooth_hwservice_26_0 (hal_bluetooth_hwservice))
(typeattributeset hal_bootctl_hwservice_26_0 (hal_bootctl_hwservice))
(typeattributeset hal_camera_hwservice_26_0 (hal_camera_hwservice))
(typeattributeset hal_configstore_ISurfaceFlingerConfigs_26_0 (hal_configstore_ISurfaceFlingerConfigs))
(typeattributeset hal_contexthub_hwservice_26_0 (hal_contexthub_hwservice))
(typeattributeset hal_drm_hwservice_26_0 (hal_drm_hwservice))
(typeattributeset hal_dumpstate_hwservice_26_0 (hal_dumpstate_hwservice))
(typeattributeset hal_fingerprint_hwservice_26_0 (hal_fingerprint_hwservice))
(typeattributeset hal_fingerprint_service_26_0 (hal_fingerprint_service))
(typeattributeset hal_gatekeeper_hwservice_26_0 (hal_gatekeeper_hwservice))
(typeattributeset hal_gnss_hwservice_26_0 (hal_gnss_hwservice))
(typeattributeset hal_graphics_allocator_hwservice_26_0 (hal_graphics_allocator_hwservice))
(typeattributeset hal_graphics_composer_hwservice_26_0 (hal_graphics_composer_hwservice))
(typeattributeset hal_graphics_mapper_hwservice_26_0 (hal_graphics_mapper_hwservice))
(typeattributeset hal_health_hwservice_26_0 (hal_health_hwservice))
(typeattributeset hal_ir_hwservice_26_0 (hal_ir_hwservice))
(typeattributeset hal_keymaster_hwservice_26_0 (hal_keymaster_hwservice))
(typeattributeset hal_light_hwservice_26_0 (hal_light_hwservice))
(typeattributeset hal_memtrack_hwservice_26_0 (hal_memtrack_hwservice))
(typeattributeset hal_nfc_hwservice_26_0 (hal_nfc_hwservice))
(typeattributeset hal_oemlock_hwservice_26_0 (hal_oemlock_hwservice))
(typeattributeset hal_omx_hwservice_26_0 (hal_omx_hwservice))
(typeattributeset hal_power_hwservice_26_0 (hal_power_hwservice))
(typeattributeset hal_renderscript_hwservice_26_0 (hal_renderscript_hwservice))
(typeattributeset hal_sensors_hwservice_26_0 (hal_sensors_hwservice))
(typeattributeset hal_telephony_hwservice_26_0 (hal_telephony_hwservice))
(typeattributeset hal_thermal_hwservice_26_0 (hal_thermal_hwservice))
(typeattributeset hal_tv_cec_hwservice_26_0 (hal_tv_cec_hwservice))
(typeattributeset hal_tv_input_hwservice_26_0 (hal_tv_input_hwservice))
(typeattributeset hal_usb_hwservice_26_0 (hal_usb_hwservice))
(typeattributeset hal_vibrator_hwservice_26_0 (hal_vibrator_hwservice))
(typeattributeset hal_vr_hwservice_26_0 (hal_vr_hwservice))
(typeattributeset hal_weaver_hwservice_26_0 (hal_weaver_hwservice))
(typeattributeset hal_wifi_hwservice_26_0 (hal_wifi_hwservice))
(typeattributeset hal_wifi_supplicant_hwservice_26_0 (hal_wifi_supplicant_hwservice))
(typeattributeset hardware_properties_service_26_0 (hardware_properties_service))
(typeattributeset hardware_service_26_0 (hardware_service))
(typeattributeset hci_attach_dev_26_0 (hci_attach_dev))
(typeattributeset hdmi_control_service_26_0 (hdmi_control_service))
(typeattributeset healthd_26_0 (healthd))
(typeattributeset healthd_exec_26_0 (healthd_exec))
(typeattributeset heapdump_data_file_26_0 (heapdump_data_file))
(typeattributeset hidl_allocator_hwservice_26_0 (hidl_allocator_hwservice))
(typeattributeset hidl_base_hwservice_26_0 (hidl_base_hwservice))
(typeattributeset hidl_manager_hwservice_26_0 (hidl_manager_hwservice))
(typeattributeset hidl_memory_hwservice_26_0 (hidl_memory_hwservice))
(typeattributeset hidl_token_hwservice_26_0 (hidl_token_hwservice))
(typeattributeset hwbinder_device_26_0 (hwbinder_device))
(typeattributeset hw_random_device_26_0 (hw_random_device))
(typeattributeset hwservice_contexts_file_26_0 (hwservice_contexts_file))
(typeattributeset hwservicemanager_26_0 (hwservicemanager))
(typeattributeset hwservicemanager_exec_26_0 (hwservicemanager_exec))
(typeattributeset hwservicemanager_prop_26_0 (hwservicemanager_prop))
(typeattributeset i2c_device_26_0 (i2c_device))
(typeattributeset icon_file_26_0 (icon_file))
(typeattributeset idmap_26_0 (idmap))
(typeattributeset idmap_exec_26_0 (idmap_exec))
(typeattributeset iio_device_26_0 (iio_device))
(typeattributeset imms_service_26_0 (imms_service))
(typeattributeset incident_26_0 (incident))
(typeattributeset incidentd_26_0 (incidentd))
(typeattributeset incident_data_file_26_0 (incident_data_file))
(typeattributeset incident_service_26_0 (incident_service))
(typeattributeset init_26_0 (init))
(typeattributeset init_exec_26_0 (init_exec))
(typeattributeset inotify_26_0 (inotify))
(typeattributeset input_device_26_0 (input_device))
(typeattributeset inputflinger_26_0 (inputflinger))
(typeattributeset inputflinger_exec_26_0 (inputflinger_exec))
(typeattributeset inputflinger_service_26_0 (inputflinger_service))
(typeattributeset input_method_service_26_0 (input_method_service))
(typeattributeset input_service_26_0 (input_service))
(typeattributeset installd_26_0 (installd))
(typeattributeset install_data_file_26_0 (install_data_file))
(typeattributeset installd_exec_26_0 (installd_exec))
(typeattributeset installd_service_26_0 (installd_service))
(typeattributeset install_recovery_26_0 (install_recovery))
(typeattributeset install_recovery_exec_26_0 (install_recovery_exec))
(typeattributeset ion_device_26_0 (ion_device))
(typeattributeset IProxyService_service_26_0 (IProxyService_service))
(typeattributeset ipsec_service_26_0 (ipsec_service))
(typeattributeset isolated_app_26_0 (isolated_app))
(typeattributeset jobscheduler_service_26_0 (jobscheduler_service))
(typeattributeset kernel_26_0 (kernel))
(typeattributeset keychain_data_file_26_0 (keychain_data_file))
(typeattributeset keychord_device_26_0 (keychord_device))
(typeattributeset keystore_26_0 (keystore))
(typeattributeset keystore_data_file_26_0 (keystore_data_file))
(typeattributeset keystore_exec_26_0 (keystore_exec))
(typeattributeset keystore_service_26_0 (keystore_service))
(typeattributeset kmem_device_26_0 (kmem_device))
(typeattributeset kmsg_device_26_0 (kmsg_device))
(typeattributeset labeledfs_26_0 (labeledfs))
(typeattributeset launcherapps_service_26_0 (launcherapps_service))
(typeattributeset lmkd_26_0 (lmkd))
(typeattributeset lmkd_exec_26_0 (lmkd_exec))
(typeattributeset lmkd_socket_26_0 (lmkd_socket))
(typeattributeset location_service_26_0 (location_service))
(typeattributeset lock_settings_service_26_0 (lock_settings_service))
(typeattributeset logcat_exec_26_0 (logcat_exec))
(typeattributeset logd_26_0 (logd))
(typeattributeset log_device_26_0 (log_device))
(typeattributeset logd_exec_26_0 (logd_exec))
(typeattributeset logd_prop_26_0 (logd_prop))
(typeattributeset logdr_socket_26_0 (logdr_socket))
(typeattributeset logd_socket_26_0 (logd_socket))
(typeattributeset logdw_socket_26_0 (logdw_socket))
(typeattributeset logpersist_26_0 (logpersist))
(typeattributeset logpersistd_logging_prop_26_0 (logpersistd_logging_prop))
(typeattributeset log_prop_26_0 (log_prop))
(typeattributeset log_tag_prop_26_0 (log_tag_prop))
(typeattributeset loop_control_device_26_0 (loop_control_device))
(typeattributeset loop_device_26_0 (loop_device))
(typeattributeset mac_perms_file_26_0 (mac_perms_file))
(typeattributeset mdnsd_26_0 (mdnsd))
(typeattributeset mdnsd_socket_26_0 (mdnsd_socket))
(typeattributeset mdns_socket_26_0 (mdns_socket))
(typeattributeset mediacasserver_service_26_0 (mediacasserver_service))
(typeattributeset mediacodec_26_0 (mediacodec))
(typeattributeset mediacodec_exec_26_0 (mediacodec_exec))
(typeattributeset mediacodec_service_26_0 (mediacodec_service))
(typeattributeset media_data_file_26_0 (media_data_file))
(typeattributeset mediadrmserver_26_0 (mediadrmserver))
(typeattributeset mediadrmserver_exec_26_0 (mediadrmserver_exec))
(typeattributeset mediadrmserver_service_26_0 (mediadrmserver_service))
(typeattributeset mediaextractor_26_0 (mediaextractor))
(typeattributeset mediaextractor_exec_26_0 (mediaextractor_exec))
(typeattributeset mediaextractor_service_26_0 (mediaextractor_service))
(typeattributeset mediametrics_26_0 (mediametrics))
(typeattributeset mediametrics_exec_26_0 (mediametrics_exec))
(typeattributeset mediametrics_service_26_0 (mediametrics_service))
(typeattributeset media_projection_service_26_0 (media_projection_service))
(typeattributeset media_router_service_26_0 (media_router_service))
(typeattributeset media_rw_data_file_26_0 (media_rw_data_file))
(typeattributeset mediaserver_26_0 (mediaserver))
(typeattributeset mediaserver_exec_26_0 (mediaserver_exec))
(typeattributeset mediaserver_service_26_0 (mediaserver_service))
(typeattributeset media_session_service_26_0 (media_session_service))
(typeattributeset meminfo_service_26_0 (meminfo_service))
(typeattributeset metadata_block_device_26_0 (metadata_block_device))
(typeattributeset method_trace_data_file_26_0 (method_trace_data_file))
(typeattributeset midi_service_26_0 (midi_service))
(typeattributeset misc_block_device_26_0 (misc_block_device))
(typeattributeset misc_logd_file_26_0 (misc_logd_file))
(typeattributeset misc_user_data_file_26_0 (misc_user_data_file))
(typeattributeset mmc_prop_26_0 (mmc_prop))
(typeattributeset mnt_expand_file_26_0 (mnt_expand_file))
(typeattributeset mnt_media_rw_file_26_0 (mnt_media_rw_file))
(typeattributeset mnt_media_rw_stub_file_26_0 (mnt_media_rw_stub_file))
(typeattributeset mnt_user_file_26_0 (mnt_user_file))
(typeattributeset modprobe_26_0 (modprobe))
(typeattributeset mount_service_26_0 (mount_service))
(typeattributeset mqueue_26_0 (mqueue))
(typeattributeset mtd_device_26_0 (mtd_device))
(typeattributeset mtp_26_0 (mtp))
(typeattributeset mtp_device_26_0 (mtp_device))
(typeattributeset mtpd_socket_26_0 (mtpd_socket))
(typeattributeset mtp_exec_26_0 (mtp_exec))
(typeattributeset nativetest_data_file_26_0 (nativetest_data_file))
(typeattributeset netd_26_0 (netd))
(typeattributeset net_data_file_26_0 (net_data_file))
(typeattributeset netd_exec_26_0 (netd_exec))
(typeattributeset netd_listener_service_26_0 (netd_listener_service))
(typeattributeset net_dns_prop_26_0 (net_dns_prop))
(typeattributeset netd_service_26_0 (netd_service))
(typeattributeset netd_socket_26_0 (netd_socket))
(typeattributeset netif_26_0 (netif))
(typeattributeset netpolicy_service_26_0 (netpolicy_service))
(typeattributeset net_radio_prop_26_0 (net_radio_prop))
(typeattributeset netstats_service_26_0 (netstats_service))
(typeattributeset netutils_wrapper_26_0 (netutils_wrapper))
(typeattributeset netutils_wrapper_exec_26_0 (netutils_wrapper_exec))
(typeattributeset network_management_service_26_0 (network_management_service))
(typeattributeset network_score_service_26_0 (network_score_service))
(typeattributeset network_time_update_service_26_0 (network_time_update_service))
(typeattributeset nfc_26_0 (nfc))
(typeattributeset nfc_data_file_26_0 (nfc_data_file))
(typeattributeset nfc_device_26_0 (nfc_device))
(typeattributeset nfc_prop_26_0 (nfc_prop))
(typeattributeset nfc_service_26_0 (nfc_service))
(typeattributeset node_26_0 (node))
(typeattributeset notification_service_26_0 (notification_service))
(typeattributeset null_device_26_0 (null_device))
(typeattributeset oemfs_26_0 (oemfs))
(typeattributeset oem_lock_service_26_0 (oem_lock_service))
(typeattributeset ota_data_file_26_0 (ota_data_file))
(typeattributeset otadexopt_service_26_0 (otadexopt_service))
(typeattributeset ota_package_file_26_0 (ota_package_file))
(typeattributeset otapreopt_chroot_26_0 (otapreopt_chroot))
(typeattributeset otapreopt_chroot_exec_26_0 (otapreopt_chroot_exec))
(typeattributeset otapreopt_slot_26_0 (otapreopt_slot))
(typeattributeset otapreopt_slot_exec_26_0 (otapreopt_slot_exec))
(typeattributeset overlay_prop_26_0 (overlay_prop))
(typeattributeset overlay_service_26_0 (overlay_service))
(typeattributeset owntty_device_26_0 (owntty_device))
(typeattributeset package_service_26_0 (package_service))
(typeattributeset pan_result_prop_26_0 (pan_result_prop))
(typeattributeset pdx_bufferhub_client_channel_socket_26_0 (pdx_bufferhub_client_channel_socket))
(typeattributeset pdx_bufferhub_client_endpoint_socket_26_0 (pdx_bufferhub_client_endpoint_socket))
(typeattributeset pdx_bufferhub_dir_26_0 (pdx_bufferhub_dir))
(typeattributeset pdx_display_client_channel_socket_26_0 (pdx_display_client_channel_socket))
(typeattributeset pdx_display_client_endpoint_socket_26_0 (pdx_display_client_endpoint_socket))
(typeattributeset pdx_display_dir_26_0 (pdx_display_dir))
(typeattributeset pdx_display_manager_channel_socket_26_0 (pdx_display_manager_channel_socket))
(typeattributeset pdx_display_manager_endpoint_socket_26_0 (pdx_display_manager_endpoint_socket))
(typeattributeset pdx_display_screenshot_channel_socket_26_0 (pdx_display_screenshot_channel_socket))
(typeattributeset pdx_display_screenshot_endpoint_socket_26_0 (pdx_display_screenshot_endpoint_socket))
(typeattributeset pdx_display_vsync_channel_socket_26_0 (pdx_display_vsync_channel_socket))
(typeattributeset pdx_display_vsync_endpoint_socket_26_0 (pdx_display_vsync_endpoint_socket))
(typeattributeset pdx_performance_client_channel_socket_26_0 (pdx_performance_client_channel_socket))
(typeattributeset pdx_performance_client_endpoint_socket_26_0 (pdx_performance_client_endpoint_socket))
(typeattributeset pdx_performance_dir_26_0 (pdx_performance_dir))
(typeattributeset performanced_26_0 (performanced))
(typeattributeset performanced_exec_26_0 (performanced_exec))
(typeattributeset perfprofd_26_0 (perfprofd))
(typeattributeset perfprofd_data_file_26_0 (perfprofd_data_file))
(typeattributeset perfprofd_exec_26_0 (perfprofd_exec))
(typeattributeset permission_service_26_0 (permission_service))
(typeattributeset persist_debug_prop_26_0 (persist_debug_prop))
(typeattributeset persistent_data_block_service_26_0 (persistent_data_block_service))
(typeattributeset persistent_properties_ready_prop_26_0 (persistent_properties_ready_prop))
(typeattributeset pinner_service_26_0 (pinner_service))
(typeattributeset pipefs_26_0 (pipefs))
(typeattributeset platform_app_26_0 (platform_app))
(typeattributeset pmsg_device_26_0 (pmsg_device))
(typeattributeset port_26_0 (port))
(typeattributeset port_device_26_0 (port_device))
(typeattributeset postinstall_26_0 (postinstall))
(typeattributeset postinstall_dexopt_26_0 (postinstall_dexopt))
(typeattributeset postinstall_file_26_0 (postinstall_file))
(typeattributeset postinstall_mnt_dir_26_0 (postinstall_mnt_dir))
(typeattributeset powerctl_prop_26_0 (powerctl_prop))
(typeattributeset power_service_26_0 (power_service))
(typeattributeset ppp_26_0 (ppp))
(typeattributeset ppp_device_26_0 (ppp_device))
(typeattributeset ppp_exec_26_0 (ppp_exec))
(typeattributeset preloads_data_file_26_0 (preloads_data_file))
(typeattributeset preloads_media_file_26_0 (preloads_media_file))
(typeattributeset preopt2cachename_26_0 (preopt2cachename))
(typeattributeset preopt2cachename_exec_26_0 (preopt2cachename_exec))
(typeattributeset print_service_26_0 (print_service))
(typeattributeset priv_app_26_0 (mediaprovider priv_app))
(typeattributeset proc_26_0
  ( proc
    proc_abi
    proc_asound
    proc_buddyinfo
    proc_cmdline
    proc_dirty
    proc_diskstats
    proc_extra_free_kbytes
    proc_filesystems
    proc_hostname
    proc_hung_task
    proc_kmsg
    proc_loadavg
    proc_max_map_count
    proc_min_free_order_shift
    proc_mounts
    proc_page_cluster
    proc_pagetypeinfo
    proc_panic
    proc_pid_max
    proc_pipe_conf
    proc_random
    proc_sched
    proc_swaps
    proc_uid_time_in_state
    proc_uid_concurrent_active_time
    proc_uid_concurrent_policy_time
    proc_uid_cpupower
    proc_uptime
    proc_version
    proc_vmallocinfo
    proc_vmstat))
(typeattributeset proc_bluetooth_writable_26_0 (proc_bluetooth_writable))
(typeattributeset proc_cpuinfo_26_0 (proc_cpuinfo))
(typeattributeset proc_drop_caches_26_0 (proc_drop_caches))
(typeattributeset processinfo_service_26_0 (processinfo_service))
(typeattributeset proc_interrupts_26_0 (proc_interrupts))
(typeattributeset proc_iomem_26_0 (proc_iomem))
(typeattributeset proc_meminfo_26_0 (proc_meminfo))
(typeattributeset proc_misc_26_0 (proc_misc))
(typeattributeset proc_modules_26_0 (proc_modules))
(typeattributeset proc_net_26_0
  ( proc_net
    proc_qtaguid_stat))
(typeattributeset proc_overcommit_memory_26_0 (proc_overcommit_memory))
(typeattributeset proc_perf_26_0 (proc_perf))
(typeattributeset proc_security_26_0 (proc_security))
(typeattributeset proc_stat_26_0 (proc_stat))
(typeattributeset procstats_service_26_0 (procstats_service))
(typeattributeset proc_sysrq_26_0 (proc_sysrq))
(typeattributeset proc_timer_26_0 (proc_timer))
(typeattributeset proc_tty_drivers_26_0 (proc_tty_drivers))
(typeattributeset proc_uid_cputime_removeuid_26_0 (proc_uid_cputime_removeuid))
(typeattributeset proc_uid_cputime_showstat_26_0 (proc_uid_cputime_showstat))
(typeattributeset proc_uid_io_stats_26_0 (proc_uid_io_stats))
(typeattributeset proc_uid_procstat_set_26_0 (proc_uid_procstat_set))
(typeattributeset proc_zoneinfo_26_0 (proc_zoneinfo))
(typeattributeset profman_26_0 (profman))
(typeattributeset profman_dump_data_file_26_0 (profman_dump_data_file))
(typeattributeset profman_exec_26_0 (profman_exec))
(typeattributeset properties_device_26_0 (properties_device))
(typeattributeset properties_serial_26_0 (properties_serial))
(typeattributeset property_contexts_file_26_0 (property_contexts_file))
(typeattributeset property_data_file_26_0 (property_data_file))
(typeattributeset property_socket_26_0 (property_socket))
(typeattributeset pstorefs_26_0 (pstorefs))
(typeattributeset ptmx_device_26_0 (ptmx_device))
(typeattributeset qtaguid_device_26_0 (qtaguid_device))
(typeattributeset qtaguid_proc_26_0 (qtaguid_proc))
(typeattributeset racoon_26_0 (racoon))
(typeattributeset racoon_exec_26_0 (racoon_exec))
(typeattributeset racoon_socket_26_0 (racoon_socket))
(typeattributeset radio_26_0 (radio))
(typeattributeset radio_data_file_26_0 (radio_data_file))
(typeattributeset radio_device_26_0 (radio_device))
(typeattributeset radio_prop_26_0 (radio_prop))
(typeattributeset radio_service_26_0 (radio_service))
(typeattributeset ram_device_26_0 (ram_device))
(typeattributeset random_device_26_0 (random_device))
(typeattributeset reboot_data_file_26_0 (reboot_data_file))
(typeattributeset recovery_26_0 (recovery))
(typeattributeset recovery_block_device_26_0 (recovery_block_device))
(typeattributeset recovery_data_file_26_0 (recovery_data_file))
(typeattributeset recovery_persist_26_0 (recovery_persist))
(typeattributeset recovery_persist_exec_26_0 (recovery_persist_exec))
(typeattributeset recovery_refresh_26_0 (recovery_refresh))
(typeattributeset recovery_refresh_exec_26_0 (recovery_refresh_exec))
(typeattributeset recovery_service_26_0 (recovery_service))
(typeattributeset registry_service_26_0 (registry_service))
(typeattributeset resourcecache_data_file_26_0 (resourcecache_data_file))
(typeattributeset restorecon_prop_26_0 (restorecon_prop))
(typeattributeset restrictions_service_26_0 (restrictions_service))
(typeattributeset rild_26_0 (rild))
(typeattributeset rild_debug_socket_26_0 (rild_debug_socket))
(typeattributeset rild_socket_26_0 (rild_socket))
(typeattributeset ringtone_file_26_0 (ringtone_file))
(typeattributeset root_block_device_26_0 (root_block_device))
(typeattributeset rootfs_26_0 (rootfs))
(typeattributeset rpmsg_device_26_0 (rpmsg_device))
(typeattributeset rtc_device_26_0 (rtc_device))
(typeattributeset rttmanager_service_26_0 (rttmanager_service))
(typeattributeset runas_26_0 (runas))
(typeattributeset runas_exec_26_0 (runas_exec))
(typeattributeset runtime_event_log_tags_file_26_0 (runtime_event_log_tags_file))
(typeattributeset safemode_prop_26_0 (safemode_prop))
(typeattributeset same_process_hal_file_26_0 (same_process_hal_file))
(typeattributeset samplingprofiler_service_26_0 (samplingprofiler_service))
(typeattributeset scheduling_policy_service_26_0 (scheduling_policy_service))
(typeattributeset sdcardd_26_0 (sdcardd))
(typeattributeset sdcardd_exec_26_0 (sdcardd_exec))
(typeattributeset sdcardfs_26_0 (sdcardfs))
(typeattributeset seapp_contexts_file_26_0 (seapp_contexts_file))
(typeattributeset search_service_26_0 (search_service))
(typeattributeset sec_key_att_app_id_provider_service_26_0 (sec_key_att_app_id_provider_service))
(typeattributeset selinuxfs_26_0 (selinuxfs))
(typeattributeset sensors_device_26_0 (sensors_device))
(typeattributeset sensorservice_service_26_0 (sensorservice_service))
(typeattributeset sepolicy_file_26_0 (sepolicy_file))
(typeattributeset serial_device_26_0 (serial_device))
(typeattributeset serialno_prop_26_0 (serialno_prop))
(typeattributeset serial_service_26_0 (serial_service))
(typeattributeset service_contexts_file_26_0 (service_contexts_file nonplat_service_contexts_file))
(typeattributeset servicediscovery_service_26_0 (servicediscovery_service))
(typeattributeset servicemanager_26_0 (servicemanager))
(typeattributeset servicemanager_exec_26_0 (servicemanager_exec))
(typeattributeset settings_service_26_0 (settings_service))
(typeattributeset sgdisk_26_0 (sgdisk))
(typeattributeset sgdisk_exec_26_0 (sgdisk_exec))
(typeattributeset shared_relro_26_0 (shared_relro))
(typeattributeset shared_relro_file_26_0 (shared_relro_file))
(typeattributeset shell_26_0 (shell))
(typeattributeset shell_data_file_26_0 (shell_data_file))
(typeattributeset shell_exec_26_0 (shell_exec))
(typeattributeset shell_prop_26_0 (shell_prop))
(typeattributeset shm_26_0 (shm))
(typeattributeset shortcut_manager_icons_26_0 (shortcut_manager_icons))
(typeattributeset shortcut_service_26_0 (shortcut_service))
(typeattributeset slideshow_26_0 (slideshow))
(typeattributeset socket_device_26_0 (socket_device))
(typeattributeset sockfs_26_0 (sockfs))
(typeattributeset statusbar_service_26_0 (statusbar_service))
(typeattributeset storaged_service_26_0 (storaged_service))
(typeattributeset storage_file_26_0 (storage_file))
(typeattributeset storagestats_service_26_0 (storagestats_service))
(typeattributeset storage_stub_file_26_0 (storage_stub_file))
(typeattributeset su_26_0 (su))
(typeattributeset su_exec_26_0 (su_exec))
(typeattributeset surfaceflinger_26_0 (surfaceflinger))
(typeattributeset surfaceflinger_service_26_0 (surfaceflinger_service))
(typeattributeset swap_block_device_26_0 (swap_block_device))
(typeattributeset sysfs_26_0
  ( sysfs
    sysfs_android_usb
    sysfs_dm
    sysfs_dt_firmware_android
    sysfs_ipv4
    sysfs_kernel_notes
    sysfs_net
    sysfs_power
    sysfs_rtc
    sysfs_switch
    sysfs_wakeup_reasons))
(typeattributeset sysfs_batteryinfo_26_0 (sysfs_batteryinfo))
(typeattributeset sysfs_bluetooth_writable_26_0 (sysfs_bluetooth_writable))
(typeattributeset sysfs_devices_system_cpu_26_0 (sysfs_devices_system_cpu))
(typeattributeset sysfs_hwrandom_26_0 (sysfs_hwrandom))
(typeattributeset sysfs_leds_26_0 (sysfs_leds))
(typeattributeset sysfs_lowmemorykiller_26_0 (sysfs_lowmemorykiller))
(typeattributeset sysfs_mac_address_26_0 (sysfs_mac_address))
(typeattributeset sysfs_nfc_power_writable_26_0 (sysfs_nfc_power_writable))
(typeattributeset sysfs_thermal_26_0 (sysfs_thermal))
(typeattributeset sysfs_uio_26_0 (sysfs_uio))
(typeattributeset sysfs_usb_26_0 (sysfs_usb))
(typeattributeset sysfs_vibrator_26_0 (sysfs_vibrator))
(typeattributeset sysfs_wake_lock_26_0 (sysfs_wake_lock))
(typeattributeset sysfs_wlan_fwpath_26_0 (sysfs_wlan_fwpath))
(typeattributeset sysfs_zram_26_0 (sysfs_zram))
(typeattributeset sysfs_zram_uevent_26_0 (sysfs_zram_uevent))
(typeattributeset system_app_26_0 (system_app))
(typeattributeset system_app_data_file_26_0 (system_app_data_file))
(typeattributeset system_app_service_26_0 (system_app_service))
(typeattributeset system_block_device_26_0 (system_block_device))
(typeattributeset system_data_file_26_0
  ( system_data_file
    vendor_data_file))
(typeattributeset system_file_26_0 (system_file))
(typeattributeset systemkeys_data_file_26_0 (systemkeys_data_file))
(typeattributeset system_ndebug_socket_26_0 (system_ndebug_socket))
(typeattributeset system_prop_26_0 (system_prop))
(typeattributeset system_radio_prop_26_0 (system_radio_prop))
(typeattributeset system_server_26_0 (system_server))
(typeattributeset system_wifi_keystore_hwservice_26_0 (system_wifi_keystore_hwservice))
(typeattributeset system_wpa_socket_26_0 (system_wpa_socket))
(typeattributeset task_service_26_0 (task_service))
(typeattributeset tee_26_0 (tee))
(typeattributeset tee_data_file_26_0 (tee_data_file))
(typeattributeset tee_device_26_0 (tee_device))
(typeattributeset telecom_service_26_0 (telecom_service))
(typeattributeset textclassification_service_26_0 (textclassification_service))
(typeattributeset textclassifier_data_file_26_0 (textclassifier_data_file))
(typeattributeset textservices_service_26_0 (textservices_service))
(typeattributeset tmpfs_26_0 (tmpfs))
(typeattributeset tombstoned_26_0 (tombstoned))
(typeattributeset tombstone_data_file_26_0 (tombstone_data_file))
(typeattributeset tombstoned_crash_socket_26_0 (tombstoned_crash_socket))
(typeattributeset tombstoned_exec_26_0 (tombstoned_exec))
(typeattributeset tombstoned_intercept_socket_26_0 (tombstoned_intercept_socket))
(typeattributeset toolbox_26_0 (toolbox))
(typeattributeset toolbox_exec_26_0 (toolbox_exec))
(typeattributeset tracing_shell_writable_26_0 (debugfs_tracing tracing_shell_writable))
(typeattributeset tracing_shell_writable_debug_26_0 (debugfs_tracing_debug tracing_shell_writable_debug))
(typeattributeset trust_service_26_0 (trust_service))
(typeattributeset tty_device_26_0 (tty_device))
(typeattributeset tun_device_26_0 (tun_device))
(typeattributeset tv_input_service_26_0 (tv_input_service))
(typeattributeset tzdatacheck_26_0 (tzdatacheck))
(typeattributeset tzdatacheck_exec_26_0 (tzdatacheck_exec))
(typeattributeset ueventd_26_0 (ueventd))
(typeattributeset uhid_device_26_0 (uhid_device))
(typeattributeset uimode_service_26_0 (uimode_service))
(typeattributeset uio_device_26_0 (uio_device))
(typeattributeset uncrypt_26_0 (uncrypt))
(typeattributeset uncrypt_exec_26_0 (uncrypt_exec))
(typeattributeset uncrypt_socket_26_0 (uncrypt_socket))
(typeattributeset unencrypted_data_file_26_0 (unencrypted_data_file))
(typeattributeset unlabeled_26_0 (unlabeled))
(typeattributeset untrusted_app_25_26_0 (untrusted_app_25))
(typeattributeset untrusted_app_26_0
  ( untrusted_app
    untrusted_app_27))
(typeattributeset untrusted_v2_app_26_0 (untrusted_v2_app))
(typeattributeset update_engine_26_0 (update_engine))
(typeattributeset update_engine_data_file_26_0 (update_engine_data_file))
(typeattributeset update_engine_exec_26_0 (update_engine_exec))
(typeattributeset update_engine_service_26_0 (update_engine_service))
(typeattributeset updatelock_service_26_0 (updatelock_service))
(typeattributeset update_verifier_26_0 (update_verifier))
(typeattributeset update_verifier_exec_26_0 (update_verifier_exec))
(typeattributeset usagestats_service_26_0 (usagestats_service))
(typeattributeset usbaccessory_device_26_0 (usbaccessory_device))
(typeattributeset usb_device_26_0 (usb_device))
(typeattributeset usbfs_26_0 (usbfs))
(typeattributeset usb_service_26_0 (usb_service))
(typeattributeset userdata_block_device_26_0 (userdata_block_device))
(typeattributeset usermodehelper_26_0 (sysfs_usermodehelper usermodehelper))
(typeattributeset user_profile_data_file_26_0 (user_profile_data_file))
(typeattributeset user_service_26_0 (user_service))
(typeattributeset vcs_device_26_0 (vcs_device))
(typeattributeset vdc_26_0 (vdc))
(typeattributeset vdc_exec_26_0 (vdc_exec))
(typeattributeset vendor_app_file_26_0 (vendor_app_file))
(typeattributeset vendor_configs_file_26_0 (vendor_configs_file))
(typeattributeset vendor_file_26_0 (vendor_file))
(typeattributeset vendor_framework_file_26_0 (vendor_framework_file))
(typeattributeset vendor_hal_file_26_0 (vendor_hal_file))
(typeattributeset vendor_overlay_file_26_0 (vendor_overlay_file))
(typeattributeset vendor_shell_exec_26_0 (vendor_shell_exec))
(typeattributeset vendor_toolbox_exec_26_0 (vendor_toolbox_exec))
(typeattributeset vfat_26_0 (vfat))
(typeattributeset vibrator_service_26_0 (vibrator_service))
(typeattributeset video_device_26_0 (video_device))
(typeattributeset virtual_touchpad_26_0 (virtual_touchpad))
(typeattributeset virtual_touchpad_exec_26_0 (virtual_touchpad_exec))
(typeattributeset virtual_touchpad_service_26_0 (virtual_touchpad_service))
(typeattributeset vndbinder_device_26_0 (vndbinder_device))
(typeattributeset vndk_sp_file_26_0 (vndk_sp_file))
(typeattributeset vndservice_contexts_file_26_0 (vndservice_contexts_file))
(typeattributeset vndservicemanager_26_0 (vndservicemanager))
(typeattributeset voiceinteraction_service_26_0 (voiceinteraction_service))
(typeattributeset vold_26_0 (vold))
(typeattributeset vold_data_file_26_0 (vold_data_file))
(typeattributeset vold_device_26_0 (vold_device))
(typeattributeset vold_exec_26_0 (vold_exec))
(typeattributeset vold_prop_26_0 (vold_prop))
(typeattributeset vold_socket_26_0 (vold_socket))
(typeattributeset vpn_data_file_26_0 (vpn_data_file))
(typeattributeset vr_hwc_26_0 (vr_hwc))
(typeattributeset vr_hwc_exec_26_0 (vr_hwc_exec))
(typeattributeset vr_hwc_service_26_0 (vr_hwc_service))
(typeattributeset vr_manager_service_26_0 (vr_manager_service))
(typeattributeset wallpaper_file_26_0 (wallpaper_file))
(typeattributeset wallpaper_service_26_0 (wallpaper_service))
(typeattributeset watchdogd_26_0 (watchdogd))
(typeattributeset watchdog_device_26_0 (watchdog_device))
(typeattributeset webviewupdate_service_26_0 (webviewupdate_service))
(typeattributeset webview_zygote_26_0 (webview_zygote))
(typeattributeset webview_zygote_exec_26_0 (webview_zygote_exec))
(typeattributeset webview_zygote_socket_26_0 (webview_zygote_socket))
(typeattributeset wifiaware_service_26_0 (wifiaware_service))
(typeattributeset wificond_26_0 (wificond))
(typeattributeset wificond_exec_26_0 (wificond_exec))
(typeattributeset wificond_service_26_0 (wificond_service))
(typeattributeset wifi_data_file_26_0 (wifi_data_file))
(typeattributeset wifi_log_prop_26_0 (wifi_log_prop))
(typeattributeset wifip2p_service_26_0 (wifip2p_service))
(typeattributeset wifi_prop_26_0 (wifi_prop))
(typeattributeset wifiscanner_service_26_0 (wifiscanner_service))
(typeattributeset wifi_service_26_0 (wifi_service))
(typeattributeset window_service_26_0 (window_service))
(typeattributeset wpa_socket_26_0 (wpa_socket))
(typeattributeset zero_device_26_0 (zero_device))
(typeattributeset zoneinfo_data_file_26_0 (zoneinfo_data_file))
(typeattributeset zygote_26_0 (zygote))
(typeattributeset zygote_exec_26_0 (zygote_exec))
(typeattributeset zygote_socket_26_0 (zygote_socket))
