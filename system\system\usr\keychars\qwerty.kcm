# Copyright (C) 2010 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

#
# Emulator keyboard character map #1.
#
# This file is no longer used as the platform's default keyboard character map.
# Refer to Generic.kcm and Virtual.kcm instead.
#

type ALPHA

key A {
    label:                              'A'
    number:                             '2'
    base:                               'a'
    shift, capslock:                    'A'
    alt:                                '#'
    shift+alt, capslock+alt:            none
}

key B {
    label:                              'B'
    number:                             '2'
    base:                               'b'
    shift, capslock:                    'B'
    alt:                                '<'
    shift+alt, capslock+alt:            none
}

key C {
    label:                              'C'
    number:                             '2'
    base:                               'c'
    shift, capslock:                    'C'
    alt:                                '9'
    shift+alt, capslock+alt:            '\u00e7'
}

key D {
    label:                              'D'
    number:                             '3'
    base:                               'd'
    shift, capslock:                    'D'
    alt:                                '5'
    shift+alt, capslock+alt:            none
}

key E {
    label:                              'E'
    number:                             '3'
    base:                               'e'
    shift, capslock:                    'E'
    alt:                                '2'
    shift+alt, capslock+alt:            '\u0301'
}

key F {
    label:                              'F'
    number:                             '3'
    base:                               'f'
    shift, capslock:                    'F'
    alt:                                '6'
    shift+alt, capslock+alt:            '\u00a5'
}

key G {
    label:                              'G'
    number:                             '4'
    base:                               'g'
    shift, capslock:                    'G'
    alt:                                '-'
    shift+alt, capslock+alt:            '_'
}

key H {
    label:                              'H'
    number:                             '4'
    base:                               'h'
    shift, capslock:                    'H'
    alt:                                '['
    shift+alt, capslock+alt:            '{'
}

key I {
    label:                              'I'
    number:                             '4'
    base:                               'i'
    shift, capslock:                    'I'
    alt:                                '$'
    shift+alt, capslock+alt:            '\u0302'
}

key J {
    label:                              'J'
    number:                             '5'
    base:                               'j'
    shift, capslock:                    'J'
    alt:                                ']'
    shift+alt, capslock+alt:            '}'
}

key K {
    label:                              'K'
    number:                             '5'
    base:                               'k'
    shift, capslock:                    'K'
    alt:                                '"'
    shift+alt, capslock+alt:            '~'
}

key L {
    label:                              'L'
    number:                             '5'
    base:                               'l'
    shift, capslock:                    'L'
    alt:                                '\''
    shift+alt, capslock+alt:            '`'
}

key M {
    label:                              'M'
    number:                             '6'
    base:                               'm'
    shift, capslock:                    'M'
    alt:                                '!'
    shift+alt, capslock+alt:            none
}

key N {
    label:                              'N'
    number:                             '6'
    base:                               'n'
    shift, capslock:                    'N'
    alt:                                '>'
    shift+alt, capslock+alt:            '\u0303'
}

key O {
    label:                              'O'
    number:                             '6'
    base:                               'o'
    shift, capslock:                    'O'
    alt:                                '('
    shift+alt, capslock+alt:            none
}

key P {
    label:                              'P'
    number:                             '7'
    base:                               'p'
    shift, capslock:                    'P'
    alt:                                ')'
    shift+alt, capslock+alt:            none
}

key Q {
    label:                              'Q'
    number:                             '7'
    base:                               'q'
    shift, capslock:                    'Q'
    alt:                                '*'
    shift+alt, capslock+alt:            '\u0300'
}

key R {
    label:                              'R'
    number:                             '7'
    base:                               'r'
    shift, capslock:                    'R'
    alt:                                '3'
    shift+alt, capslock+alt:            '\u20ac'
}

key S {
    label:                              'S'
    number:                             '7'
    base:                               's'
    shift, capslock:                    'S'
    alt:                                '4'
    shift+alt, capslock+alt:            '\u00df'
}

key T {
    label:                              'T'
    number:                             '8'
    base:                               't'
    shift, capslock:                    'T'
    alt:                                '+'
    shift+alt, capslock+alt:            '\u00a3'
}

key U {
    label:                              'U'
    number:                             '8'
    base:                               'u'
    shift, capslock:                    'U'
    alt:                                '&'
    shift+alt, capslock+alt:            '\u0308'
}

key V {
    label:                              'V'
    number:                             '8'
    base:                               'v'
    shift, capslock:                    'V'
    alt:                                '='
    shift+alt, capslock+alt:            '^'
}

key W {
    label:                              'W'
    number:                             '9'
    base:                               'w'
    shift, capslock:                    'W'
    alt:                                '1'
    shift+alt, capslock+alt:            none
}

key X {
    label:                              'X'
    number:                             '9'
    base:                               'x'
    shift, capslock:                    'X'
    alt:                                '8'
    shift+alt, capslock+alt:            '\uef00'
}

key Y {
    label:                              'Y'
    number:                             '9'
    base:                               'y'
    shift, capslock:                    'Y'
    alt:                                '%'
    shift+alt, capslock+alt:            '\u00a1'
}

key Z {
    label:                              'Z'
    number:                             '9'
    base:                               'z'
    shift, capslock:                    'Z'
    alt:                                '7'
    shift+alt, capslock+alt:            none
}

key COMMA {
    label:                              ','
    number:                             ','
    base:                               ','
    shift:                              ';'
    alt:                                ';'
    shift+alt:                          '|'
}

key PERIOD {
    label:                              '.'
    number:                             '.'
    base:                               '.'
    shift:                              ':'
    alt:                                ':'
    shift+alt:                          '\u2026'
}

key AT {
    label:                              '@'
    number:                             '0'
    base:                               '@'
    shift:                              '0'
    alt:                                '0'
    shift+alt:                          '\u2022'
}

key SLASH {
    label:                              '/'
    number:                             '/'
    base:                               '/'
    shift:                              '?'
    alt:                                '?'
    shift+alt:                          '\\'
}

key SPACE {
    label:                              ' '
    number:                             ' '
    base:                               ' '
    shift:                              ' '
    alt:                                '\uef01'
    shift+alt:                          '\uef01'
}

key ENTER {
    label:                              '\n'
    number:                             '\n'
    base:                               '\n'
    shift:                              '\n'
    alt:                                '\n'
    shift+alt:                          '\n'
}

key TAB {
    label:                              '\t'
    number:                             '\t'
    base:                               '\t'
    shift:                              '\t'
    alt:                                '\t'
    shift+alt:                          '\t'
}

key 0 {
    label:                              '0'
    number:                             '0'
    base:                               '0'
    shift:                              ')'
    alt:                                ')'
    shift+alt:                          ')'
}

key 1 {
    label:                              '1'
    number:                             '1'
    base:                               '1'
    shift:                              '!'
    alt:                                '!'
    shift+alt:                          '!'
}

key 2 {
    label:                              '2'
    number:                             '2'
    base:                               '2'
    shift:                              '@'
    alt:                                '@'
    shift+alt:                          '@'
}

key 3 {
    label:                              '3'
    number:                             '3'
    base:                               '3'
    shift:                              '#'
    alt:                                '#'
    shift+alt:                          '#'
}

key 4 {
    label:                              '4'
    number:                             '4'
    base:                               '4'
    shift:                              '$'
    alt:                                '$'
    shift+alt:                          '$'
}

key 5 {
    label:                              '5'
    number:                             '5'
    base:                               '5'
    shift:                              '%'
    alt:                                '%'
    shift+alt:                          '%'
}

key 6 {
    label:                              '6'
    number:                             '6'
    base:                               '6'
    shift:                              '^'
    alt:                                '^'
    shift+alt:                          '^'
}

key 7 {
    label:                              '7'
    number:                             '7'
    base:                               '7'
    shift:                              '&'
    alt:                                '&'
    shift+alt:                          '&'
}

key 8 {
    label:                              '8'
    number:                             '8'
    base:                               '8'
    shift:                              '*'
    alt:                                '*'
    shift+alt:                          '*'
}

key 9 {
    label:                              '9'
    number:                             '9'
    base:                               '9'
    shift:                              '('
    alt:                                '('
    shift+alt:                          '('
}

key GRAVE {
    label:                              '`'
    number:                             '`'
    base:                               '`'
    shift:                              '~'
    alt:                                '`'
    shift+alt:                          '~'
}

key MINUS {
    label:                              '-'
    number:                             '-'
    base:                               '-'
    shift:                              '_'
    alt:                                '-'
    shift+alt:                          '_'
}

key EQUALS {
    label:                              '='
    number:                             '='
    base:                               '='
    shift:                              '+'
    alt:                                '='
    shift+alt:                          '+'
}

key LEFT_BRACKET {
    label:                              '['
    number:                             '['
    base:                               '['
    shift:                              '{'
    alt:                                '['
    shift+alt:                          '{'
}

key RIGHT_BRACKET {
    label:                              ']'
    number:                             ']'
    base:                               ']'
    shift:                              '}'
    alt:                                ']'
    shift+alt:                          '}'
}

key BACKSLASH {
    label:                              '\\'
    number:                             '\\'
    base:                               '\\'
    shift:                              '|'
    alt:                                '\\'
    shift+alt:                          '|'
}

key SEMICOLON {
    label:                              ';'
    number:                             ';'
    base:                               ';'
    shift:                              ':'
    alt:                                ';'
    shift+alt:                          ':'
}

key APOSTROPHE {
    label:                              '\''
    number:                             '\''
    base:                               '\''
    shift:                              '"'
    alt:                                '\''
    shift+alt:                          '"'
}

key STAR {
    label:                              '*'
    number:                             '*'
    base:                               '*'
    shift:                              '*'
    alt:                                '*'
    shift+alt:                          '*'
}

key POUND {
    label:                              '#'
    number:                             '#'
    base:                               '#'
    shift:                              '#'
    alt:                                '#'
    shift+alt:                          '#'
}

key PLUS {
    label:                              '+'
    number:                             '+'
    base:                               '+'
    shift:                              '+'
    alt:                                '+'
    shift+alt:                          '+'
}
