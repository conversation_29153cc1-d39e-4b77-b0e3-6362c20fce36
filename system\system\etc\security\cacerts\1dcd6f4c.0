-----<PERSON><PERSON><PERSON> CERTIFICATE-----
MIIFcjCCA1qgAwIBAgIQH51ZWtcvwgZEpYAIaeNe9jANBgkqhkiG9w0BAQUFADA/
MQswCQYDVQQGEwJUVzEwMC4GA1UECgwnR292ZXJubWVudCBSb290IENlcnRpZmlj
YXRpb24gQXV0aG9yaXR5MB4XDTAyMTIwNTEzMjMzM1oXDTMyMTIwNTEzMjMzM1ow
PzELMAkGA1UEBhMCVFcxMDAuBgNVBAoMJ0dvdmVybm1lbnQgUm9vdCBDZXJ0aWZp
Y2F0aW9uIEF1dGhvcml0eTCCAiIwDQYJKoZIhvcNAQEBBQADggIPADCCAgoCggIB
AJoluOzMonWoe/fOW1mKydGGEghU7Jzy50b2iPN86aXfTEc2pBsBHH8eV4qNw8XR
IePaJD9IK/ufLqGU5ywck9G/GwGHU5nOp/UKIXZ3/6m3xnOUT0b3EEk3+qhZSV1q
gQdW8or5BtD3cCJNtLdBuTK4sfCxw5w/cP1T3YGq2GN49thTbqGsaoQkclSGxtKy
yhwOeYHWtXBiCAEuTk8O1RGvqa/lmr/czIdtJuTJV6L7lvnM4T9TjGxMfptTCAts
F/tnyMKtsc2AtJfcdgEWFelq16TheEfOhtX7MfP6Mb40qij7cEwdScevLJ1tZqa2
jWR+tSBqnTuBto9AAGdLiYa4zGX+FVPpBMHWXx1E1wovJ5pGfaENda1UhhXcSTvx
ls4Pm6Dso3pdvtUqdULle96ltqqvKKyskKw4t9VoNSZ63Pc78/1Fm9G7Q3hub/FC
VGqY8A2tl+lSXunVanLeavcbYBT0peS2cWeqH+riTcFCQP5nRhc4L0c/cZyu5SHK
YS1tB6iEfC3uUSXxY5Ce/eFXiGvviiNtsea9P63RPZYLhY3Naye7twWb7LuRqQoH
EgKXTiCQ8P8NHuJBO9NAOueNXdpm5AKwB1KYXA6OM5zCppX7VRluTI6uSw+9wThN
Xo+EHWbNxWCWtFJaBYmOlXqYwZE8lSOyDvR5tMl8wUohAgMBAAGjajBoMB0GA1Ud
DgQWBBTMzO/MKWCkO7GStjz6MmKPrCUVOzAMBgNVHRMEBTADAQH/MDkGBGcqBwAE
MTAvMC0CAQAwCQYFKw4DAhoFADAHBgVnKgMAAAQUA5vwIhP/lSg209yewDL7MTqK
UWUwDQYJKoZIhvcNAQEFBQADggIBAECASvomyc5eMN1PhnR2WPWus4MzeKR6dBcZ
TulStbngCnRiqmjKeKBMmo4sIy7VahIkv9Ro04rQ2JyftB8M3jh+Vzj8jeJPXgyf
qzvS/3WXy6TjZwj/5cAWtUgBfen5Cv8b5Wppv3ghqMKnI6mGq3ZW6A4M9hPdKmaK
ZEk9GhiHkASfQlK3T8v+R0F2Ne//AHY2RTKbxkaFXeIksB7jSJaYV0eUVXoPQbFE
JPPB/hprv4j9wabak2BegUqZIJxIZhm1AHlUD7gsL0u8qV1bYH+Mh6XgUmMqvtg7
hUAV/h62ZT/FS9p+tXo1KaMuephgIqP0fSdOLeq0dDzpD6QzDxARvBMB1uUO07+1
EqLhRSPAzAhuYbeJq4PjJB7mXQfnHyA+z2fI56wwbSdLaG5LKlwCCDTb+HbkZ6Mm
nD+iMsJKxYEYMRBWqoTvLQr/uB930r+lWKBi5NdLkXWNiYCYfm3LU05er/ayl4WX
udpVBrkk7tfGOB5jGxI7leFYrPLfhNVfmS8NVVvmONsuP3LpSIXLuykTjx44Vbnz
ssQwmSNOXfJIoRIM3BKQCZBUkQM8R+XVyWXgt0t97EfTsws+rZ7QdAAO671RrcDe
LMDDav7v3Aun+kbfYNucpllQdSNpc5Oy+fwC00fmcc4QAu4njIT/rEUNE1yDMuAl
pYYsfPQS
-----END CERTIFICATE-----
Certificate:
    Data:
        Version: 3 (0x2)
        Serial Number:
            1f:9d:59:5a:d7:2f:c2:06:44:a5:80:08:69:e3:5e:f6
    Signature Algorithm: sha1WithRSAEncryption
        Issuer: C=TW, O=Government Root Certification Authority
        Validity
            Not Before: Dec  5 13:23:33 2002 GMT
            Not After : Dec  5 13:23:33 2032 GMT
        Subject: C=TW, O=Government Root Certification Authority
        Subject Public Key Info:
            Public Key Algorithm: rsaEncryption
                Public-Key: (4096 bit)
                Modulus:
                    00:9a:25:b8:ec:cc:a2:75:a8:7b:f7:ce:5b:59:8a:
                    c9:d1:86:12:08:54:ec:9c:f2:e7:46:f6:88:f3:7c:
                    e9:a5:df:4c:47:36:a4:1b:01:1c:7f:1e:57:8a:8d:
                    c3:c5:d1:21:e3:da:24:3f:48:2b:fb:9f:2e:a1:94:
                    e7:2c:1c:93:d1:bf:1b:01:87:53:99:ce:a7:f5:0a:
                    21:76:77:ff:a9:b7:c6:73:94:4f:46:f7:10:49:37:
                    fa:a8:59:49:5d:6a:81:07:56:f2:8a:f9:06:d0:f7:
                    70:22:4d:b4:b7:41:b9:32:b8:b1:f0:b1:c3:9c:3f:
                    70:fd:53:dd:81:aa:d8:63:78:f6:d8:53:6e:a1:ac:
                    6a:84:24:72:54:86:c6:d2:b2:ca:1c:0e:79:81:d6:
                    b5:70:62:08:01:2e:4e:4f:0e:d5:11:af:a9:af:e5:
                    9a:bf:dc:cc:87:6d:26:e4:c9:57:a2:fb:96:f9:cc:
                    e1:3f:53:8c:6c:4c:7e:9b:53:08:0b:6c:17:fb:67:
                    c8:c2:ad:b1:cd:80:b4:97:dc:76:01:16:15:e9:6a:
                    d7:a4:e1:78:47:ce:86:d5:fb:31:f3:fa:31:be:34:
                    aa:28:fb:70:4c:1d:49:c7:af:2c:9d:6d:66:a6:b6:
                    8d:64:7e:b5:20:6a:9d:3b:81:b6:8f:40:00:67:4b:
                    89:86:b8:cc:65:fe:15:53:e9:04:c1:d6:5f:1d:44:
                    d7:0a:2f:27:9a:46:7d:a1:0d:75:ad:54:86:15:dc:
                    49:3b:f1:96:ce:0f:9b:a0:ec:a3:7a:5d:be:d5:2a:
                    75:42:e5:7b:de:a5:b6:aa:af:28:ac:ac:90:ac:38:
                    b7:d5:68:35:26:7a:dc:f7:3b:f3:fd:45:9b:d1:bb:
                    43:78:6e:6f:f1:42:54:6a:98:f0:0d:ad:97:e9:52:
                    5e:e9:d5:6a:72:de:6a:f7:1b:60:14:f4:a5:e4:b6:
                    71:67:aa:1f:ea:e2:4d:c1:42:40:fe:67:46:17:38:
                    2f:47:3f:71:9c:ae:e5:21:ca:61:2d:6d:07:a8:84:
                    7c:2d:ee:51:25:f1:63:90:9e:fd:e1:57:88:6b:ef:
                    8a:23:6d:b1:e6:bd:3f:ad:d1:3d:96:0b:85:8d:cd:
                    6b:27:bb:b7:05:9b:ec:bb:91:a9:0a:07:12:02:97:
                    4e:20:90:f0:ff:0d:1e:e2:41:3b:d3:40:3a:e7:8d:
                    5d:da:66:e4:02:b0:07:52:98:5c:0e:8e:33:9c:c2:
                    a6:95:fb:55:19:6e:4c:8e:ae:4b:0f:bd:c1:38:4d:
                    5e:8f:84:1d:66:cd:c5:60:96:b4:52:5a:05:89:8e:
                    95:7a:98:c1:91:3c:95:23:b2:0e:f4:79:b4:c9:7c:
                    c1:4a:21
                Exponent: 65537 (0x10001)
        X509v3 extensions:
            X509v3 Subject Key Identifier: 
                CC:CC:EF:CC:29:60:A4:3B:B1:92:B6:3C:FA:32:62:8F:AC:25:15:3B
            X509v3 Basic Constraints: 
                CA:TRUE
            setCext-hashedRoot: 
                0/0-...0...+......0...g*........"...(6....2.1:.Qe
    Signature Algorithm: sha1WithRSAEncryption
         40:80:4a:fa:26:c9:ce:5e:30:dd:4f:86:74:76:58:f5:ae:b3:
         83:33:78:a4:7a:74:17:19:4e:e9:52:b5:b9:e0:0a:74:62:aa:
         68:ca:78:a0:4c:9a:8e:2c:23:2e:d5:6a:12:24:bf:d4:68:d3:
         8a:d0:d8:9c:9f:b4:1f:0c:de:38:7e:57:38:fc:8d:e2:4f:5e:
         0c:9f:ab:3b:d2:ff:75:97:cb:a4:e3:67:08:ff:e5:c0:16:b5:
         48:01:7d:e9:f9:0a:ff:1b:e5:6a:69:bf:78:21:a8:c2:a7:23:
         a9:86:ab:76:56:e8:0e:0c:f6:13:dd:2a:66:8a:64:49:3d:1a:
         18:87:90:04:9f:42:52:b7:4f:cb:fe:47:41:76:35:ef:ff:00:
         76:36:45:32:9b:c6:46:85:5d:e2:24:b0:1e:e3:48:96:98:57:
         47:94:55:7a:0f:41:b1:44:24:f3:c1:fe:1a:6b:bf:88:fd:c1:
         a6:da:93:60:5e:81:4a:99:20:9c:48:66:19:b5:00:79:54:0f:
         b8:2c:2f:4b:bc:a9:5d:5b:60:7f:8c:87:a5:e0:52:63:2a:be:
         d8:3b:85:40:15:fe:1e:b6:65:3f:c5:4b:da:7e:b5:7a:35:29:
         a3:2e:7a:98:60:22:a3:f4:7d:27:4e:2d:ea:b4:74:3c:e9:0f:
         a4:33:0f:10:11:bc:13:01:d6:e5:0e:d3:bf:b5:12:a2:e1:45:
         23:c0:cc:08:6e:61:b7:89:ab:83:e3:24:1e:e6:5d:07:e7:1f:
         20:3e:cf:67:c8:e7:ac:30:6d:27:4b:68:6e:4b:2a:5c:02:08:
         34:db:f8:76:e4:67:a3:26:9c:3f:a2:32:c2:4a:c5:81:18:31:
         10:56:aa:84:ef:2d:0a:ff:b8:1f:77:d2:bf:a5:58:a0:62:e4:
         d7:4b:91:75:8d:89:80:98:7e:6d:cb:53:4e:5e:af:f6:b2:97:
         85:97:b9:da:55:06:b9:24:ee:d7:c6:38:1e:63:1b:12:3b:95:
         e1:58:ac:f2:df:84:d5:5f:99:2f:0d:55:5b:e6:38:db:2e:3f:
         72:e9:48:85:cb:bb:29:13:8f:1e:38:55:b9:f3:b2:c4:30:99:
         23:4e:5d:f2:48:a1:12:0c:dc:12:90:09:90:54:91:03:3c:47:
         e5:d5:c9:65:e0:b7:4b:7d:ec:47:d3:b3:0b:3e:ad:9e:d0:74:
         00:0e:eb:bd:51:ad:c0:de:2c:c0:c3:6a:fe:ef:dc:0b:a7:fa:
         46:df:60:db:9c:a6:59:50:75:23:69:73:93:b2:f9:fc:02:d3:
         47:e6:71:ce:10:02:ee:27:8c:84:ff:ac:45:0d:13:5c:83:32:
         e0:25:a5:86:2c:7c:f4:12
SHA1 Fingerprint=F4:8B:11:BF:DE:AB:BE:94:54:20:71:E6:41:DE:6B:BE:88:2B:40:B9
