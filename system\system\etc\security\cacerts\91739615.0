-----<PERSON><PERSON><PERSON> CERTIFICATE-----
MIIEAzCCAuugAwIBAgIQVID5oHPtPwBMyonY43HmSjANBgkqhkiG9w0BAQUFADB1
MQswCQYDVQQGEwJFRTEiMCAGA1UECgwZQVMgU2VydGlmaXRzZWVyaW1pc2tlc2t1
czEoMCYGA1UEAwwfRUUgQ2VydGlmaWNhdGlvbiBDZW50cmUgUm9vdCBDQTEYMBYG
CSqGSIb3DQEJARYJcGtpQHNrLmVlMCIYDzIwMTAxMDMwMTAxMDMwWhgPMjAzMDEy
MTcyMzU5NTlaMHUxCzAJBgNVBAYTAkVFMSIwIAYDVQQKDBlBUyBTZXJ0aWZpdHNl
ZXJpbWlza2Vza3VzMSgwJgYDVQQDDB9FRSBDZXJ0aWZpY2F0aW9uIENlbnRyZSBS
b290IENBMRgwFgYJKoZIhvcNAQkBFglwa2lAc2suZWUwggEiMA0GCSqGSIb3DQEB
AQUAA4IBDwAwggEKAoIBAQDIIMDs4MVLqwd4lfNE7vsLDP90jmG7sWLqI9iroWUy
euuOF0+W2Ap7kaJjbMeMTC55v6kF/GlclY1i+blw7cNRfdCT5mzrMEvhvH2/UpvO
bntl8jixwKIy72KyaOBhU8E2lf/slLo2rpwcpzIP5Xy0xm90/XsY6KxX7QYgSzIw
WFv9zajmofxwvI6Sc9uXp3whrj3B9UiHbCe9nyV0gVWw93X2PaRka9ZP585ArQ/d
MtO8ihJTmMmJ+xAdTX7Nfh9WDSFwhfYggx/2uh8Ej+p3iDXE/+pOoYtNP2MbRMNE
1CV2yreN1x5KZmTNXMWcg+HCCIia7E6j8T4cLNlsHaFLAgMBAAGjgYowgYcwDwYD
VR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYwHQYDVR0OBBYEFBLyWj7qVhy/
zQas8fElyalL1BSZMEUGA1UdJQQ+MDwGCCsGAQUFBwMCBggrBgEFBQcDAQYIKwYB
BQUHAwMGCCsGAQUFBwMEBggrBgEFBQcDCAYIKwYBBQUHAwkwDQYJKoZIhvcNAQEF
BQADggEBAHv25MANqhlHt01Xo/6tu7Fq1Q+e2+RjxY6hUFaTlrg4wCQiZrxTFGGV
v9DHKpY5P30osxBAIWrEr7BSdxjhlthWXePdNl4dp1BUoMUq5KqMlIpPnTX/dqQG
E5Gion0ARD9V04I8GtVbvFZMIi5GQ4okQC3zErg7cBqklrkar4dBGmoYDQZPxz5u
uSlNDUmJEYcyW+ZLBMjkXOZ0c5RdFpgTlf7727FE5TpwrDdr5rMzcijJs1eg9gIW
iAYLtqZLICjU3j2LrTcFU3T+bsy8QxdxXvnFzBqpYe73dgzzcvRyrc9yAjYHR8/v
GVCJYMzpJJUPwssd8m92kMfMdcGWxZ0=
-----END CERTIFICATE-----
Certificate:
    Data:
        Version: 3 (0x2)
        Serial Number:
            54:80:f9:a0:73:ed:3f:00:4c:ca:89:d8:e3:71:e6:4a
    Signature Algorithm: sha1WithRSAEncryption
        Issuer: C=EE, O=AS Sertifitseerimiskeskus, CN=EE Certification Centre Root CA/emailAddress=<EMAIL>
        Validity
            Not Before: Oct 30 10:10:30 2010 GMT
            Not After : Dec 17 23:59:59 2030 GMT
        Subject: C=EE, O=AS Sertifitseerimiskeskus, CN=EE Certification Centre Root CA/emailAddress=<EMAIL>
        Subject Public Key Info:
            Public Key Algorithm: rsaEncryption
                Public-Key: (2048 bit)
                Modulus:
                    00:c8:20:c0:ec:e0:c5:4b:ab:07:78:95:f3:44:ee:
                    fb:0b:0c:ff:74:8e:61:bb:b1:62:ea:23:d8:ab:a1:
                    65:32:7a:eb:8e:17:4f:96:d8:0a:7b:91:a2:63:6c:
                    c7:8c:4c:2e:79:bf:a9:05:fc:69:5c:95:8d:62:f9:
                    b9:70:ed:c3:51:7d:d0:93:e6:6c:eb:30:4b:e1:bc:
                    7d:bf:52:9b:ce:6e:7b:65:f2:38:b1:c0:a2:32:ef:
                    62:b2:68:e0:61:53:c1:36:95:ff:ec:94:ba:36:ae:
                    9c:1c:a7:32:0f:e5:7c:b4:c6:6f:74:fd:7b:18:e8:
                    ac:57:ed:06:20:4b:32:30:58:5b:fd:cd:a8:e6:a1:
                    fc:70:bc:8e:92:73:db:97:a7:7c:21:ae:3d:c1:f5:
                    48:87:6c:27:bd:9f:25:74:81:55:b0:f7:75:f6:3d:
                    a4:64:6b:d6:4f:e7:ce:40:ad:0f:dd:32:d3:bc:8a:
                    12:53:98:c9:89:fb:10:1d:4d:7e:cd:7e:1f:56:0d:
                    21:70:85:f6:20:83:1f:f6:ba:1f:04:8f:ea:77:88:
                    35:c4:ff:ea:4e:a1:8b:4d:3f:63:1b:44:c3:44:d4:
                    25:76:ca:b7:8d:d7:1e:4a:66:64:cd:5c:c5:9c:83:
                    e1:c2:08:88:9a:ec:4e:a3:f1:3e:1c:2c:d9:6c:1d:
                    a1:4b
                Exponent: 65537 (0x10001)
        X509v3 extensions:
            X509v3 Basic Constraints: critical
                CA:TRUE
            X509v3 Key Usage: critical
                Certificate Sign, CRL Sign
            X509v3 Subject Key Identifier: 
                12:F2:5A:3E:EA:56:1C:BF:CD:06:AC:F1:F1:25:C9:A9:4B:D4:14:99
            X509v3 Extended Key Usage: 
                TLS Web Client Authentication, TLS Web Server Authentication, Code Signing, E-mail Protection, Time Stamping, OCSP Signing
    Signature Algorithm: sha1WithRSAEncryption
         7b:f6:e4:c0:0d:aa:19:47:b7:4d:57:a3:fe:ad:bb:b1:6a:d5:
         0f:9e:db:e4:63:c5:8e:a1:50:56:93:96:b8:38:c0:24:22:66:
         bc:53:14:61:95:bf:d0:c7:2a:96:39:3f:7d:28:b3:10:40:21:
         6a:c4:af:b0:52:77:18:e1:96:d8:56:5d:e3:dd:36:5e:1d:a7:
         50:54:a0:c5:2a:e4:aa:8c:94:8a:4f:9d:35:ff:76:a4:06:13:
         91:a2:a2:7d:00:44:3f:55:d3:82:3c:1a:d5:5b:bc:56:4c:22:
         2e:46:43:8a:24:40:2d:f3:12:b8:3b:70:1a:a4:96:b9:1a:af:
         87:41:1a:6a:18:0d:06:4f:c7:3e:6e:b9:29:4d:0d:49:89:11:
         87:32:5b:e6:4b:04:c8:e4:5c:e6:74:73:94:5d:16:98:13:95:
         fe:fb:db:b1:44:e5:3a:70:ac:37:6b:e6:b3:33:72:28:c9:b3:
         57:a0:f6:02:16:88:06:0b:b6:a6:4b:20:28:d4:de:3d:8b:ad:
         37:05:53:74:fe:6e:cc:bc:43:17:71:5e:f9:c5:cc:1a:a9:61:
         ee:f7:76:0c:f3:72:f4:72:ad:cf:72:02:36:07:47:cf:ef:19:
         50:89:60:cc:e9:24:95:0f:c2:cb:1d:f2:6f:76:90:c7:cc:75:
         c1:96:c5:9d
SHA1 Fingerprint=C9:A8:B9:E7:55:80:5E:58:E3:53:77:A7:25:EB:AF:C3:7B:27:CC:D7
