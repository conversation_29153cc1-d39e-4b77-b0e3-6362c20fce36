;; types removed from current policy
(type webview_zygote_socket)
(type reboot_data_file)
(type vold_socket)
(type rild)

(expandtypeattribute (accessibility_service_27_0) true)
(expandtypeattribute (account_service_27_0) true)
(expandtypeattribute (activity_service_27_0) true)
(expandtypeattribute (adbd_27_0) true)
(expandtypeattribute (adb_data_file_27_0) true)
(expandtypeattribute (adbd_exec_27_0) true)
(expandtypeattribute (adbd_socket_27_0) true)
(expandtypeattribute (adb_keys_file_27_0) true)
(expandtypeattribute (alarm_device_27_0) true)
(expandtypeattribute (alarm_service_27_0) true)
(expandtypeattribute (anr_data_file_27_0) true)
(expandtypeattribute (apk_data_file_27_0) true)
(expandtypeattribute (apk_private_data_file_27_0) true)
(expandtypeattribute (apk_private_tmp_file_27_0) true)
(expandtypeattribute (apk_tmp_file_27_0) true)
(expandtypeattribute (app_data_file_27_0) true)
(expandtypeattribute (app_fuse_file_27_0) true)
(expandtypeattribute (app_fusefs_27_0) true)
(expandtypeattribute (appops_service_27_0) true)
(expandtypeattribute (appwidget_service_27_0) true)
(expandtypeattribute (asec_apk_file_27_0) true)
(expandtypeattribute (asec_image_file_27_0) true)
(expandtypeattribute (asec_public_file_27_0) true)
(expandtypeattribute (ashmem_device_27_0) true)
(expandtypeattribute (assetatlas_service_27_0) true)
(expandtypeattribute (audio_data_file_27_0) true)
(expandtypeattribute (audio_device_27_0) true)
(expandtypeattribute (audiohal_data_file_27_0) true)
(expandtypeattribute (audio_prop_27_0) true)
(expandtypeattribute (audio_seq_device_27_0) true)
(expandtypeattribute (audioserver_27_0) true)
(expandtypeattribute (audioserver_data_file_27_0) true)
(expandtypeattribute (audioserver_service_27_0) true)
(expandtypeattribute (audio_service_27_0) true)
(expandtypeattribute (audio_timer_device_27_0) true)
(expandtypeattribute (autofill_service_27_0) true)
(expandtypeattribute (backup_data_file_27_0) true)
(expandtypeattribute (backup_service_27_0) true)
(expandtypeattribute (batteryproperties_service_27_0) true)
(expandtypeattribute (battery_service_27_0) true)
(expandtypeattribute (batterystats_service_27_0) true)
(expandtypeattribute (binder_device_27_0) true)
(expandtypeattribute (binfmt_miscfs_27_0) true)
(expandtypeattribute (blkid_27_0) true)
(expandtypeattribute (blkid_untrusted_27_0) true)
(expandtypeattribute (block_device_27_0) true)
(expandtypeattribute (bluetooth_27_0) true)
(expandtypeattribute (bluetooth_data_file_27_0) true)
(expandtypeattribute (bluetooth_efs_file_27_0) true)
(expandtypeattribute (bluetooth_logs_data_file_27_0) true)
(expandtypeattribute (bluetooth_manager_service_27_0) true)
(expandtypeattribute (bluetooth_prop_27_0) true)
(expandtypeattribute (bluetooth_service_27_0) true)
(expandtypeattribute (bluetooth_socket_27_0) true)
(expandtypeattribute (bootanim_27_0) true)
(expandtypeattribute (bootanim_exec_27_0) true)
(expandtypeattribute (boot_block_device_27_0) true)
(expandtypeattribute (bootchart_data_file_27_0) true)
(expandtypeattribute (bootstat_27_0) true)
(expandtypeattribute (bootstat_data_file_27_0) true)
(expandtypeattribute (bootstat_exec_27_0) true)
(expandtypeattribute (boottime_prop_27_0) true)
(expandtypeattribute (boottrace_data_file_27_0) true)
(expandtypeattribute (broadcastradio_service_27_0) true)
(expandtypeattribute (bufferhubd_27_0) true)
(expandtypeattribute (bufferhubd_exec_27_0) true)
(expandtypeattribute (cache_backup_file_27_0) true)
(expandtypeattribute (cache_block_device_27_0) true)
(expandtypeattribute (cache_file_27_0) true)
(expandtypeattribute (cache_private_backup_file_27_0) true)
(expandtypeattribute (cache_recovery_file_27_0) true)
(expandtypeattribute (camera_data_file_27_0) true)
(expandtypeattribute (camera_device_27_0) true)
(expandtypeattribute (cameraproxy_service_27_0) true)
(expandtypeattribute (cameraserver_27_0) true)
(expandtypeattribute (cameraserver_exec_27_0) true)
(expandtypeattribute (cameraserver_service_27_0) true)
(expandtypeattribute (cgroup_27_0) true)
(expandtypeattribute (charger_27_0) true)
(expandtypeattribute (clatd_27_0) true)
(expandtypeattribute (clatd_exec_27_0) true)
(expandtypeattribute (clipboard_service_27_0) true)
(expandtypeattribute (commontime_management_service_27_0) true)
(expandtypeattribute (companion_device_service_27_0) true)
(expandtypeattribute (configfs_27_0) true)
(expandtypeattribute (config_prop_27_0) true)
(expandtypeattribute (connectivity_service_27_0) true)
(expandtypeattribute (connmetrics_service_27_0) true)
(expandtypeattribute (console_device_27_0) true)
(expandtypeattribute (consumer_ir_service_27_0) true)
(expandtypeattribute (content_service_27_0) true)
(expandtypeattribute (contexthub_service_27_0) true)
(expandtypeattribute (coredump_file_27_0) true)
(expandtypeattribute (country_detector_service_27_0) true)
(expandtypeattribute (coverage_service_27_0) true)
(expandtypeattribute (cppreopt_prop_27_0) true)
(expandtypeattribute (cppreopts_27_0) true)
(expandtypeattribute (cppreopts_exec_27_0) true)
(expandtypeattribute (cpuctl_device_27_0) true)
(expandtypeattribute (cpuinfo_service_27_0) true)
(expandtypeattribute (crash_dump_27_0) true)
(expandtypeattribute (crash_dump_exec_27_0) true)
(expandtypeattribute (ctl_bootanim_prop_27_0) true)
(expandtypeattribute (ctl_bugreport_prop_27_0) true)
(expandtypeattribute (ctl_console_prop_27_0) true)
(expandtypeattribute (ctl_default_prop_27_0) true)
(expandtypeattribute (ctl_dumpstate_prop_27_0) true)
(expandtypeattribute (ctl_fuse_prop_27_0) true)
(expandtypeattribute (ctl_mdnsd_prop_27_0) true)
(expandtypeattribute (ctl_rildaemon_prop_27_0) true)
(expandtypeattribute (dalvikcache_data_file_27_0) true)
(expandtypeattribute (dalvik_prop_27_0) true)
(expandtypeattribute (dbinfo_service_27_0) true)
(expandtypeattribute (debugfs_27_0) true)
(expandtypeattribute (debugfs_mmc_27_0) true)
(expandtypeattribute (debugfs_trace_marker_27_0) true)
(expandtypeattribute (debugfs_tracing_27_0) true)
(expandtypeattribute (debugfs_tracing_debug_27_0) true)
(expandtypeattribute (debugfs_tracing_instances_27_0) true)
(expandtypeattribute (debugfs_wifi_tracing_27_0) true)
(expandtypeattribute (debuggerd_prop_27_0) true)
(expandtypeattribute (debug_prop_27_0) true)
(expandtypeattribute (default_android_hwservice_27_0) true)
(expandtypeattribute (default_android_service_27_0) true)
(expandtypeattribute (default_android_vndservice_27_0) true)
(expandtypeattribute (default_prop_27_0) true)
(expandtypeattribute (device_27_0) true)
(expandtypeattribute (device_identifiers_service_27_0) true)
(expandtypeattribute (deviceidle_service_27_0) true)
(expandtypeattribute (device_logging_prop_27_0) true)
(expandtypeattribute (device_policy_service_27_0) true)
(expandtypeattribute (devicestoragemonitor_service_27_0) true)
(expandtypeattribute (devpts_27_0) true)
(expandtypeattribute (dex2oat_27_0) true)
(expandtypeattribute (dex2oat_exec_27_0) true)
(expandtypeattribute (dhcp_27_0) true)
(expandtypeattribute (dhcp_data_file_27_0) true)
(expandtypeattribute (dhcp_exec_27_0) true)
(expandtypeattribute (dhcp_prop_27_0) true)
(expandtypeattribute (diskstats_service_27_0) true)
(expandtypeattribute (display_service_27_0) true)
(expandtypeattribute (dm_device_27_0) true)
(expandtypeattribute (dnsmasq_27_0) true)
(expandtypeattribute (dnsmasq_exec_27_0) true)
(expandtypeattribute (dnsproxyd_socket_27_0) true)
(expandtypeattribute (DockObserver_service_27_0) true)
(expandtypeattribute (dreams_service_27_0) true)
(expandtypeattribute (drm_data_file_27_0) true)
(expandtypeattribute (drmserver_27_0) true)
(expandtypeattribute (drmserver_exec_27_0) true)
(expandtypeattribute (drmserver_service_27_0) true)
(expandtypeattribute (drmserver_socket_27_0) true)
(expandtypeattribute (dropbox_service_27_0) true)
(expandtypeattribute (dumpstate_27_0) true)
(expandtypeattribute (dumpstate_exec_27_0) true)
(expandtypeattribute (dumpstate_options_prop_27_0) true)
(expandtypeattribute (dumpstate_prop_27_0) true)
(expandtypeattribute (dumpstate_service_27_0) true)
(expandtypeattribute (dumpstate_socket_27_0) true)
(expandtypeattribute (e2fs_27_0) true)
(expandtypeattribute (e2fs_exec_27_0) true)
(expandtypeattribute (efs_file_27_0) true)
(expandtypeattribute (ephemeral_app_27_0) true)
(expandtypeattribute (ethernet_service_27_0) true)
(expandtypeattribute (ffs_prop_27_0) true)
(expandtypeattribute (file_contexts_file_27_0) true)
(expandtypeattribute (fingerprintd_27_0) true)
(expandtypeattribute (fingerprintd_data_file_27_0) true)
(expandtypeattribute (fingerprintd_exec_27_0) true)
(expandtypeattribute (fingerprintd_service_27_0) true)
(expandtypeattribute (fingerprint_prop_27_0) true)
(expandtypeattribute (fingerprint_service_27_0) true)
(expandtypeattribute (firstboot_prop_27_0) true)
(expandtypeattribute (font_service_27_0) true)
(expandtypeattribute (frp_block_device_27_0) true)
(expandtypeattribute (fsck_27_0) true)
(expandtypeattribute (fsck_exec_27_0) true)
(expandtypeattribute (fscklogs_27_0) true)
(expandtypeattribute (fsck_untrusted_27_0) true)
(expandtypeattribute (full_device_27_0) true)
(expandtypeattribute (functionfs_27_0) true)
(expandtypeattribute (fuse_27_0) true)
(expandtypeattribute (fuse_device_27_0) true)
(expandtypeattribute (fwk_display_hwservice_27_0) true)
(expandtypeattribute (fwk_scheduler_hwservice_27_0) true)
(expandtypeattribute (fwk_sensor_hwservice_27_0) true)
(expandtypeattribute (fwmarkd_socket_27_0) true)
(expandtypeattribute (gatekeeperd_27_0) true)
(expandtypeattribute (gatekeeper_data_file_27_0) true)
(expandtypeattribute (gatekeeperd_exec_27_0) true)
(expandtypeattribute (gatekeeper_service_27_0) true)
(expandtypeattribute (gfxinfo_service_27_0) true)
(expandtypeattribute (gps_control_27_0) true)
(expandtypeattribute (gpu_device_27_0) true)
(expandtypeattribute (gpu_service_27_0) true)
(expandtypeattribute (graphics_device_27_0) true)
(expandtypeattribute (graphicsstats_service_27_0) true)
(expandtypeattribute (hal_audio_hwservice_27_0) true)
(expandtypeattribute (hal_bluetooth_hwservice_27_0) true)
(expandtypeattribute (hal_bootctl_hwservice_27_0) true)
(expandtypeattribute (hal_broadcastradio_hwservice_27_0) true)
(expandtypeattribute (hal_camera_hwservice_27_0) true)
(expandtypeattribute (hal_cas_hwservice_27_0) true)
(expandtypeattribute (hal_configstore_ISurfaceFlingerConfigs_27_0) true)
(expandtypeattribute (hal_contexthub_hwservice_27_0) true)
(expandtypeattribute (hal_drm_hwservice_27_0) true)
(expandtypeattribute (hal_dumpstate_hwservice_27_0) true)
(expandtypeattribute (hal_fingerprint_hwservice_27_0) true)
(expandtypeattribute (hal_fingerprint_service_27_0) true)
(expandtypeattribute (hal_gatekeeper_hwservice_27_0) true)
(expandtypeattribute (hal_gnss_hwservice_27_0) true)
(expandtypeattribute (hal_graphics_allocator_hwservice_27_0) true)
(expandtypeattribute (hal_graphics_composer_hwservice_27_0) true)
(expandtypeattribute (hal_graphics_mapper_hwservice_27_0) true)
(expandtypeattribute (hal_health_hwservice_27_0) true)
(expandtypeattribute (hal_ir_hwservice_27_0) true)
(expandtypeattribute (hal_keymaster_hwservice_27_0) true)
(expandtypeattribute (hal_light_hwservice_27_0) true)
(expandtypeattribute (hal_memtrack_hwservice_27_0) true)
(expandtypeattribute (hal_neuralnetworks_hwservice_27_0) true)
(expandtypeattribute (hal_nfc_hwservice_27_0) true)
(expandtypeattribute (hal_oemlock_hwservice_27_0) true)
(expandtypeattribute (hal_omx_hwservice_27_0) true)
(expandtypeattribute (hal_power_hwservice_27_0) true)
(expandtypeattribute (hal_renderscript_hwservice_27_0) true)
(expandtypeattribute (hal_sensors_hwservice_27_0) true)
(expandtypeattribute (hal_telephony_hwservice_27_0) true)
(expandtypeattribute (hal_tetheroffload_hwservice_27_0) true)
(expandtypeattribute (hal_thermal_hwservice_27_0) true)
(expandtypeattribute (hal_tv_cec_hwservice_27_0) true)
(expandtypeattribute (hal_tv_input_hwservice_27_0) true)
(expandtypeattribute (hal_usb_hwservice_27_0) true)
(expandtypeattribute (hal_vibrator_hwservice_27_0) true)
(expandtypeattribute (hal_vr_hwservice_27_0) true)
(expandtypeattribute (hal_weaver_hwservice_27_0) true)
(expandtypeattribute (hal_wifi_hwservice_27_0) true)
(expandtypeattribute (hal_wifi_offload_hwservice_27_0) true)
(expandtypeattribute (hal_wifi_supplicant_hwservice_27_0) true)
(expandtypeattribute (hardware_properties_service_27_0) true)
(expandtypeattribute (hardware_service_27_0) true)
(expandtypeattribute (hci_attach_dev_27_0) true)
(expandtypeattribute (hdmi_control_service_27_0) true)
(expandtypeattribute (healthd_27_0) true)
(expandtypeattribute (healthd_exec_27_0) true)
(expandtypeattribute (heapdump_data_file_27_0) true)
(expandtypeattribute (hidl_allocator_hwservice_27_0) true)
(expandtypeattribute (hidl_base_hwservice_27_0) true)
(expandtypeattribute (hidl_manager_hwservice_27_0) true)
(expandtypeattribute (hidl_memory_hwservice_27_0) true)
(expandtypeattribute (hidl_token_hwservice_27_0) true)
(expandtypeattribute (hwbinder_device_27_0) true)
(expandtypeattribute (hw_random_device_27_0) true)
(expandtypeattribute (hwservice_contexts_file_27_0) true)
(expandtypeattribute (hwservicemanager_27_0) true)
(expandtypeattribute (hwservicemanager_exec_27_0) true)
(expandtypeattribute (hwservicemanager_prop_27_0) true)
(expandtypeattribute (i2c_device_27_0) true)
(expandtypeattribute (icon_file_27_0) true)
(expandtypeattribute (idmap_27_0) true)
(expandtypeattribute (idmap_exec_27_0) true)
(expandtypeattribute (iio_device_27_0) true)
(expandtypeattribute (imms_service_27_0) true)
(expandtypeattribute (incident_27_0) true)
(expandtypeattribute (incidentd_27_0) true)
(expandtypeattribute (incident_data_file_27_0) true)
(expandtypeattribute (incident_service_27_0) true)
(expandtypeattribute (init_27_0) true)
(expandtypeattribute (init_exec_27_0) true)
(expandtypeattribute (inotify_27_0) true)
(expandtypeattribute (input_device_27_0) true)
(expandtypeattribute (inputflinger_27_0) true)
(expandtypeattribute (inputflinger_exec_27_0) true)
(expandtypeattribute (inputflinger_service_27_0) true)
(expandtypeattribute (input_method_service_27_0) true)
(expandtypeattribute (input_service_27_0) true)
(expandtypeattribute (installd_27_0) true)
(expandtypeattribute (install_data_file_27_0) true)
(expandtypeattribute (installd_exec_27_0) true)
(expandtypeattribute (installd_service_27_0) true)
(expandtypeattribute (install_recovery_27_0) true)
(expandtypeattribute (install_recovery_exec_27_0) true)
(expandtypeattribute (ion_device_27_0) true)
(expandtypeattribute (IProxyService_service_27_0) true)
(expandtypeattribute (ipsec_service_27_0) true)
(expandtypeattribute (isolated_app_27_0) true)
(expandtypeattribute (jobscheduler_service_27_0) true)
(expandtypeattribute (kernel_27_0) true)
(expandtypeattribute (keychain_data_file_27_0) true)
(expandtypeattribute (keychord_device_27_0) true)
(expandtypeattribute (keystore_27_0) true)
(expandtypeattribute (keystore_data_file_27_0) true)
(expandtypeattribute (keystore_exec_27_0) true)
(expandtypeattribute (keystore_service_27_0) true)
(expandtypeattribute (kmem_device_27_0) true)
(expandtypeattribute (kmsg_debug_device_27_0) true)
(expandtypeattribute (kmsg_device_27_0) true)
(expandtypeattribute (labeledfs_27_0) true)
(expandtypeattribute (launcherapps_service_27_0) true)
(expandtypeattribute (lmkd_27_0) true)
(expandtypeattribute (lmkd_exec_27_0) true)
(expandtypeattribute (lmkd_socket_27_0) true)
(expandtypeattribute (location_service_27_0) true)
(expandtypeattribute (lock_settings_service_27_0) true)
(expandtypeattribute (logcat_exec_27_0) true)
(expandtypeattribute (logd_27_0) true)
(expandtypeattribute (logd_exec_27_0) true)
(expandtypeattribute (logd_prop_27_0) true)
(expandtypeattribute (logdr_socket_27_0) true)
(expandtypeattribute (logd_socket_27_0) true)
(expandtypeattribute (logdw_socket_27_0) true)
(expandtypeattribute (logpersist_27_0) true)
(expandtypeattribute (logpersistd_logging_prop_27_0) true)
(expandtypeattribute (log_prop_27_0) true)
(expandtypeattribute (log_tag_prop_27_0) true)
(expandtypeattribute (loop_control_device_27_0) true)
(expandtypeattribute (loop_device_27_0) true)
(expandtypeattribute (mac_perms_file_27_0) true)
(expandtypeattribute (mdnsd_27_0) true)
(expandtypeattribute (mdnsd_socket_27_0) true)
(expandtypeattribute (mdns_socket_27_0) true)
(expandtypeattribute (mediacodec_27_0) true)
(expandtypeattribute (mediacodec_exec_27_0) true)
(expandtypeattribute (mediacodec_service_27_0) true)
(expandtypeattribute (media_data_file_27_0) true)
(expandtypeattribute (mediadrmserver_27_0) true)
(expandtypeattribute (mediadrmserver_exec_27_0) true)
(expandtypeattribute (mediadrmserver_service_27_0) true)
(expandtypeattribute (mediaextractor_27_0) true)
(expandtypeattribute (mediaextractor_exec_27_0) true)
(expandtypeattribute (mediaextractor_service_27_0) true)
(expandtypeattribute (mediametrics_27_0) true)
(expandtypeattribute (mediametrics_exec_27_0) true)
(expandtypeattribute (mediametrics_service_27_0) true)
(expandtypeattribute (media_projection_service_27_0) true)
(expandtypeattribute (mediaprovider_27_0) true)
(expandtypeattribute (media_router_service_27_0) true)
(expandtypeattribute (media_rw_data_file_27_0) true)
(expandtypeattribute (mediaserver_27_0) true)
(expandtypeattribute (mediaserver_exec_27_0) true)
(expandtypeattribute (mediaserver_service_27_0) true)
(expandtypeattribute (media_session_service_27_0) true)
(expandtypeattribute (meminfo_service_27_0) true)
(expandtypeattribute (metadata_block_device_27_0) true)
(expandtypeattribute (method_trace_data_file_27_0) true)
(expandtypeattribute (midi_service_27_0) true)
(expandtypeattribute (misc_block_device_27_0) true)
(expandtypeattribute (misc_logd_file_27_0) true)
(expandtypeattribute (misc_user_data_file_27_0) true)
(expandtypeattribute (mmc_prop_27_0) true)
(expandtypeattribute (mnt_expand_file_27_0) true)
(expandtypeattribute (mnt_media_rw_file_27_0) true)
(expandtypeattribute (mnt_media_rw_stub_file_27_0) true)
(expandtypeattribute (mnt_user_file_27_0) true)
(expandtypeattribute (modprobe_27_0) true)
(expandtypeattribute (mount_service_27_0) true)
(expandtypeattribute (mqueue_27_0) true)
(expandtypeattribute (mtd_device_27_0) true)
(expandtypeattribute (mtp_27_0) true)
(expandtypeattribute (mtp_device_27_0) true)
(expandtypeattribute (mtpd_socket_27_0) true)
(expandtypeattribute (mtp_exec_27_0) true)
(expandtypeattribute (nativetest_data_file_27_0) true)
(expandtypeattribute (netd_27_0) true)
(expandtypeattribute (net_data_file_27_0) true)
(expandtypeattribute (netd_exec_27_0) true)
(expandtypeattribute (netd_listener_service_27_0) true)
(expandtypeattribute (net_dns_prop_27_0) true)
(expandtypeattribute (netd_service_27_0) true)
(expandtypeattribute (netd_socket_27_0) true)
(expandtypeattribute (netd_stable_secret_prop_27_0) true)
(expandtypeattribute (netif_27_0) true)
(expandtypeattribute (netpolicy_service_27_0) true)
(expandtypeattribute (net_radio_prop_27_0) true)
(expandtypeattribute (netstats_service_27_0) true)
(expandtypeattribute (netutils_wrapper_27_0) true)
(expandtypeattribute (netutils_wrapper_exec_27_0) true)
(expandtypeattribute (network_management_service_27_0) true)
(expandtypeattribute (network_score_service_27_0) true)
(expandtypeattribute (network_time_update_service_27_0) true)
(expandtypeattribute (nfc_27_0) true)
(expandtypeattribute (nfc_data_file_27_0) true)
(expandtypeattribute (nfc_device_27_0) true)
(expandtypeattribute (nfc_prop_27_0) true)
(expandtypeattribute (nfc_service_27_0) true)
(expandtypeattribute (node_27_0) true)
(expandtypeattribute (nonplat_service_contexts_file_27_0) true)
(expandtypeattribute (notification_service_27_0) true)
(expandtypeattribute (null_device_27_0) true)
(expandtypeattribute (oemfs_27_0) true)
(expandtypeattribute (oem_lock_service_27_0) true)
(expandtypeattribute (ota_data_file_27_0) true)
(expandtypeattribute (otadexopt_service_27_0) true)
(expandtypeattribute (ota_package_file_27_0) true)
(expandtypeattribute (otapreopt_chroot_27_0) true)
(expandtypeattribute (otapreopt_chroot_exec_27_0) true)
(expandtypeattribute (otapreopt_slot_27_0) true)
(expandtypeattribute (otapreopt_slot_exec_27_0) true)
(expandtypeattribute (overlay_prop_27_0) true)
(expandtypeattribute (overlay_service_27_0) true)
(expandtypeattribute (owntty_device_27_0) true)
(expandtypeattribute (package_native_service_27_0) true)
(expandtypeattribute (package_service_27_0) true)
(expandtypeattribute (pan_result_prop_27_0) true)
(expandtypeattribute (pdx_bufferhub_client_channel_socket_27_0) true)
(expandtypeattribute (pdx_bufferhub_client_endpoint_socket_27_0) true)
(expandtypeattribute (pdx_bufferhub_dir_27_0) true)
(expandtypeattribute (pdx_display_client_channel_socket_27_0) true)
(expandtypeattribute (pdx_display_client_endpoint_socket_27_0) true)
(expandtypeattribute (pdx_display_dir_27_0) true)
(expandtypeattribute (pdx_display_manager_channel_socket_27_0) true)
(expandtypeattribute (pdx_display_manager_endpoint_socket_27_0) true)
(expandtypeattribute (pdx_display_screenshot_channel_socket_27_0) true)
(expandtypeattribute (pdx_display_screenshot_endpoint_socket_27_0) true)
(expandtypeattribute (pdx_display_vsync_channel_socket_27_0) true)
(expandtypeattribute (pdx_display_vsync_endpoint_socket_27_0) true)
(expandtypeattribute (pdx_performance_client_channel_socket_27_0) true)
(expandtypeattribute (pdx_performance_client_endpoint_socket_27_0) true)
(expandtypeattribute (pdx_performance_dir_27_0) true)
(expandtypeattribute (performanced_27_0) true)
(expandtypeattribute (performanced_exec_27_0) true)
(expandtypeattribute (perfprofd_27_0) true)
(expandtypeattribute (perfprofd_data_file_27_0) true)
(expandtypeattribute (perfprofd_exec_27_0) true)
(expandtypeattribute (permission_service_27_0) true)
(expandtypeattribute (persist_debug_prop_27_0) true)
(expandtypeattribute (persistent_data_block_service_27_0) true)
(expandtypeattribute (persistent_properties_ready_prop_27_0) true)
(expandtypeattribute (pinner_service_27_0) true)
(expandtypeattribute (pipefs_27_0) true)
(expandtypeattribute (platform_app_27_0) true)
(expandtypeattribute (pmsg_device_27_0) true)
(expandtypeattribute (port_27_0) true)
(expandtypeattribute (port_device_27_0) true)
(expandtypeattribute (postinstall_27_0) true)
(expandtypeattribute (postinstall_dexopt_27_0) true)
(expandtypeattribute (postinstall_file_27_0) true)
(expandtypeattribute (postinstall_mnt_dir_27_0) true)
(expandtypeattribute (powerctl_prop_27_0) true)
(expandtypeattribute (power_service_27_0) true)
(expandtypeattribute (ppp_27_0) true)
(expandtypeattribute (ppp_device_27_0) true)
(expandtypeattribute (ppp_exec_27_0) true)
(expandtypeattribute (preloads_data_file_27_0) true)
(expandtypeattribute (preloads_media_file_27_0) true)
(expandtypeattribute (preopt2cachename_27_0) true)
(expandtypeattribute (preopt2cachename_exec_27_0) true)
(expandtypeattribute (print_service_27_0) true)
(expandtypeattribute (priv_app_27_0) true)
(expandtypeattribute (proc_27_0) true)
(expandtypeattribute (proc_bluetooth_writable_27_0) true)
(expandtypeattribute (proc_cpuinfo_27_0) true)
(expandtypeattribute (proc_drop_caches_27_0) true)
(expandtypeattribute (processinfo_service_27_0) true)
(expandtypeattribute (proc_interrupts_27_0) true)
(expandtypeattribute (proc_iomem_27_0) true)
(expandtypeattribute (proc_meminfo_27_0) true)
(expandtypeattribute (proc_misc_27_0) true)
(expandtypeattribute (proc_modules_27_0) true)
(expandtypeattribute (proc_net_27_0) true)
(expandtypeattribute (proc_overcommit_memory_27_0) true)
(expandtypeattribute (proc_perf_27_0) true)
(expandtypeattribute (proc_security_27_0) true)
(expandtypeattribute (proc_stat_27_0) true)
(expandtypeattribute (procstats_service_27_0) true)
(expandtypeattribute (proc_sysrq_27_0) true)
(expandtypeattribute (proc_timer_27_0) true)
(expandtypeattribute (proc_tty_drivers_27_0) true)
(expandtypeattribute (proc_uid_cputime_removeuid_27_0) true)
(expandtypeattribute (proc_uid_cputime_showstat_27_0) true)
(expandtypeattribute (proc_uid_io_stats_27_0) true)
(expandtypeattribute (proc_uid_procstat_set_27_0) true)
(expandtypeattribute (proc_uid_time_in_state_27_0) true)
(expandtypeattribute (proc_zoneinfo_27_0) true)
(expandtypeattribute (profman_27_0) true)
(expandtypeattribute (profman_dump_data_file_27_0) true)
(expandtypeattribute (profman_exec_27_0) true)
(expandtypeattribute (properties_device_27_0) true)
(expandtypeattribute (properties_serial_27_0) true)
(expandtypeattribute (property_contexts_file_27_0) true)
(expandtypeattribute (property_data_file_27_0) true)
(expandtypeattribute (property_socket_27_0) true)
(expandtypeattribute (pstorefs_27_0) true)
(expandtypeattribute (ptmx_device_27_0) true)
(expandtypeattribute (qtaguid_device_27_0) true)
(expandtypeattribute (qtaguid_proc_27_0) true)
(expandtypeattribute (racoon_27_0) true)
(expandtypeattribute (racoon_exec_27_0) true)
(expandtypeattribute (racoon_socket_27_0) true)
(expandtypeattribute (radio_27_0) true)
(expandtypeattribute (radio_data_file_27_0) true)
(expandtypeattribute (radio_device_27_0) true)
(expandtypeattribute (radio_prop_27_0) true)
(expandtypeattribute (radio_service_27_0) true)
(expandtypeattribute (ram_device_27_0) true)
(expandtypeattribute (random_device_27_0) true)
(expandtypeattribute (reboot_data_file_27_0) true)
(expandtypeattribute (recovery_27_0) true)
(expandtypeattribute (recovery_block_device_27_0) true)
(expandtypeattribute (recovery_data_file_27_0) true)
(expandtypeattribute (recovery_persist_27_0) true)
(expandtypeattribute (recovery_persist_exec_27_0) true)
(expandtypeattribute (recovery_refresh_27_0) true)
(expandtypeattribute (recovery_refresh_exec_27_0) true)
(expandtypeattribute (recovery_service_27_0) true)
(expandtypeattribute (registry_service_27_0) true)
(expandtypeattribute (resourcecache_data_file_27_0) true)
(expandtypeattribute (restorecon_prop_27_0) true)
(expandtypeattribute (restrictions_service_27_0) true)
(expandtypeattribute (rild_27_0) true)
(expandtypeattribute (rild_debug_socket_27_0) true)
(expandtypeattribute (rild_socket_27_0) true)
(expandtypeattribute (ringtone_file_27_0) true)
(expandtypeattribute (root_block_device_27_0) true)
(expandtypeattribute (rootfs_27_0) true)
(expandtypeattribute (rpmsg_device_27_0) true)
(expandtypeattribute (rtc_device_27_0) true)
(expandtypeattribute (rttmanager_service_27_0) true)
(expandtypeattribute (runas_27_0) true)
(expandtypeattribute (runas_exec_27_0) true)
(expandtypeattribute (runtime_event_log_tags_file_27_0) true)
(expandtypeattribute (safemode_prop_27_0) true)
(expandtypeattribute (same_process_hal_file_27_0) true)
(expandtypeattribute (samplingprofiler_service_27_0) true)
(expandtypeattribute (scheduling_policy_service_27_0) true)
(expandtypeattribute (sdcardd_27_0) true)
(expandtypeattribute (sdcardd_exec_27_0) true)
(expandtypeattribute (sdcardfs_27_0) true)
(expandtypeattribute (seapp_contexts_file_27_0) true)
(expandtypeattribute (search_service_27_0) true)
(expandtypeattribute (sec_key_att_app_id_provider_service_27_0) true)
(expandtypeattribute (selinuxfs_27_0) true)
(expandtypeattribute (sensors_device_27_0) true)
(expandtypeattribute (sensorservice_service_27_0) true)
(expandtypeattribute (sepolicy_file_27_0) true)
(expandtypeattribute (serial_device_27_0) true)
(expandtypeattribute (serialno_prop_27_0) true)
(expandtypeattribute (serial_service_27_0) true)
(expandtypeattribute (service_contexts_file_27_0) true)
(expandtypeattribute (servicediscovery_service_27_0) true)
(expandtypeattribute (servicemanager_27_0) true)
(expandtypeattribute (servicemanager_exec_27_0) true)
(expandtypeattribute (settings_service_27_0) true)
(expandtypeattribute (sgdisk_27_0) true)
(expandtypeattribute (sgdisk_exec_27_0) true)
(expandtypeattribute (shared_relro_27_0) true)
(expandtypeattribute (shared_relro_file_27_0) true)
(expandtypeattribute (shell_27_0) true)
(expandtypeattribute (shell_data_file_27_0) true)
(expandtypeattribute (shell_exec_27_0) true)
(expandtypeattribute (shell_prop_27_0) true)
(expandtypeattribute (shm_27_0) true)
(expandtypeattribute (shortcut_manager_icons_27_0) true)
(expandtypeattribute (shortcut_service_27_0) true)
(expandtypeattribute (slideshow_27_0) true)
(expandtypeattribute (socket_device_27_0) true)
(expandtypeattribute (sockfs_27_0) true)
(expandtypeattribute (statusbar_service_27_0) true)
(expandtypeattribute (storaged_service_27_0) true)
(expandtypeattribute (storage_file_27_0) true)
(expandtypeattribute (storagestats_service_27_0) true)
(expandtypeattribute (storage_stub_file_27_0) true)
(expandtypeattribute (su_27_0) true)
(expandtypeattribute (su_exec_27_0) true)
(expandtypeattribute (surfaceflinger_27_0) true)
(expandtypeattribute (surfaceflinger_service_27_0) true)
(expandtypeattribute (swap_block_device_27_0) true)
(expandtypeattribute (sysfs_27_0) true)
(expandtypeattribute (sysfs_batteryinfo_27_0) true)
(expandtypeattribute (sysfs_bluetooth_writable_27_0) true)
(expandtypeattribute (sysfs_devices_system_cpu_27_0) true)
(expandtypeattribute (sysfs_fs_ext4_features_27_0) true)
(expandtypeattribute (sysfs_hwrandom_27_0) true)
(expandtypeattribute (sysfs_leds_27_0) true)
(expandtypeattribute (sysfs_lowmemorykiller_27_0) true)
(expandtypeattribute (sysfs_mac_address_27_0) true)
(expandtypeattribute (sysfs_nfc_power_writable_27_0) true)
(expandtypeattribute (sysfs_thermal_27_0) true)
(expandtypeattribute (sysfs_uio_27_0) true)
(expandtypeattribute (sysfs_usb_27_0) true)
(expandtypeattribute (sysfs_usermodehelper_27_0) true)
(expandtypeattribute (sysfs_vibrator_27_0) true)
(expandtypeattribute (sysfs_wake_lock_27_0) true)
(expandtypeattribute (sysfs_wlan_fwpath_27_0) true)
(expandtypeattribute (sysfs_zram_27_0) true)
(expandtypeattribute (sysfs_zram_uevent_27_0) true)
(expandtypeattribute (system_app_27_0) true)
(expandtypeattribute (system_app_data_file_27_0) true)
(expandtypeattribute (system_app_service_27_0) true)
(expandtypeattribute (system_block_device_27_0) true)
(expandtypeattribute (system_data_file_27_0) true)
(expandtypeattribute (system_file_27_0) true)
(expandtypeattribute (systemkeys_data_file_27_0) true)
(expandtypeattribute (system_ndebug_socket_27_0) true)
(expandtypeattribute (system_net_netd_hwservice_27_0) true)
(expandtypeattribute (system_prop_27_0) true)
(expandtypeattribute (system_radio_prop_27_0) true)
(expandtypeattribute (system_server_27_0) true)
(expandtypeattribute (system_wifi_keystore_hwservice_27_0) true)
(expandtypeattribute (system_wpa_socket_27_0) true)
(expandtypeattribute (task_service_27_0) true)
(expandtypeattribute (tee_27_0) true)
(expandtypeattribute (tee_data_file_27_0) true)
(expandtypeattribute (tee_device_27_0) true)
(expandtypeattribute (telecom_service_27_0) true)
(expandtypeattribute (textclassification_service_27_0) true)
(expandtypeattribute (textclassifier_data_file_27_0) true)
(expandtypeattribute (textservices_service_27_0) true)
(expandtypeattribute (thermalcallback_hwservice_27_0) true)
(expandtypeattribute (thermal_service_27_0) true)
(expandtypeattribute (thermalserviced_27_0) true)
(expandtypeattribute (thermalserviced_exec_27_0) true)
(expandtypeattribute (timezone_service_27_0) true)
(expandtypeattribute (tmpfs_27_0) true)
(expandtypeattribute (tombstoned_27_0) true)
(expandtypeattribute (tombstone_data_file_27_0) true)
(expandtypeattribute (tombstoned_crash_socket_27_0) true)
(expandtypeattribute (tombstoned_exec_27_0) true)
(expandtypeattribute (tombstoned_intercept_socket_27_0) true)
(expandtypeattribute (tombstoned_java_trace_socket_27_0) true)
(expandtypeattribute (toolbox_27_0) true)
(expandtypeattribute (toolbox_exec_27_0) true)
(expandtypeattribute (trust_service_27_0) true)
(expandtypeattribute (tty_device_27_0) true)
(expandtypeattribute (tun_device_27_0) true)
(expandtypeattribute (tv_input_service_27_0) true)
(expandtypeattribute (tzdatacheck_27_0) true)
(expandtypeattribute (tzdatacheck_exec_27_0) true)
(expandtypeattribute (ueventd_27_0) true)
(expandtypeattribute (uhid_device_27_0) true)
(expandtypeattribute (uimode_service_27_0) true)
(expandtypeattribute (uio_device_27_0) true)
(expandtypeattribute (uncrypt_27_0) true)
(expandtypeattribute (uncrypt_exec_27_0) true)
(expandtypeattribute (uncrypt_socket_27_0) true)
(expandtypeattribute (unencrypted_data_file_27_0) true)
(expandtypeattribute (unlabeled_27_0) true)
(expandtypeattribute (untrusted_app_25_27_0) true)
(expandtypeattribute (untrusted_app_27_0) true)
(expandtypeattribute (untrusted_v2_app_27_0) true)
(expandtypeattribute (update_engine_27_0) true)
(expandtypeattribute (update_engine_data_file_27_0) true)
(expandtypeattribute (update_engine_exec_27_0) true)
(expandtypeattribute (update_engine_service_27_0) true)
(expandtypeattribute (updatelock_service_27_0) true)
(expandtypeattribute (update_verifier_27_0) true)
(expandtypeattribute (update_verifier_exec_27_0) true)
(expandtypeattribute (usagestats_service_27_0) true)
(expandtypeattribute (usbaccessory_device_27_0) true)
(expandtypeattribute (usb_device_27_0) true)
(expandtypeattribute (usbfs_27_0) true)
(expandtypeattribute (usb_service_27_0) true)
(expandtypeattribute (userdata_block_device_27_0) true)
(expandtypeattribute (usermodehelper_27_0) true)
(expandtypeattribute (user_profile_data_file_27_0) true)
(expandtypeattribute (user_service_27_0) true)
(expandtypeattribute (vcs_device_27_0) true)
(expandtypeattribute (vdc_27_0) true)
(expandtypeattribute (vdc_exec_27_0) true)
(expandtypeattribute (vendor_app_file_27_0) true)
(expandtypeattribute (vendor_configs_file_27_0) true)
(expandtypeattribute (vendor_file_27_0) true)
(expandtypeattribute (vendor_framework_file_27_0) true)
(expandtypeattribute (vendor_hal_file_27_0) true)
(expandtypeattribute (vendor_overlay_file_27_0) true)
(expandtypeattribute (vendor_shell_exec_27_0) true)
(expandtypeattribute (vendor_toolbox_exec_27_0) true)
(expandtypeattribute (vfat_27_0) true)
(expandtypeattribute (vibrator_service_27_0) true)
(expandtypeattribute (video_device_27_0) true)
(expandtypeattribute (virtual_touchpad_27_0) true)
(expandtypeattribute (virtual_touchpad_exec_27_0) true)
(expandtypeattribute (virtual_touchpad_service_27_0) true)
(expandtypeattribute (vndbinder_device_27_0) true)
(expandtypeattribute (vndk_sp_file_27_0) true)
(expandtypeattribute (vndservice_contexts_file_27_0) true)
(expandtypeattribute (vndservicemanager_27_0) true)
(expandtypeattribute (voiceinteraction_service_27_0) true)
(expandtypeattribute (vold_27_0) true)
(expandtypeattribute (vold_data_file_27_0) true)
(expandtypeattribute (vold_device_27_0) true)
(expandtypeattribute (vold_exec_27_0) true)
(expandtypeattribute (vold_prop_27_0) true)
(expandtypeattribute (vold_socket_27_0) true)
(expandtypeattribute (vpn_data_file_27_0) true)
(expandtypeattribute (vr_hwc_27_0) true)
(expandtypeattribute (vr_hwc_exec_27_0) true)
(expandtypeattribute (vr_hwc_service_27_0) true)
(expandtypeattribute (vr_manager_service_27_0) true)
(expandtypeattribute (wallpaper_file_27_0) true)
(expandtypeattribute (wallpaper_service_27_0) true)
(expandtypeattribute (watchdogd_27_0) true)
(expandtypeattribute (watchdog_device_27_0) true)
(expandtypeattribute (webviewupdate_service_27_0) true)
(expandtypeattribute (webview_zygote_27_0) true)
(expandtypeattribute (webview_zygote_exec_27_0) true)
(expandtypeattribute (webview_zygote_socket_27_0) true)
(expandtypeattribute (wifiaware_service_27_0) true)
(expandtypeattribute (wificond_27_0) true)
(expandtypeattribute (wificond_exec_27_0) true)
(expandtypeattribute (wificond_service_27_0) true)
(expandtypeattribute (wifi_data_file_27_0) true)
(expandtypeattribute (wifi_log_prop_27_0) true)
(expandtypeattribute (wifip2p_service_27_0) true)
(expandtypeattribute (wifi_prop_27_0) true)
(expandtypeattribute (wifiscanner_service_27_0) true)
(expandtypeattribute (wifi_service_27_0) true)
(expandtypeattribute (window_service_27_0) true)
(expandtypeattribute (wpa_socket_27_0) true)
(expandtypeattribute (zero_device_27_0) true)
(expandtypeattribute (zoneinfo_data_file_27_0) true)
(expandtypeattribute (zygote_27_0) true)
(expandtypeattribute (zygote_exec_27_0) true)
(expandtypeattribute (zygote_socket_27_0) true)
(typeattributeset accessibility_service_27_0 (accessibility_service))
(typeattributeset account_service_27_0 (account_service))
(typeattributeset activity_service_27_0 (activity_service))
(typeattributeset adbd_27_0 (adbd))
(typeattributeset adb_data_file_27_0 (adb_data_file))
(typeattributeset adbd_exec_27_0 (adbd_exec))
(typeattributeset adbd_socket_27_0 (adbd_socket))
(typeattributeset adb_keys_file_27_0 (adb_keys_file))
(typeattributeset alarm_device_27_0 (alarm_device))
(typeattributeset alarm_service_27_0 (alarm_service))
(typeattributeset anr_data_file_27_0 (anr_data_file))
(typeattributeset apk_data_file_27_0 (apk_data_file))
(typeattributeset apk_private_data_file_27_0 (apk_private_data_file))
(typeattributeset apk_private_tmp_file_27_0 (apk_private_tmp_file))
(typeattributeset apk_tmp_file_27_0 (apk_tmp_file))
(typeattributeset app_data_file_27_0 (app_data_file))
(typeattributeset app_fuse_file_27_0 (app_fuse_file))
(typeattributeset app_fusefs_27_0 (app_fusefs))
(typeattributeset appops_service_27_0 (appops_service))
(typeattributeset appwidget_service_27_0 (appwidget_service))
(typeattributeset asec_apk_file_27_0 (asec_apk_file))
(typeattributeset asec_image_file_27_0 (asec_image_file))
(typeattributeset asec_public_file_27_0 (asec_public_file))
(typeattributeset ashmem_device_27_0 (ashmem_device))
(typeattributeset assetatlas_service_27_0 (assetatlas_service))
(typeattributeset audio_data_file_27_0 (audio_data_file))
(typeattributeset audio_device_27_0 (audio_device))
(typeattributeset audiohal_data_file_27_0 (audiohal_data_file))
(typeattributeset audio_prop_27_0 (audio_prop))
(typeattributeset audio_seq_device_27_0 (audio_seq_device))
(typeattributeset audioserver_27_0 (audioserver))
(typeattributeset audioserver_data_file_27_0 (audioserver_data_file))
(typeattributeset audioserver_service_27_0 (audioserver_service))
(typeattributeset audio_service_27_0 (audio_service))
(typeattributeset audio_timer_device_27_0 (audio_timer_device))
(typeattributeset autofill_service_27_0 (autofill_service))
(typeattributeset backup_data_file_27_0 (backup_data_file))
(typeattributeset backup_service_27_0 (backup_service))
(typeattributeset batteryproperties_service_27_0 (batteryproperties_service))
(typeattributeset battery_service_27_0 (battery_service))
(typeattributeset batterystats_service_27_0 (batterystats_service))
(typeattributeset binder_device_27_0 (binder_device))
(typeattributeset binfmt_miscfs_27_0 (binfmt_miscfs))
(typeattributeset blkid_27_0 (blkid))
(typeattributeset blkid_untrusted_27_0 (blkid_untrusted))
(typeattributeset block_device_27_0 (block_device))
(typeattributeset bluetooth_27_0 (bluetooth))
(typeattributeset bluetooth_data_file_27_0 (bluetooth_data_file))
(typeattributeset bluetooth_efs_file_27_0 (bluetooth_efs_file))
(typeattributeset bluetooth_logs_data_file_27_0 (bluetooth_logs_data_file))
(typeattributeset bluetooth_manager_service_27_0 (bluetooth_manager_service))
(typeattributeset bluetooth_prop_27_0 (bluetooth_prop))
(typeattributeset bluetooth_service_27_0 (bluetooth_service))
(typeattributeset bluetooth_socket_27_0 (bluetooth_socket))
(typeattributeset bootanim_27_0 (bootanim))
(typeattributeset bootanim_exec_27_0 (bootanim_exec))
(typeattributeset boot_block_device_27_0 (boot_block_device))
(typeattributeset bootchart_data_file_27_0 (bootchart_data_file))
(typeattributeset bootstat_27_0 (bootstat))
(typeattributeset bootstat_data_file_27_0 (bootstat_data_file))
(typeattributeset bootstat_exec_27_0 (bootstat_exec))
(typeattributeset boottime_prop_27_0 (boottime_prop))
(typeattributeset boottrace_data_file_27_0 (boottrace_data_file))
(typeattributeset broadcastradio_service_27_0 (broadcastradio_service))
(typeattributeset bufferhubd_27_0 (bufferhubd))
(typeattributeset bufferhubd_exec_27_0 (bufferhubd_exec))
(typeattributeset cache_backup_file_27_0 (cache_backup_file))
(typeattributeset cache_block_device_27_0 (cache_block_device))
(typeattributeset cache_file_27_0 (cache_file))
(typeattributeset cache_private_backup_file_27_0 (cache_private_backup_file))
(typeattributeset cache_recovery_file_27_0 (cache_recovery_file))
(typeattributeset camera_data_file_27_0 (camera_data_file))
(typeattributeset camera_device_27_0 (camera_device))
(typeattributeset cameraproxy_service_27_0 (cameraproxy_service))
(typeattributeset cameraserver_27_0 (cameraserver))
(typeattributeset cameraserver_exec_27_0 (cameraserver_exec))
(typeattributeset cameraserver_service_27_0 (cameraserver_service))
(typeattributeset cgroup_27_0 (cgroup))
(typeattributeset charger_27_0 (charger))
(typeattributeset clatd_27_0 (clatd))
(typeattributeset clatd_exec_27_0 (clatd_exec))
(typeattributeset clipboard_service_27_0 (clipboard_service))
(typeattributeset commontime_management_service_27_0 (commontime_management_service))
(typeattributeset companion_device_service_27_0 (companion_device_service))
(typeattributeset configfs_27_0 (configfs))
(typeattributeset config_prop_27_0 (config_prop))
(typeattributeset connectivity_service_27_0 (connectivity_service))
(typeattributeset connmetrics_service_27_0 (connmetrics_service))
(typeattributeset console_device_27_0 (console_device))
(typeattributeset consumer_ir_service_27_0 (consumer_ir_service))
(typeattributeset content_service_27_0 (content_service))
(typeattributeset contexthub_service_27_0 (contexthub_service))
(typeattributeset coredump_file_27_0 (coredump_file))
(typeattributeset country_detector_service_27_0 (country_detector_service))
(typeattributeset coverage_service_27_0 (coverage_service))
(typeattributeset cppreopt_prop_27_0 (cppreopt_prop))
(typeattributeset cppreopts_27_0 (cppreopts))
(typeattributeset cppreopts_exec_27_0 (cppreopts_exec))
(typeattributeset cpuctl_device_27_0 (cpuctl_device))
(typeattributeset cpuinfo_service_27_0 (cpuinfo_service))
(typeattributeset crash_dump_27_0 (crash_dump))
(typeattributeset crash_dump_exec_27_0 (crash_dump_exec))
(typeattributeset ctl_bootanim_prop_27_0 (ctl_bootanim_prop))
(typeattributeset ctl_bugreport_prop_27_0 (ctl_bugreport_prop))
(typeattributeset ctl_console_prop_27_0 (ctl_console_prop))
(typeattributeset ctl_default_prop_27_0 (ctl_default_prop ctl_restart_prop ctl_start_prop ctl_stop_prop))
(typeattributeset ctl_dumpstate_prop_27_0 (ctl_dumpstate_prop))
(typeattributeset ctl_fuse_prop_27_0 (ctl_fuse_prop))
(typeattributeset ctl_mdnsd_prop_27_0 (ctl_mdnsd_prop))
(typeattributeset ctl_rildaemon_prop_27_0 (ctl_rildaemon_prop))
(typeattributeset dalvikcache_data_file_27_0 (dalvikcache_data_file))
(typeattributeset dalvik_prop_27_0 (dalvik_prop))
(typeattributeset dbinfo_service_27_0 (dbinfo_service))
(typeattributeset debugfs_27_0
  ( debugfs
    debugfs_wakeup_sources))
(typeattributeset debugfs_mmc_27_0 (debugfs_mmc))
(typeattributeset debugfs_trace_marker_27_0 (debugfs_trace_marker))
(typeattributeset debugfs_tracing_27_0 (debugfs_tracing))
(typeattributeset debugfs_tracing_debug_27_0 (debugfs_tracing_debug))
(typeattributeset debugfs_tracing_instances_27_0 (debugfs_tracing_instances))
(typeattributeset debugfs_wifi_tracing_27_0 (debugfs_wifi_tracing))
(typeattributeset debuggerd_prop_27_0 (debuggerd_prop))
(typeattributeset debug_prop_27_0 (debug_prop))
(typeattributeset default_android_hwservice_27_0 (default_android_hwservice))
(typeattributeset default_android_service_27_0 (default_android_service))
(typeattributeset default_android_vndservice_27_0 (default_android_vndservice))
(typeattributeset default_prop_27_0
  ( default_prop
    pm_prop))
(typeattributeset device_27_0 (device))
(typeattributeset device_identifiers_service_27_0 (device_identifiers_service))
(typeattributeset deviceidle_service_27_0 (deviceidle_service))
(typeattributeset device_logging_prop_27_0 (device_logging_prop))
(typeattributeset device_policy_service_27_0 (device_policy_service))
(typeattributeset devicestoragemonitor_service_27_0 (devicestoragemonitor_service))
(typeattributeset devpts_27_0 (devpts))
(typeattributeset dex2oat_27_0 (dex2oat))
(typeattributeset dex2oat_exec_27_0 (dex2oat_exec))
(typeattributeset dhcp_27_0 (dhcp))
(typeattributeset dhcp_data_file_27_0 (dhcp_data_file))
(typeattributeset dhcp_exec_27_0 (dhcp_exec))
(typeattributeset dhcp_prop_27_0 (dhcp_prop))
(typeattributeset diskstats_service_27_0 (diskstats_service))
(typeattributeset display_service_27_0 (display_service))
(typeattributeset dm_device_27_0 (dm_device))
(typeattributeset dnsmasq_27_0 (dnsmasq))
(typeattributeset dnsmasq_exec_27_0 (dnsmasq_exec))
(typeattributeset dnsproxyd_socket_27_0 (dnsproxyd_socket))
(typeattributeset DockObserver_service_27_0 (DockObserver_service))
(typeattributeset dreams_service_27_0 (dreams_service))
(typeattributeset drm_data_file_27_0 (drm_data_file))
(typeattributeset drmserver_27_0 (drmserver))
(typeattributeset drmserver_exec_27_0 (drmserver_exec))
(typeattributeset drmserver_service_27_0 (drmserver_service))
(typeattributeset drmserver_socket_27_0 (drmserver_socket))
(typeattributeset dropbox_service_27_0 (dropbox_service))
(typeattributeset dumpstate_27_0 (dumpstate))
(typeattributeset dumpstate_exec_27_0 (dumpstate_exec))
(typeattributeset dumpstate_options_prop_27_0 (dumpstate_options_prop))
(typeattributeset dumpstate_prop_27_0 (dumpstate_prop))
(typeattributeset dumpstate_service_27_0 (dumpstate_service))
(typeattributeset dumpstate_socket_27_0 (dumpstate_socket))
(typeattributeset e2fs_27_0 (e2fs))
(typeattributeset e2fs_exec_27_0 (e2fs_exec))
(typeattributeset efs_file_27_0 (efs_file))
(typeattributeset ephemeral_app_27_0 (ephemeral_app))
(typeattributeset ethernet_service_27_0 (ethernet_service))
(typeattributeset ffs_prop_27_0 (ffs_prop))
(typeattributeset file_contexts_file_27_0 (file_contexts_file))
(typeattributeset fingerprintd_27_0 (fingerprintd))
(typeattributeset fingerprintd_data_file_27_0 (fingerprintd_data_file))
(typeattributeset fingerprintd_exec_27_0 (fingerprintd_exec))
(typeattributeset fingerprintd_service_27_0 (fingerprintd_service))
(typeattributeset fingerprint_prop_27_0 (fingerprint_prop))
(typeattributeset fingerprint_service_27_0 (fingerprint_service))
(typeattributeset firstboot_prop_27_0 (firstboot_prop))
(typeattributeset font_service_27_0 (font_service))
(typeattributeset frp_block_device_27_0 (frp_block_device))
(typeattributeset fsck_27_0 (fsck))
(typeattributeset fsck_exec_27_0 (fsck_exec))
(typeattributeset fscklogs_27_0 (fscklogs))
(typeattributeset fsck_untrusted_27_0 (fsck_untrusted))
(typeattributeset full_device_27_0 (full_device))
(typeattributeset functionfs_27_0 (functionfs))
(typeattributeset fuse_27_0 (fuse))
(typeattributeset fuse_device_27_0 (fuse_device))
(typeattributeset fwk_display_hwservice_27_0 (fwk_display_hwservice))
(typeattributeset fwk_scheduler_hwservice_27_0 (fwk_scheduler_hwservice))
(typeattributeset fwk_sensor_hwservice_27_0 (fwk_sensor_hwservice))
(typeattributeset fwmarkd_socket_27_0 (fwmarkd_socket))
(typeattributeset gatekeeperd_27_0 (gatekeeperd))
(typeattributeset gatekeeper_data_file_27_0 (gatekeeper_data_file))
(typeattributeset gatekeeperd_exec_27_0 (gatekeeperd_exec))
(typeattributeset gatekeeper_service_27_0 (gatekeeper_service))
(typeattributeset gfxinfo_service_27_0 (gfxinfo_service))
(typeattributeset gps_control_27_0 (gps_control))
(typeattributeset gpu_device_27_0 (gpu_device))
(typeattributeset gpu_service_27_0 (gpu_service))
(typeattributeset graphics_device_27_0 (graphics_device))
(typeattributeset graphicsstats_service_27_0 (graphicsstats_service))
(typeattributeset hal_audio_hwservice_27_0 (hal_audio_hwservice))
(typeattributeset hal_bluetooth_hwservice_27_0 (hal_bluetooth_hwservice))
(typeattributeset hal_bootctl_hwservice_27_0 (hal_bootctl_hwservice))
(typeattributeset hal_broadcastradio_hwservice_27_0 (hal_broadcastradio_hwservice))
(typeattributeset hal_camera_hwservice_27_0 (hal_camera_hwservice))
(typeattributeset hal_cas_hwservice_27_0 (hal_cas_hwservice))
(typeattributeset hal_configstore_ISurfaceFlingerConfigs_27_0 (hal_configstore_ISurfaceFlingerConfigs))
(typeattributeset hal_contexthub_hwservice_27_0 (hal_contexthub_hwservice))
(typeattributeset hal_drm_hwservice_27_0 (hal_drm_hwservice))
(typeattributeset hal_dumpstate_hwservice_27_0 (hal_dumpstate_hwservice))
(typeattributeset hal_fingerprint_hwservice_27_0 (hal_fingerprint_hwservice))
(typeattributeset hal_fingerprint_service_27_0 (hal_fingerprint_service))
(typeattributeset hal_gatekeeper_hwservice_27_0 (hal_gatekeeper_hwservice))
(typeattributeset hal_gnss_hwservice_27_0 (hal_gnss_hwservice))
(typeattributeset hal_graphics_allocator_hwservice_27_0 (hal_graphics_allocator_hwservice))
(typeattributeset hal_graphics_composer_hwservice_27_0 (hal_graphics_composer_hwservice))
(typeattributeset hal_graphics_mapper_hwservice_27_0 (hal_graphics_mapper_hwservice))
(typeattributeset hal_health_hwservice_27_0 (hal_health_hwservice))
(typeattributeset hal_ir_hwservice_27_0 (hal_ir_hwservice))
(typeattributeset hal_keymaster_hwservice_27_0 (hal_keymaster_hwservice))
(typeattributeset hal_light_hwservice_27_0 (hal_light_hwservice))
(typeattributeset hal_memtrack_hwservice_27_0 (hal_memtrack_hwservice))
(typeattributeset hal_neuralnetworks_hwservice_27_0 (hal_neuralnetworks_hwservice))
(typeattributeset hal_nfc_hwservice_27_0 (hal_nfc_hwservice))
(typeattributeset hal_oemlock_hwservice_27_0 (hal_oemlock_hwservice))
(typeattributeset hal_omx_hwservice_27_0 (hal_omx_hwservice))
(typeattributeset hal_power_hwservice_27_0 (hal_power_hwservice))
(typeattributeset hal_renderscript_hwservice_27_0 (hal_renderscript_hwservice))
(typeattributeset hal_sensors_hwservice_27_0 (hal_sensors_hwservice))
(typeattributeset hal_telephony_hwservice_27_0 (hal_telephony_hwservice))
(typeattributeset hal_tetheroffload_hwservice_27_0 (hal_tetheroffload_hwservice))
(typeattributeset hal_thermal_hwservice_27_0 (hal_thermal_hwservice))
(typeattributeset hal_tv_cec_hwservice_27_0 (hal_tv_cec_hwservice))
(typeattributeset hal_tv_input_hwservice_27_0 (hal_tv_input_hwservice))
(typeattributeset hal_usb_hwservice_27_0 (hal_usb_hwservice))
(typeattributeset hal_vibrator_hwservice_27_0 (hal_vibrator_hwservice))
(typeattributeset hal_vr_hwservice_27_0 (hal_vr_hwservice))
(typeattributeset hal_weaver_hwservice_27_0 (hal_weaver_hwservice))
(typeattributeset hal_wifi_hwservice_27_0 (hal_wifi_hwservice))
(typeattributeset hal_wifi_offload_hwservice_27_0 (hal_wifi_offload_hwservice))
(typeattributeset hal_wifi_supplicant_hwservice_27_0 (hal_wifi_supplicant_hwservice))
(typeattributeset hardware_properties_service_27_0 (hardware_properties_service))
(typeattributeset hardware_service_27_0 (hardware_service))
(typeattributeset hci_attach_dev_27_0 (hci_attach_dev))
(typeattributeset hdmi_control_service_27_0 (hdmi_control_service))
(typeattributeset healthd_27_0 (healthd))
(typeattributeset healthd_exec_27_0 (healthd_exec))
(typeattributeset heapdump_data_file_27_0 (heapdump_data_file))
(typeattributeset hidl_allocator_hwservice_27_0 (hidl_allocator_hwservice))
(typeattributeset hidl_base_hwservice_27_0 (hidl_base_hwservice))
(typeattributeset hidl_manager_hwservice_27_0 (hidl_manager_hwservice))
(typeattributeset hidl_memory_hwservice_27_0 (hidl_memory_hwservice))
(typeattributeset hidl_token_hwservice_27_0 (hidl_token_hwservice))
(typeattributeset hwbinder_device_27_0 (hwbinder_device))
(typeattributeset hw_random_device_27_0 (hw_random_device))
(typeattributeset hwservice_contexts_file_27_0 (hwservice_contexts_file))
(typeattributeset hwservicemanager_27_0 (hwservicemanager))
(typeattributeset hwservicemanager_exec_27_0 (hwservicemanager_exec))
(typeattributeset hwservicemanager_prop_27_0 (hwservicemanager_prop))
(typeattributeset i2c_device_27_0 (i2c_device))
(typeattributeset icon_file_27_0 (icon_file))
(typeattributeset idmap_27_0 (idmap))
(typeattributeset idmap_exec_27_0 (idmap_exec))
(typeattributeset iio_device_27_0 (iio_device))
(typeattributeset imms_service_27_0 (imms_service))
(typeattributeset incident_27_0 (incident))
(typeattributeset incidentd_27_0 (incidentd))
(typeattributeset incident_data_file_27_0 (incident_data_file))
(typeattributeset incident_service_27_0 (incident_service))
(typeattributeset init_27_0 (init))
(typeattributeset init_exec_27_0 (init_exec))
(typeattributeset inotify_27_0 (inotify))
(typeattributeset input_device_27_0 (input_device))
(typeattributeset inputflinger_27_0 (inputflinger))
(typeattributeset inputflinger_exec_27_0 (inputflinger_exec))
(typeattributeset inputflinger_service_27_0 (inputflinger_service))
(typeattributeset input_method_service_27_0 (input_method_service))
(typeattributeset input_service_27_0 (input_service))
(typeattributeset installd_27_0 (installd))
(typeattributeset install_data_file_27_0 (install_data_file))
(typeattributeset installd_exec_27_0 (installd_exec))
(typeattributeset installd_service_27_0 (installd_service))
(typeattributeset install_recovery_27_0 (install_recovery))
(typeattributeset install_recovery_exec_27_0 (install_recovery_exec))
(typeattributeset ion_device_27_0 (ion_device))
(typeattributeset IProxyService_service_27_0 (IProxyService_service))
(typeattributeset ipsec_service_27_0 (ipsec_service))
(typeattributeset isolated_app_27_0 (isolated_app))
(typeattributeset jobscheduler_service_27_0 (jobscheduler_service))
(typeattributeset kernel_27_0 (kernel))
(typeattributeset keychain_data_file_27_0 (keychain_data_file))
(typeattributeset keychord_device_27_0 (keychord_device))
(typeattributeset keystore_27_0 (keystore))
(typeattributeset keystore_data_file_27_0 (keystore_data_file))
(typeattributeset keystore_exec_27_0 (keystore_exec))
(typeattributeset keystore_service_27_0 (keystore_service))
(typeattributeset kmem_device_27_0 (kmem_device))
(typeattributeset kmsg_debug_device_27_0 (kmsg_debug_device))
(typeattributeset kmsg_device_27_0 (kmsg_device))
(typeattributeset labeledfs_27_0 (labeledfs))
(typeattributeset launcherapps_service_27_0 (launcherapps_service))
(typeattributeset lmkd_27_0 (lmkd))
(typeattributeset lmkd_exec_27_0 (lmkd_exec))
(typeattributeset lmkd_socket_27_0 (lmkd_socket))
(typeattributeset location_service_27_0 (location_service))
(typeattributeset lock_settings_service_27_0 (lock_settings_service))
(typeattributeset logcat_exec_27_0 (logcat_exec))
(typeattributeset logd_27_0 (logd))
(typeattributeset logd_exec_27_0 (logd_exec))
(typeattributeset logd_prop_27_0 (logd_prop))
(typeattributeset logdr_socket_27_0 (logdr_socket))
(typeattributeset logd_socket_27_0 (logd_socket))
(typeattributeset logdw_socket_27_0 (logdw_socket))
(typeattributeset logpersist_27_0 (logpersist))
(typeattributeset logpersistd_logging_prop_27_0 (logpersistd_logging_prop))
(typeattributeset log_prop_27_0 (log_prop))
(typeattributeset log_tag_prop_27_0 (log_tag_prop))
(typeattributeset loop_control_device_27_0 (loop_control_device))
(typeattributeset loop_device_27_0 (loop_device))
(typeattributeset mac_perms_file_27_0 (mac_perms_file))
(typeattributeset mdnsd_27_0 (mdnsd))
(typeattributeset mdnsd_socket_27_0 (mdnsd_socket))
(typeattributeset mdns_socket_27_0 (mdns_socket))
(typeattributeset mediacodec_27_0 (mediacodec))
(typeattributeset mediacodec_exec_27_0 (mediacodec_exec))
(typeattributeset mediacodec_service_27_0 (mediacodec_service))
(typeattributeset media_data_file_27_0 (media_data_file))
(typeattributeset mediadrmserver_27_0 (mediadrmserver))
(typeattributeset mediadrmserver_exec_27_0 (mediadrmserver_exec))
(typeattributeset mediadrmserver_service_27_0 (mediadrmserver_service))
(typeattributeset mediaextractor_27_0 (mediaextractor))
(typeattributeset mediaextractor_exec_27_0 (mediaextractor_exec))
(typeattributeset mediaextractor_service_27_0 (mediaextractor_service))
(typeattributeset mediametrics_27_0 (mediametrics))
(typeattributeset mediametrics_exec_27_0 (mediametrics_exec))
(typeattributeset mediametrics_service_27_0 (mediametrics_service))
(typeattributeset media_projection_service_27_0 (media_projection_service))
(typeattributeset mediaprovider_27_0 (mediaprovider))
(typeattributeset media_router_service_27_0 (media_router_service))
(typeattributeset media_rw_data_file_27_0 (media_rw_data_file))
(typeattributeset mediaserver_27_0 (mediaserver))
(typeattributeset mediaserver_exec_27_0 (mediaserver_exec))
(typeattributeset mediaserver_service_27_0 (mediaserver_service))
(typeattributeset media_session_service_27_0 (media_session_service))
(typeattributeset meminfo_service_27_0 (meminfo_service))
(typeattributeset metadata_block_device_27_0 (metadata_block_device))
(typeattributeset method_trace_data_file_27_0 (method_trace_data_file))
(typeattributeset midi_service_27_0 (midi_service))
(typeattributeset misc_block_device_27_0 (misc_block_device))
(typeattributeset misc_logd_file_27_0 (misc_logd_file))
(typeattributeset misc_user_data_file_27_0 (misc_user_data_file))
(typeattributeset mmc_prop_27_0 (mmc_prop))
(typeattributeset mnt_expand_file_27_0 (mnt_expand_file))
(typeattributeset mnt_media_rw_file_27_0 (mnt_media_rw_file))
(typeattributeset mnt_media_rw_stub_file_27_0 (mnt_media_rw_stub_file))
(typeattributeset mnt_user_file_27_0 (mnt_user_file))
(typeattributeset modprobe_27_0 (modprobe))
(typeattributeset mount_service_27_0 (mount_service))
(typeattributeset mqueue_27_0 (mqueue))
(typeattributeset mtd_device_27_0 (mtd_device))
(typeattributeset mtp_27_0 (mtp))
(typeattributeset mtp_device_27_0 (mtp_device))
(typeattributeset mtpd_socket_27_0 (mtpd_socket))
(typeattributeset mtp_exec_27_0 (mtp_exec))
(typeattributeset nativetest_data_file_27_0 (nativetest_data_file))
(typeattributeset netd_27_0 (netd))
(typeattributeset net_data_file_27_0 (net_data_file))
(typeattributeset netd_exec_27_0 (netd_exec))
(typeattributeset netd_listener_service_27_0 (netd_listener_service))
(typeattributeset net_dns_prop_27_0 (net_dns_prop))
(typeattributeset netd_service_27_0 (netd_service))
(typeattributeset netd_socket_27_0 (netd_socket))
(typeattributeset netd_stable_secret_prop_27_0 (netd_stable_secret_prop))
(typeattributeset netif_27_0 (netif))
(typeattributeset netpolicy_service_27_0 (netpolicy_service))
(typeattributeset net_radio_prop_27_0 (net_radio_prop))
(typeattributeset netstats_service_27_0 (netstats_service))
(typeattributeset netutils_wrapper_27_0 (netutils_wrapper))
(typeattributeset netutils_wrapper_exec_27_0 (netutils_wrapper_exec))
(typeattributeset network_management_service_27_0 (network_management_service))
(typeattributeset network_score_service_27_0 (network_score_service))
(typeattributeset network_time_update_service_27_0 (network_time_update_service))
(typeattributeset nfc_27_0 (nfc))
(typeattributeset nfc_data_file_27_0 (nfc_data_file))
(typeattributeset nfc_device_27_0 (nfc_device))
(typeattributeset nfc_prop_27_0 (nfc_prop))
(typeattributeset nfc_service_27_0 (nfc_service))
(typeattributeset node_27_0 (node))
(typeattributeset nonplat_service_contexts_file_27_0 (nonplat_service_contexts_file))
(typeattributeset notification_service_27_0 (notification_service))
(typeattributeset null_device_27_0 (null_device))
(typeattributeset oemfs_27_0 (oemfs))
(typeattributeset oem_lock_service_27_0 (oem_lock_service))
(typeattributeset ota_data_file_27_0 (ota_data_file))
(typeattributeset otadexopt_service_27_0 (otadexopt_service))
(typeattributeset ota_package_file_27_0 (ota_package_file))
(typeattributeset otapreopt_chroot_27_0 (otapreopt_chroot))
(typeattributeset otapreopt_chroot_exec_27_0 (otapreopt_chroot_exec))
(typeattributeset otapreopt_slot_27_0 (otapreopt_slot))
(typeattributeset otapreopt_slot_exec_27_0 (otapreopt_slot_exec))
(typeattributeset overlay_prop_27_0 (overlay_prop))
(typeattributeset overlay_service_27_0 (overlay_service))
(typeattributeset owntty_device_27_0 (owntty_device))
(typeattributeset package_native_service_27_0 (package_native_service))
(typeattributeset package_service_27_0 (package_service))
(typeattributeset pan_result_prop_27_0 (pan_result_prop))
(typeattributeset pdx_bufferhub_client_channel_socket_27_0 (pdx_bufferhub_client_channel_socket))
(typeattributeset pdx_bufferhub_client_endpoint_socket_27_0 (pdx_bufferhub_client_endpoint_socket))
(typeattributeset pdx_bufferhub_dir_27_0 (pdx_bufferhub_dir))
(typeattributeset pdx_display_client_channel_socket_27_0 (pdx_display_client_channel_socket))
(typeattributeset pdx_display_client_endpoint_socket_27_0 (pdx_display_client_endpoint_socket))
(typeattributeset pdx_display_dir_27_0 (pdx_display_dir))
(typeattributeset pdx_display_manager_channel_socket_27_0 (pdx_display_manager_channel_socket))
(typeattributeset pdx_display_manager_endpoint_socket_27_0 (pdx_display_manager_endpoint_socket))
(typeattributeset pdx_display_screenshot_channel_socket_27_0 (pdx_display_screenshot_channel_socket))
(typeattributeset pdx_display_screenshot_endpoint_socket_27_0 (pdx_display_screenshot_endpoint_socket))
(typeattributeset pdx_display_vsync_channel_socket_27_0 (pdx_display_vsync_channel_socket))
(typeattributeset pdx_display_vsync_endpoint_socket_27_0 (pdx_display_vsync_endpoint_socket))
(typeattributeset pdx_performance_client_channel_socket_27_0 (pdx_performance_client_channel_socket))
(typeattributeset pdx_performance_client_endpoint_socket_27_0 (pdx_performance_client_endpoint_socket))
(typeattributeset pdx_performance_dir_27_0 (pdx_performance_dir))
(typeattributeset performanced_27_0 (performanced))
(typeattributeset performanced_exec_27_0 (performanced_exec))
(typeattributeset perfprofd_27_0 (perfprofd))
(typeattributeset perfprofd_data_file_27_0 (perfprofd_data_file))
(typeattributeset perfprofd_exec_27_0 (perfprofd_exec))
(typeattributeset permission_service_27_0 (permission_service))
(typeattributeset persist_debug_prop_27_0 (persist_debug_prop))
(typeattributeset persistent_data_block_service_27_0 (persistent_data_block_service))
(typeattributeset persistent_properties_ready_prop_27_0 (persistent_properties_ready_prop))
(typeattributeset pinner_service_27_0 (pinner_service))
(typeattributeset pipefs_27_0 (pipefs))
(typeattributeset platform_app_27_0 (platform_app))
(typeattributeset pmsg_device_27_0 (pmsg_device))
(typeattributeset port_27_0 (port))
(typeattributeset port_device_27_0 (port_device))
(typeattributeset postinstall_27_0 (postinstall))
(typeattributeset postinstall_dexopt_27_0 (postinstall_dexopt))
(typeattributeset postinstall_file_27_0 (postinstall_file))
(typeattributeset postinstall_mnt_dir_27_0 (postinstall_mnt_dir))
(typeattributeset powerctl_prop_27_0 (powerctl_prop))
(typeattributeset power_service_27_0 (power_service))
(typeattributeset ppp_27_0 (ppp))
(typeattributeset ppp_device_27_0 (ppp_device))
(typeattributeset ppp_exec_27_0 (ppp_exec))
(typeattributeset preloads_data_file_27_0 (preloads_data_file))
(typeattributeset preloads_media_file_27_0 (preloads_media_file))
(typeattributeset preopt2cachename_27_0 (preopt2cachename))
(typeattributeset preopt2cachename_exec_27_0 (preopt2cachename_exec))
(typeattributeset print_service_27_0 (print_service))
(typeattributeset priv_app_27_0 (priv_app))
(typeattributeset proc_27_0
  ( proc
    proc_abi
    proc_asound
    proc_buddyinfo
    proc_cmdline
    proc_dirty
    proc_diskstats
    proc_extra_free_kbytes
    proc_filesystems
    proc_hostname
    proc_hung_task
    proc_kmsg
    proc_loadavg
    proc_max_map_count
    proc_min_free_order_shift
    proc_mounts
    proc_page_cluster
    proc_pagetypeinfo
    proc_panic
    proc_pid_max
    proc_pipe_conf
    proc_random
    proc_sched
    proc_swaps
    proc_uid_concurrent_active_time
    proc_uid_concurrent_policy_time
    proc_uid_cpupower
    proc_uptime
    proc_version
    proc_vmallocinfo
    proc_vmstat))
(typeattributeset proc_bluetooth_writable_27_0 (proc_bluetooth_writable))
(typeattributeset proc_cpuinfo_27_0 (proc_cpuinfo))
(typeattributeset proc_drop_caches_27_0 (proc_drop_caches))
(typeattributeset processinfo_service_27_0 (processinfo_service))
(typeattributeset proc_interrupts_27_0 (proc_interrupts))
(typeattributeset proc_iomem_27_0 (proc_iomem))
(typeattributeset proc_meminfo_27_0 (proc_meminfo))
(typeattributeset proc_misc_27_0 (proc_misc))
(typeattributeset proc_modules_27_0 (proc_modules))
(typeattributeset proc_net_27_0
  ( proc_net
    proc_qtaguid_stat))
(typeattributeset proc_overcommit_memory_27_0 (proc_overcommit_memory))
(typeattributeset proc_perf_27_0 (proc_perf))
(typeattributeset proc_security_27_0 (proc_security))
(typeattributeset proc_stat_27_0 (proc_stat))
(typeattributeset procstats_service_27_0 (procstats_service))
(typeattributeset proc_sysrq_27_0 (proc_sysrq))
(typeattributeset proc_timer_27_0 (proc_timer))
(typeattributeset proc_tty_drivers_27_0 (proc_tty_drivers))
(typeattributeset proc_uid_cputime_removeuid_27_0 (proc_uid_cputime_removeuid))
(typeattributeset proc_uid_cputime_showstat_27_0 (proc_uid_cputime_showstat))
(typeattributeset proc_uid_io_stats_27_0 (proc_uid_io_stats))
(typeattributeset proc_uid_procstat_set_27_0 (proc_uid_procstat_set))
(typeattributeset proc_uid_time_in_state_27_0 (proc_uid_time_in_state))
(typeattributeset proc_zoneinfo_27_0 (proc_zoneinfo))
(typeattributeset profman_27_0 (profman))
(typeattributeset profman_dump_data_file_27_0 (profman_dump_data_file))
(typeattributeset profman_exec_27_0 (profman_exec))
(typeattributeset properties_device_27_0 (properties_device))
(typeattributeset properties_serial_27_0 (properties_serial))
(typeattributeset property_contexts_file_27_0 (property_contexts_file))
(typeattributeset property_data_file_27_0 (property_data_file))
(typeattributeset property_socket_27_0 (property_socket))
(typeattributeset pstorefs_27_0 (pstorefs))
(typeattributeset ptmx_device_27_0 (ptmx_device))
(typeattributeset qtaguid_device_27_0 (qtaguid_device))
(typeattributeset qtaguid_proc_27_0 (qtaguid_proc))
(typeattributeset racoon_27_0 (racoon))
(typeattributeset racoon_exec_27_0 (racoon_exec))
(typeattributeset racoon_socket_27_0 (racoon_socket))
(typeattributeset radio_27_0 (radio))
(typeattributeset radio_data_file_27_0 (radio_data_file))
(typeattributeset radio_device_27_0 (radio_device))
(typeattributeset radio_prop_27_0 (radio_prop))
(typeattributeset radio_service_27_0 (radio_service))
(typeattributeset ram_device_27_0 (ram_device))
(typeattributeset random_device_27_0 (random_device))
(typeattributeset reboot_data_file_27_0 (reboot_data_file))
(typeattributeset recovery_27_0 (recovery))
(typeattributeset recovery_block_device_27_0 (recovery_block_device))
(typeattributeset recovery_data_file_27_0 (recovery_data_file))
(typeattributeset recovery_persist_27_0 (recovery_persist))
(typeattributeset recovery_persist_exec_27_0 (recovery_persist_exec))
(typeattributeset recovery_refresh_27_0 (recovery_refresh))
(typeattributeset recovery_refresh_exec_27_0 (recovery_refresh_exec))
(typeattributeset recovery_service_27_0 (recovery_service))
(typeattributeset registry_service_27_0 (registry_service))
(typeattributeset resourcecache_data_file_27_0 (resourcecache_data_file))
(typeattributeset restorecon_prop_27_0 (restorecon_prop))
(typeattributeset restrictions_service_27_0 (restrictions_service))
(typeattributeset rild_27_0 (rild))
(typeattributeset rild_debug_socket_27_0 (rild_debug_socket))
(typeattributeset rild_socket_27_0 (rild_socket))
(typeattributeset ringtone_file_27_0 (ringtone_file))
(typeattributeset root_block_device_27_0 (root_block_device))
(typeattributeset rootfs_27_0 (rootfs))
(typeattributeset rpmsg_device_27_0 (rpmsg_device))
(typeattributeset rtc_device_27_0 (rtc_device))
(typeattributeset rttmanager_service_27_0 (rttmanager_service))
(typeattributeset runas_27_0 (runas))
(typeattributeset runas_exec_27_0 (runas_exec))
(typeattributeset runtime_event_log_tags_file_27_0 (runtime_event_log_tags_file))
(typeattributeset safemode_prop_27_0 (safemode_prop))
(typeattributeset same_process_hal_file_27_0 (same_process_hal_file))
(typeattributeset samplingprofiler_service_27_0 (samplingprofiler_service))
(typeattributeset scheduling_policy_service_27_0 (scheduling_policy_service))
(typeattributeset sdcardd_27_0 (sdcardd))
(typeattributeset sdcardd_exec_27_0 (sdcardd_exec))
(typeattributeset sdcardfs_27_0 (sdcardfs))
(typeattributeset seapp_contexts_file_27_0 (seapp_contexts_file))
(typeattributeset search_service_27_0 (search_service))
(typeattributeset sec_key_att_app_id_provider_service_27_0 (sec_key_att_app_id_provider_service))
(typeattributeset selinuxfs_27_0 (selinuxfs))
(typeattributeset sensors_device_27_0 (sensors_device))
(typeattributeset sensorservice_service_27_0 (sensorservice_service))
(typeattributeset sepolicy_file_27_0 (sepolicy_file))
(typeattributeset serial_device_27_0 (serial_device))
(typeattributeset serialno_prop_27_0 (serialno_prop))
(typeattributeset serial_service_27_0 (serial_service))
(typeattributeset service_contexts_file_27_0 (service_contexts_file))
(typeattributeset servicediscovery_service_27_0 (servicediscovery_service))
(typeattributeset servicemanager_27_0 (servicemanager))
(typeattributeset servicemanager_exec_27_0 (servicemanager_exec))
(typeattributeset settings_service_27_0 (settings_service))
(typeattributeset sgdisk_27_0 (sgdisk))
(typeattributeset sgdisk_exec_27_0 (sgdisk_exec))
(typeattributeset shared_relro_27_0 (shared_relro))
(typeattributeset shared_relro_file_27_0 (shared_relro_file))
(typeattributeset shell_27_0 (shell))
(typeattributeset shell_data_file_27_0 (shell_data_file))
(typeattributeset shell_exec_27_0 (shell_exec))
(typeattributeset shell_prop_27_0 (shell_prop))
(typeattributeset shm_27_0 (shm))
(typeattributeset shortcut_manager_icons_27_0 (shortcut_manager_icons))
(typeattributeset shortcut_service_27_0 (shortcut_service))
(typeattributeset slideshow_27_0 (slideshow))
(typeattributeset socket_device_27_0 (socket_device))
(typeattributeset sockfs_27_0 (sockfs))
(typeattributeset statusbar_service_27_0 (statusbar_service))
(typeattributeset storaged_service_27_0 (storaged_service))
(typeattributeset storage_file_27_0 (storage_file))
(typeattributeset storagestats_service_27_0 (storagestats_service))
(typeattributeset storage_stub_file_27_0 (storage_stub_file))
(typeattributeset su_27_0 (su))
(typeattributeset su_exec_27_0 (su_exec))
(typeattributeset surfaceflinger_27_0 (surfaceflinger))
(typeattributeset surfaceflinger_service_27_0 (surfaceflinger_service))
(typeattributeset swap_block_device_27_0 (swap_block_device))
(typeattributeset sysfs_27_0
  ( sysfs
    sysfs_android_usb
    sysfs_dm
    sysfs_dt_firmware_android
    sysfs_ipv4
    sysfs_kernel_notes
    sysfs_net
    sysfs_power
    sysfs_rtc
    sysfs_switch
    sysfs_wakeup_reasons))
(typeattributeset sysfs_batteryinfo_27_0 (sysfs_batteryinfo))
(typeattributeset sysfs_bluetooth_writable_27_0 (sysfs_bluetooth_writable))
(typeattributeset sysfs_devices_system_cpu_27_0 (sysfs_devices_system_cpu))
(typeattributeset sysfs_fs_ext4_features_27_0 (sysfs_fs_ext4_features))
(typeattributeset sysfs_hwrandom_27_0 (sysfs_hwrandom))
(typeattributeset sysfs_leds_27_0 (sysfs_leds))
(typeattributeset sysfs_lowmemorykiller_27_0 (sysfs_lowmemorykiller))
(typeattributeset sysfs_mac_address_27_0 (sysfs_mac_address))
(typeattributeset sysfs_nfc_power_writable_27_0 (sysfs_nfc_power_writable))
(typeattributeset sysfs_thermal_27_0 (sysfs_thermal))
(typeattributeset sysfs_uio_27_0 (sysfs_uio))
(typeattributeset sysfs_usb_27_0 (sysfs_usb))
(typeattributeset sysfs_usermodehelper_27_0 (sysfs_usermodehelper))
(typeattributeset sysfs_vibrator_27_0 (sysfs_vibrator))
(typeattributeset sysfs_wake_lock_27_0 (sysfs_wake_lock))
(typeattributeset sysfs_wlan_fwpath_27_0 (sysfs_wlan_fwpath))
(typeattributeset sysfs_zram_27_0 (sysfs_zram))
(typeattributeset sysfs_zram_uevent_27_0 (sysfs_zram_uevent))
(typeattributeset system_app_27_0 (system_app))
(typeattributeset system_app_data_file_27_0 (system_app_data_file))
(typeattributeset system_app_service_27_0 (system_app_service))
(typeattributeset system_block_device_27_0 (system_block_device))
(typeattributeset system_data_file_27_0
  ( system_data_file
    vendor_data_file))
(typeattributeset system_file_27_0 (system_file))
(typeattributeset systemkeys_data_file_27_0 (systemkeys_data_file))
(typeattributeset system_ndebug_socket_27_0 (system_ndebug_socket))
(typeattributeset system_net_netd_hwservice_27_0 (system_net_netd_hwservice))
(typeattributeset system_prop_27_0 (system_prop))
(typeattributeset system_radio_prop_27_0 (system_radio_prop))
(typeattributeset system_server_27_0 (system_server))
(typeattributeset system_wifi_keystore_hwservice_27_0 (system_wifi_keystore_hwservice))
(typeattributeset system_wpa_socket_27_0 (system_wpa_socket))
(typeattributeset task_service_27_0 (task_service))
(typeattributeset tee_27_0 (tee))
(typeattributeset tee_data_file_27_0 (tee_data_file))
(typeattributeset tee_device_27_0 (tee_device))
(typeattributeset telecom_service_27_0 (telecom_service))
(typeattributeset textclassification_service_27_0 (textclassification_service))
(typeattributeset textclassifier_data_file_27_0 (textclassifier_data_file))
(typeattributeset textservices_service_27_0 (textservices_service))
(typeattributeset thermalcallback_hwservice_27_0 (thermalcallback_hwservice))
(typeattributeset thermal_service_27_0 (thermal_service))
(typeattributeset thermalserviced_27_0 (thermalserviced))
(typeattributeset thermalserviced_exec_27_0 (thermalserviced_exec))
(typeattributeset timezone_service_27_0 (timezone_service))
(typeattributeset tmpfs_27_0 (tmpfs))
(typeattributeset tombstoned_27_0 (tombstoned))
(typeattributeset tombstone_data_file_27_0 (tombstone_data_file))
(typeattributeset tombstoned_crash_socket_27_0 (tombstoned_crash_socket))
(typeattributeset tombstoned_exec_27_0 (tombstoned_exec))
(typeattributeset tombstoned_intercept_socket_27_0 (tombstoned_intercept_socket))
(typeattributeset tombstoned_java_trace_socket_27_0 (tombstoned_java_trace_socket))
(typeattributeset toolbox_27_0 (toolbox))
(typeattributeset toolbox_exec_27_0 (toolbox_exec))
(typeattributeset trust_service_27_0 (trust_service))
(typeattributeset tty_device_27_0 (tty_device))
(typeattributeset tun_device_27_0 (tun_device))
(typeattributeset tv_input_service_27_0 (tv_input_service))
(typeattributeset tzdatacheck_27_0 (tzdatacheck))
(typeattributeset tzdatacheck_exec_27_0 (tzdatacheck_exec))
(typeattributeset ueventd_27_0 (ueventd))
(typeattributeset uhid_device_27_0 (uhid_device))
(typeattributeset uimode_service_27_0 (uimode_service))
(typeattributeset uio_device_27_0 (uio_device))
(typeattributeset uncrypt_27_0 (uncrypt))
(typeattributeset uncrypt_exec_27_0 (uncrypt_exec))
(typeattributeset uncrypt_socket_27_0 (uncrypt_socket))
(typeattributeset unencrypted_data_file_27_0 (unencrypted_data_file))
(typeattributeset unlabeled_27_0 (unlabeled))
(typeattributeset untrusted_app_25_27_0 (untrusted_app_25))
(typeattributeset untrusted_app_27_0
  ( untrusted_app
    untrusted_app_27))
(typeattributeset untrusted_v2_app_27_0 (untrusted_v2_app))
(typeattributeset update_engine_27_0 (update_engine))
(typeattributeset update_engine_data_file_27_0 (update_engine_data_file))
(typeattributeset update_engine_exec_27_0 (update_engine_exec))
(typeattributeset update_engine_service_27_0 (update_engine_service))
(typeattributeset updatelock_service_27_0 (updatelock_service))
(typeattributeset update_verifier_27_0 (update_verifier))
(typeattributeset update_verifier_exec_27_0 (update_verifier_exec))
(typeattributeset usagestats_service_27_0 (usagestats_service))
(typeattributeset usbaccessory_device_27_0 (usbaccessory_device))
(typeattributeset usb_device_27_0 (usb_device))
(typeattributeset usbfs_27_0 (usbfs))
(typeattributeset usb_service_27_0 (usb_service))
(typeattributeset userdata_block_device_27_0 (userdata_block_device))
(typeattributeset usermodehelper_27_0 (usermodehelper))
(typeattributeset user_profile_data_file_27_0 (user_profile_data_file))
(typeattributeset user_service_27_0 (user_service))
(typeattributeset vcs_device_27_0 (vcs_device))
(typeattributeset vdc_27_0 (vdc))
(typeattributeset vdc_exec_27_0 (vdc_exec))
(typeattributeset vendor_app_file_27_0 (vendor_app_file))
(typeattributeset vendor_configs_file_27_0 (vendor_configs_file))
(typeattributeset vendor_file_27_0 (vendor_file))
(typeattributeset vendor_framework_file_27_0 (vendor_framework_file))
(typeattributeset vendor_hal_file_27_0 (vendor_hal_file))
(typeattributeset vendor_overlay_file_27_0 (vendor_overlay_file))
(typeattributeset vendor_shell_exec_27_0 (vendor_shell_exec))
(typeattributeset vendor_toolbox_exec_27_0 (vendor_toolbox_exec))
(typeattributeset vfat_27_0 (vfat))
(typeattributeset vibrator_service_27_0 (vibrator_service))
(typeattributeset video_device_27_0 (video_device))
(typeattributeset virtual_touchpad_27_0 (virtual_touchpad))
(typeattributeset virtual_touchpad_exec_27_0 (virtual_touchpad_exec))
(typeattributeset virtual_touchpad_service_27_0 (virtual_touchpad_service))
(typeattributeset vndbinder_device_27_0 (vndbinder_device))
(typeattributeset vndk_sp_file_27_0 (vndk_sp_file))
(typeattributeset vndservice_contexts_file_27_0 (vndservice_contexts_file))
(typeattributeset vndservicemanager_27_0 (vndservicemanager))
(typeattributeset voiceinteraction_service_27_0 (voiceinteraction_service))
(typeattributeset vold_27_0 (vold))
(typeattributeset vold_data_file_27_0 (vold_data_file))
(typeattributeset vold_device_27_0 (vold_device))
(typeattributeset vold_exec_27_0 (vold_exec))
(typeattributeset vold_prop_27_0 (vold_prop))
(typeattributeset vold_socket_27_0 (vold_socket))
(typeattributeset vpn_data_file_27_0 (vpn_data_file))
(typeattributeset vr_hwc_27_0 (vr_hwc))
(typeattributeset vr_hwc_exec_27_0 (vr_hwc_exec))
(typeattributeset vr_hwc_service_27_0 (vr_hwc_service))
(typeattributeset vr_manager_service_27_0 (vr_manager_service))
(typeattributeset wallpaper_file_27_0 (wallpaper_file))
(typeattributeset wallpaper_service_27_0 (wallpaper_service))
(typeattributeset watchdogd_27_0 (watchdogd))
(typeattributeset watchdog_device_27_0 (watchdog_device))
(typeattributeset webviewupdate_service_27_0 (webviewupdate_service))
(typeattributeset webview_zygote_27_0 (webview_zygote))
(typeattributeset webview_zygote_exec_27_0 (webview_zygote_exec))
(typeattributeset webview_zygote_socket_27_0 (webview_zygote_socket))
(typeattributeset wifiaware_service_27_0 (wifiaware_service))
(typeattributeset wificond_27_0 (wificond))
(typeattributeset wificond_exec_27_0 (wificond_exec))
(typeattributeset wificond_service_27_0 (wificond_service))
(typeattributeset wifi_data_file_27_0 (wifi_data_file))
(typeattributeset wifi_log_prop_27_0 (wifi_log_prop))
(typeattributeset wifip2p_service_27_0 (wifip2p_service))
(typeattributeset wifi_prop_27_0 (wifi_prop))
(typeattributeset wifiscanner_service_27_0 (wifiscanner_service))
(typeattributeset wifi_service_27_0 (wifi_service))
(typeattributeset window_service_27_0 (window_service))
(typeattributeset wpa_socket_27_0 (wpa_socket))
(typeattributeset zero_device_27_0 (zero_device))
(typeattributeset zoneinfo_data_file_27_0 (zoneinfo_data_file))
(typeattributeset zygote_27_0 (zygote))
(typeattributeset zygote_exec_27_0 (zygote_exec))
(typeattributeset zygote_socket_27_0 (zygote_socket))
