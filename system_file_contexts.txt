/system(/.*)? u:object_r:system_file:s0
/system/acct u:object_r:cgroup:s0
/system/bin u:object_r:rootfs:s0
/system/bugreports u:object_r:rootfs:s0
/system/cache u:object_r:cache_file:s0
/system/charger u:object_r:rootfs:s0
/system/config u:object_r:rootfs:s0
/system/d u:object_r:rootfs:s0
/system/data u:object_r:system_data_file:s0
/system/default.prop u:object_r:rootfs:s0
/system/dev u:object_r:device:s0
/system/etc u:object_r:rootfs:s0
/system/init u:object_r:init_exec:s0
/system/init.environ.rc u:object_r:rootfs:s0
/system/init.rc u:object_r:rootfs:s0
/system/init.usb.configfs.rc u:object_r:rootfs:s0
/system/init.usb.rc u:object_r:rootfs:s0
/system/init.zygote32.rc u:object_r:rootfs:s0
/system/mnt u:object_r:tmpfs:s0
/system/mnt/vendor u:object_r:mnt_vendor_file:s0
/system/mnt/vendor/metadata u:object_r:vendor_metadata:s0
/system/odm u:object_r:vendor_file:s0
/system/odm/app u:object_r:vendor_app_file:s0
/system/odm/bin u:object_r:vendor_file:s0
/system/odm/etc u:object_r:vendor_configs_file:s0
/system/odm/firmware u:object_r:vendor_file:s0
/system/odm/framework u:object_r:vendor_framework_file:s0
/system/odm/lib u:object_r:vendor_file:s0
/system/odm/lib64 u:object_r:vendor_file:s0
/system/odm/overlay u:object_r:vendor_overlay_file:s0
/system/odm/priv-app u:object_r:vendor_app_file:s0
/system/oem u:object_r:oemfs:s0
/system/proc u:object_r:rootfs:s0
/system/res u:object_r:rootfs:s0
/system/res/images u:object_r:rootfs:s0
/system/res/images/charger u:object_r:rootfs:s0
/system/res/images/charger/battery_fail.png u:object_r:rootfs:s0
/system/res/images/charger/battery_scale.png u:object_r:rootfs:s0
/system/res/images/charger/font.png u:object_r:rootfs:s0
/system/sbin u:object_r:rootfs:s0
/system/sbin/charger u:object_r:rootfs:s0
/system/sbin/mkdosfs u:object_r:rootfs:s0
/system/sbin/ueventd u:object_r:rootfs:s0
/system/sbin/watchdogd u:object_r:rootfs:s0
/system/sdcard u:object_r:rootfs:s0
/system/storage u:object_r:storage_file:s0
/system/sys u:object_r:sysfs:s0
/system/system/app/dangbei u:object_r:rootfs:s0
/system/system/app/dangbei/dangbei.apk u:object_r:rootfs:s0
/system/system/app/dangbei/lib u:object_r:rootfs:s0
/system/system/app/dangbei/lib/arm u:object_r:rootfs:s0
/system/system/app/dangbei/lib/arm/libbdpush_V2_7.so u:object_r:rootfs:s0
/system/system/app/dangbei/lib/arm/libcocklogic-1.1.3.so u:object_r:rootfs:s0
/system/system/app/dangbei/lib/arm/libeuthenia-lib.so u:object_r:rootfs:s0
/system/system/app/dangbei/lib/arm/libjcore117.so u:object_r:rootfs:s0
/system/system/app/dangbei/lib/arm/libtnet-3.1.11.so u:object_r:rootfs:s0
/system/system/app/dangbei/lib/arm64 u:object_r:rootfs:s0
/system/system/app/dangbei/lib/arm64/libbdpush_V2_7.so u:object_r:rootfs:s0
/system/system/app/dangbei/lib/arm64/libcocklogic-1.1.3.so u:object_r:rootfs:s0
/system/system/app/dangbei/lib/arm64/libeuthenia-lib.so u:object_r:rootfs:s0
/system/system/app/dangbei/lib/arm64/libjcore117.so u:object_r:rootfs:s0
/system/system/app/dangbei/lib/arm64/libtnet-3.1.11.so u:object_r:rootfs:s0
/system/system/app/korgu u:object_r:rootfs:s0
/system/system/app/korgu/korgutv.apk u:object_r:rootfs:s0
/system/system/app/korgu/lib u:object_r:rootfs:s0
/system/system/app/korgu/lib/arm u:object_r:rootfs:s0
/system/system/app/korgu/lib/arm/libapp.so u:object_r:rootfs:s0
/system/system/app/korgu/lib/arm/libflutter.so u:object_r:rootfs:s0
/system/system/app/korgu/lib/arm/libijkffmpeg.so u:object_r:rootfs:s0
/system/system/app/korgu/lib/arm/libijkplayer.so u:object_r:rootfs:s0
/system/system/app/korgu/lib/arm/libijksdl.so u:object_r:rootfs:s0
/system/system/bin/IptvService u:object_r:IptvService_exec:s0
/system/system/bin/adbd u:object_r:rootfs:s0
/system/system/bin/amtprox u:object_r:amtprox_exec:s0
/system/system/bin/app_process32 u:object_r:zygote_exec:s0
/system/system/bin/atrace u:object_r:atrace_exec:s0
/system/system/bin/audioserver u:object_r:audioserver_exec:s0
/system/system/bin/blank_screen u:object_r:blank_screen_exec:s0
/system/system/bin/blkid u:object_r:blkid_exec:s0
/system/system/bin/bootanimation u:object_r:bootanim_exec:s0
/system/system/bin/bootstat u:object_r:bootstat_exec:s0
/system/system/bin/bpfloader u:object_r:bpfloader_exec:s0
/system/system/bin/cameraserver u:object_r:cameraserver_exec:s0
/system/system/bin/clatd u:object_r:clatd_exec:s0
/system/system/bin/crash_dump32 u:object_r:crash_dump_exec:s0
/system/system/bin/dex2oat u:object_r:dex2oat_exec:s0
/system/system/bin/dex2oatd u:object_r:dex2oat_exec:s0
/system/system/bin/dexoptanalyzer u:object_r:dexoptanalyzer_exec:s0
/system/system/bin/dexoptanalyzerd u:object_r:dexoptanalyzer_exec:s0
/system/system/bin/dhclient u:object_r:dhcp_exec:s0
/system/system/bin/dnsmasq u:object_r:dnsmasq_exec:s0
/system/system/bin/drmserver u:object_r:drmserver_exec:s0
/system/system/bin/dumpstate u:object_r:dumpstate_exec:s0
/system/system/bin/e2fsck u:object_r:fsck_exec:s0
/system/system/bin/e2fsdroid u:object_r:e2fs_exec:s0
/system/system/bin/flash_img.sh u:object_r:install_recovery_exec:s0
/system/system/bin/fsck.f2fs u:object_r:fsck_exec:s0
/system/system/bin/fsck_msdos u:object_r:fsck_exec:s0
/system/system/bin/gatekeeperd u:object_r:gatekeeperd_exec:s0
/system/system/bin/healthd u:object_r:healthd_exec:s0
/system/system/bin/hw/android.hidl.allocator@1.0-service u:object_r:hal_allocator_default_exec:s0
/system/system/bin/hwservicemanager u:object_r:hwservicemanager_exec:s0
/system/system/bin/idmap u:object_r:idmap_exec:s0
/system/system/bin/incident u:object_r:incident_exec:s0
/system/system/bin/incident_helper u:object_r:incident_helper_exec:s0
/system/system/bin/incidentd u:object_r:incidentd_exec:s0
/system/system/bin/install-recovery.sh u:object_r:install_recovery_exec:s0
/system/system/bin/installd u:object_r:installd_exec:s0
/system/system/bin/iso u:object_r:iso_exec:s0
/system/system/bin/keystore u:object_r:keystore_exec:s0
/system/system/bin/lmkd u:object_r:lmkd_exec:s0
/system/system/bin/logcat u:object_r:logcat_exec:s0
/system/system/bin/logcatd u:object_r:logcat_exec:s0
/system/system/bin/logd u:object_r:logd_exec:s0
/system/system/bin/make_f2fs u:object_r:e2fs_exec:s0
/system/system/bin/mdnsd u:object_r:mdnsd_exec:s0
/system/system/bin/mediadrmserver u:object_r:mediadrmserver_exec:s0
/system/system/bin/mediaextractor u:object_r:mediaextractor_exec:s0
/system/system/bin/mediametrics u:object_r:mediametrics_exec:s0
/system/system/bin/mediaserver u:object_r:mediaserver_exec:s0
/system/system/bin/mke2fs u:object_r:e2fs_exec:s0
/system/system/bin/mkntfs u:object_r:vold_exec:s0
/system/system/bin/move_widevine_data.sh u:object_r:move-widevine-data-sh_exec:s0
/system/system/bin/mtpd u:object_r:mtp_exec:s0
/system/system/bin/net_ping_chk.sh u:object_r:dhcp_exec:s0
/system/system/bin/netd u:object_r:netd_exec:s0
/system/system/bin/ntfs-3g u:object_r:vold_exec:s0
/system/system/bin/ntfsfix u:object_r:vold_exec:s0
/system/system/bin/patchoat u:object_r:dex2oat_exec:s0
/system/system/bin/patchoatd u:object_r:dex2oat_exec:s0
/system/system/bin/perfetto u:object_r:perfetto_exec:s0
/system/system/bin/perfprofd u:object_r:perfprofd_exec:s0
/system/system/bin/pppd u:object_r:ppp_exec:s0
/system/system/bin/pppoe-connect u:object_r:dhcp_exec:s0
/system/system/bin/pppoe-setup u:object_r:dhcp_exec:s0
/system/system/bin/pppoe-start u:object_r:dhcp_exec:s0
/system/system/bin/pppoe-status u:object_r:dhcp_exec:s0
/system/system/bin/pppoe-stop u:object_r:dhcp_exec:s0
/system/system/bin/profman u:object_r:profman_exec:s0
/system/system/bin/profmand u:object_r:profman_exec:s0
/system/system/bin/racoon u:object_r:racoon_exec:s0
/system/system/bin/run-as u:object_r:runas_exec:s0
/system/system/bin/sdcard u:object_r:sdcardd_exec:s0
/system/system/bin/servicemanager u:object_r:servicemanager_exec:s0
/system/system/bin/set_default_launcher.sh u:object_r:dhcp_exec:s0
/system/system/bin/sgdisk u:object_r:sgdisk_exec:s0
/system/system/bin/sh u:object_r:shell_exec:s0
/system/system/bin/sload_f2fs u:object_r:e2fs_exec:s0
/system/system/bin/statsd u:object_r:statsd_exec:s0
/system/system/bin/stb_exec_cmd.sh u:object_r:dhcp_exec:s0
/system/system/bin/storaged u:object_r:storaged_exec:s0
/system/system/bin/surfaceflinger u:object_r:surfaceflinger_exec:s0
/system/system/bin/thermalserviced u:object_r:thermalserviced_exec:s0
/system/system/bin/tombstoned u:object_r:tombstoned_exec:s0
/system/system/bin/toolbox u:object_r:toolbox_exec:s0
/system/system/bin/toybox u:object_r:toolbox_exec:s0
/system/system/bin/tp_policy.sh u:object_r:dhcp_exec:s0
/system/system/bin/traced u:object_r:traced_exec:s0
/system/system/bin/traced_probes u:object_r:traced_probes_exec:s0
/system/system/bin/tune2fs u:object_r:fsck_exec:s0
/system/system/bin/tzdatacheck u:object_r:tzdatacheck_exec:s0
/system/system/bin/uncrypt u:object_r:uncrypt_exec:s0
/system/system/bin/usbd u:object_r:usbd_exec:s0
/system/system/bin/vdc u:object_r:vdc_exec:s0
/system/system/bin/vold u:object_r:vold_exec:s0
/system/system/bin/vold_prepare_subdirs u:object_r:vold_prepare_subdirs_exec:s0
/system/system/bin/wait_for_keymaster u:object_r:wait_for_keymaster_exec:s0
/system/system/bin/wificond u:object_r:wificond_exec:s0
/system/system/etc/selinux/mapping/26.0.cil u:object_r:sepolicy_file:s0
/system/system/etc/selinux/mapping/27.0.cil u:object_r:sepolicy_file:s0
/system/system/etc/selinux/mapping/28.0.cil u:object_r:sepolicy_file:s0
/system/system/etc/selinux/plat_and_mapping_sepolicy.cil.sha256 u:object_r:sepolicy_file:s0
/system/system/etc/selinux/plat_file_contexts u:object_r:file_contexts_file:s0
/system/system/etc/selinux/plat_hwservice_contexts u:object_r:hwservice_contexts_file:s0
/system/system/etc/selinux/plat_mac_permissions.xml u:object_r:mac_perms_file:s0
/system/system/etc/selinux/plat_property_contexts u:object_r:property_contexts_file:s0
/system/system/etc/selinux/plat_seapp_contexts u:object_r:seapp_contexts_file:s0
/system/system/etc/selinux/plat_sepolicy.cil u:object_r:sepolicy_file:s0
/system/system/etc/selinux/plat_service_contexts u:object_r:service_contexts_file:s0
/system/system/vendor u:object_r:vendor_file:s0
/system/system/xbin/su u:object_r:su_exec:s0
/system/ueventd.rc u:object_r:rootfs:s0
/system/vendor u:object_r:vendor_file:s0
